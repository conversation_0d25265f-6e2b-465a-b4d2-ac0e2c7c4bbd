import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError, shareReplay, take, map } from 'rxjs';
import { UrlBuilder } from '@gc/core/shared/infrastructure';
import { CodeModuleCommercial, UserModule } from '../../models';
import { SnackbarService } from '@gc/shared/ui';

@Injectable({
  providedIn: 'root',
})
export class UserModulesService {
  private http = inject(HttpClient);
  private readonly snackBarService = inject(SnackbarService);

  // Cache the modules observable to ensure the API call is only made once
  private modulesCache$: Observable<UserModule[]> | null = null;

  getUserModules(): Observable<UserModule[]> {
    if (this.modulesCache$) {
      return this.modulesCache$;
    }

    const urlBuilder = UrlBuilder.create('/legacy/v1')
      .withRouteParam('common')
      .withRouteParam('common-protection');

    this.modulesCache$ = this.http.get<UserModule[]>(urlBuilder.build()).pipe(
      take(1),
      catchError((error) => {
        this.snackBarService.failure({
          key: error.message,
        });
        return of([]);
      }),
      shareReplay(1)
    );

    return this.modulesCache$;
  }

  canAccessModule(moduleName: CodeModuleCommercial): Observable<boolean> {
    return this.getUserModules().pipe(
      map((modules) =>
        modules.some(
          (module: UserModule) => module.code === moduleName && module.isEnabled
        )
      )
    );
  }
}
