import { MonthlyTreatmentEditionsEnum } from '@gc/accounting/models';
import { monthlyTreatmentHistoryActions } from '../actions/monthly-treatment-history.actions';
import { MonthlyTreatmentHistoryState } from '../models/monthly-treatment-history-state.model';
import {
  initialState,
  monthlyTreatmentHistoryReducer,
} from './monthly-treatment-history.reducer';

describe('monthlyTreatmentHistoryReducer', () => {
  [
    {
      currentState: initialState,
      action: monthlyTreatmentHistoryActions.changeSelectedEnclosedMonth({
        month: new Date('2024-01-01'),
      }),
      expectedNewState: {
        ...initialState,
        selectedEnclosedMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'changeSelectedEnclosedMonth',
      givenTitle: 'given an initial state and a payload with any month',
      shouldTitle: 'should return new state with selectedEnclosedMonth updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentHistoryActions.changeSelectedEnclosedYear({
        year: 2022,
      }),
      expectedNewState: {
        ...initialState,
        selectedEnclosedYear: 2022,
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'changeSelectedEnclosedYear',
      givenTitle: 'given an initial state and a payload with any year',
      shouldTitle: 'should return new state with selectedEnclosedYear updated',
    },
    {
      currentState: {
        ...initialState,
        selectedEnclosedYear: 2021,
      } as MonthlyTreatmentHistoryState,
      action: monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears(),
      expectedNewState: {
        ...initialState,
        enclosedYearsLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedCompanyEnclosedYears',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with selectedEnclosedYear set to null and enclosedYearsLoadingStatus set to "IN_PROGRESS"',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2000, 2001],
        selectedEnclosedYear: 2001,
        enclosedYearsLoadingStatus: 'LOADED',
        enclosedMonthsOfSelectedYear: [
          new Date('2001-01-01'),
          new Date('2001-02-01'),
        ],
        selectedEnclosedMonth: new Date('2001-01-01'),
        enclosedMonthsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYearsSuccess({
          years: [2022, 2023],
        }),
      expectedNewState: {
        ...initialState,
        enclosedYears: [2022, 2023],
        selectedEnclosedYear: 2023,
        enclosedYearsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedCompanyEnclosedYearsSuccess',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with enclosedYears update, selectedEnclosedYear set with the latest year, enclosedYearsLoadingStatus and all data related to month reset',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action: monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonths(),
      expectedNewState: {
        ...initialState,
        enclosedMonthsLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedYearEnclosedMonths',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with enclosedMonthsLoadingStatus set to "IN_PROGRESS"',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonthsSuccess({
          months: [new Date('2001-01-01'), new Date('2001-02-01')],
        }),
      expectedNewState: {
        ...initialState,
        enclosedMonthsOfSelectedYear: [
          new Date('2001-01-01'),
          new Date('2001-02-01'),
        ],
        selectedEnclosedMonth: new Date('2001-02-01'),
        enclosedMonthsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedYearEnclosedMonthsSuccess',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with enclosedMonthsOfSelectedYear updated , selectedEnclosedMonth set with latest month and enclosedMonthsLoadingStatus set to "LOADED"',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions(),
      expectedNewState: {
        ...initialState,
        availableMonthlyEditionsLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedMonthAvailableEditions',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with availableMonthlyEditionsLoadingStatus set to "IN_PROGRESS"',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditionsSuccess(
          {
            availableMonthlyEditions: [
              {
                id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
                isAvailable: true,
              },
              {
                id: MonthlyTreatmentEditionsEnum.DEBT,
                isAvailable: false,
              },
              {
                id: MonthlyTreatmentEditionsEnum.DETAILS,
                isAvailable: false,
              },
              {
                id: MonthlyTreatmentEditionsEnum.RECEIPTS,
                isAvailable: true,
              },
              {
                id: MonthlyTreatmentEditionsEnum.SALES,
                isAvailable: false,
              },
            ],
          }
        ),
      expectedNewState: {
        ...initialState,
        availableMonthlyEditions: [
          {
            id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
            isAvailable: true,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DEBT,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DETAILS,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.RECEIPTS,
            isAvailable: true,
          },
          {
            id: MonthlyTreatmentEditionsEnum.SALES,
            isAvailable: false,
          },
        ],
        selectedMonthlyEditions: [
          MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          MonthlyTreatmentEditionsEnum.RECEIPTS,
        ],
        availableMonthlyEditionsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'loadSelectedMonthAvailableEditionsSuccess',
      givenTitle: 'given any state',
      shouldTitle:
        'should return new state with availableMonthlyEditionsLoadingStatus set to "LOADED", availableMonthlyEditions updated and selectedMonthlyEditions containing all available editions by default',
    },
    {
      currentState: {
        ...initialState,
        selectedMonthlyEditions: [
          MonthlyTreatmentEditionsEnum.SALES,
          MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
        ],
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.toggleMonthlyTreatmentEditionSelection({
          edition: MonthlyTreatmentEditionsEnum.SALES,
        }),
      expectedNewState: {
        ...initialState,
        selectedMonthlyEditions: [MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY],
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'toggleMonthlyTreatmentEditionSelection',
      givenTitle:
        'given state with selectedMonthlyEditions containing MonthlyTreatmentEditionsEnum.SALES and MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY',
      shouldTitle:
        'should return new state with selectedMonthlyEditions containing only MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY',
    },
    {
      currentState: {
        ...initialState,
        selectedMonthlyEditions: [MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY],
      } as MonthlyTreatmentHistoryState,
      action:
        monthlyTreatmentHistoryActions.toggleMonthlyTreatmentEditionSelection({
          edition: MonthlyTreatmentEditionsEnum.SALES,
        }),
      expectedNewState: {
        ...initialState,
        selectedMonthlyEditions: [
          MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          MonthlyTreatmentEditionsEnum.SALES,
        ],
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'toggleMonthlyTreatmentEditionSelection',
      givenTitle:
        'given state with selectedMonthlyEditions containing only MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY',
      shouldTitle:
        'should return new state with selectedMonthlyEditions containing MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY and MonthlyTreatmentEditionsEnum.SALES',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action: monthlyTreatmentHistoryActions.uncloseSelectedMonth(),
      expectedNewState: {
        ...initialState,
        uncloseSelectedMonthStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'uncloseSelectedMonth',
      givenTitle: 'given any state',
      shouldTitle: 'should set uncloseSelectedMonthStatus to "IN_PROGRESS"',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action: monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess(),
      expectedNewState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'uncloseSelectedMonthSuccess',
      givenTitle: 'given any state',
      shouldTitle: 'should reset to initial state',
    },
    {
      currentState: {
        ...initialState,
      } as MonthlyTreatmentHistoryState,
      action: monthlyTreatmentHistoryActions.uncloseSelectedMonthError(),
      expectedNewState: {
        ...initialState,
        uncloseSelectedMonthStatus: 'NOT_PROCESSED',
      } as MonthlyTreatmentHistoryState,
      actionTitle: 'uncloseSelectedMonthError',
      givenTitle: 'given any state',
      shouldTitle: 'should set uncloseSelectedMonthStatus to "NOT_PROCESSED"',
    },
  ].forEach(
    ({
      currentState,
      action,
      expectedNewState,
      actionTitle,
      givenTitle,
      shouldTitle,
    }) => {
      describe(`${actionTitle} action handling`, () => {
        describe(`${givenTitle}`, () => {
          it(`${shouldTitle}`, () => {
            const newState = monthlyTreatmentHistoryReducer(
              currentState,
              action
            );

            expect(newState).toStrictEqual(expectedNewState);
          });
        });
      });
    }
  );
});
