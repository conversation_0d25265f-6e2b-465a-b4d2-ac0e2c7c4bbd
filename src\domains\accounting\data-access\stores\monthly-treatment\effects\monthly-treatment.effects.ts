import { Injectable, inject } from '@angular/core';
import {
  EnclosingMonthInformations,
  MonthlyTreatmentEditionsFiltersModel,
} from '@gc/accounting/models';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { catchError, filter, map, switchMap } from 'rxjs/operators';
import { MonthlyTreatmentService } from '../../../services/monthly-treatment.service';
import { monthlyTreatmentActions } from '../actions/monthly-treatment.actions';
import { selectMonthlyTreatmentState } from '../selectors/monthly-treatment.selectors';

@Injectable()
export class MonthlyTreatmentEffects {
  private readonly _actions$ = inject(Actions);
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);
  private readonly _store = inject(Store);

  loadDefaultFilters$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentActions.loadDefaultFilters),
      switchMap(() => this._monthlyTreatmentService.getDefaultFilters()),
      map((filters: MonthlyTreatmentEditionsFiltersModel) =>
        monthlyTreatmentActions.loadDefaultFiltersSuccess({ filters })
      ),
      catchError(() => of(monthlyTreatmentActions.loadDefaultFiltersError()))
    );
  });

  loadEnclosureMonth$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentActions.loadEnclosureMonth),
      switchMap(({ companyId }) =>
        this._monthlyTreatmentService.getAccountingDefaultMonth(companyId)
      ),
      map((enclosingMonthInformations: EnclosingMonthInformations | null) =>
        monthlyTreatmentActions.loadEnclosureMonthSuccess({
          enclosingMonthInformations,
        })
      ),
      catchError(() => of(monthlyTreatmentActions.loadEnclosureMonthError()))
    );
  });

  encloseMonth$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentActions.encloseMonth),
      concatLatestFrom(() => this._store.select(selectMonthlyTreatmentState)),
      switchMap(([_action, state]) => {
        const monthlyFilters: MonthlyTreatmentEditionsFiltersModel = {
          salesDetailOrder: state.salesDetailsCurrentFilter,
          productCategories: state.salesByProductCurrentFilter,
        };
        return this._monthlyTreatmentService.saveFilters(monthlyFilters).pipe(
          switchMap((_) =>
            this._monthlyTreatmentService.encloseMonth({
              companyId: state.companyId!,
              month: state.enclosureMonth!,
            })
          ),
          map((_) => {
            return monthlyTreatmentActions.encloseMonthSuccess();
          }),
          catchError(() => of(monthlyTreatmentActions.encloseMonthError()))
        );
      })
    );
  });

  loadHasEmptyEdition$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentActions.loadHasEmptyEdition),
      concatLatestFrom(() => this._store.select(selectMonthlyTreatmentState)),
      filter(([_, { companyId, range }]) => !!companyId && !!range),
      switchMap(([_, { companyId, range }]) => {
        return this._monthlyTreatmentService.checkEmptyData(
          companyId!,
          range!.start,
          range!.end
        );
      }),
      map((isEmpty) => {
        return monthlyTreatmentActions.loadHasEmptyEditionsSuccess({
          isEmpty,
        });
      }),
      catchError(() => of(monthlyTreatmentActions.loadHasEmptyEditionsError()))
    );
  });

  encloseDefaultMonth$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentActions.encloseDefaultMonth),
      concatLatestFrom(() => this._store.select(selectMonthlyTreatmentState)),
      filter(
        ([{ defaultMonth }, { companyId }]) => !!companyId && !!defaultMonth
      ),
      switchMap(([{ defaultMonth }, { companyId }]) => {
        return this._monthlyTreatmentService
          .saveDefaultEncloseMonth({
            companyId: companyId!,
            month: defaultMonth!,
          })
          .pipe(
            map(() =>
              monthlyTreatmentActions.encloseDefaultMonthSuccess({
                defaultMonth,
              })
            ),
            catchError(() =>
              of(monthlyTreatmentActions.encloseDefaultMonthError())
            )
          );
      })
    );
  });
}
