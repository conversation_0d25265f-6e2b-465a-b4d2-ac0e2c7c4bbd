import { SelectionModel } from '@angular/cdk/collections';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  output,
  signal,
} from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  ActionContainerComponent,
  AUTO_SIZES_DIALOG_CONFIG,
  DialogService,
  LoaderComponent,
} from '@gc/shared/ui';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { Commission } from '@gc/core/sales/commissions/domains/models';
import { MatIconModule } from '@angular/material/icon';
import { TranslocoModule } from '@jsverse/transloco';
import { PayoutTablePreviewComponent } from './payout-table-actions/payout-table-preview/payout-table-preview.component';
import { PayoutTableFormComponent } from './payout-table-actions/payout-table-form/payout-table-form.component';
import { AmountWithCurrencyPipe } from '../../../../../domains/currency/ui/pipes/amount-with-currency/amount-with-currency.pipe';
import { CurrencyService } from '@gc/currency/data-access';
import { PushPipe } from '@ngrx/component';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';

export interface commissionInfo {
  documentId: string;
  documentDate: string;
  amount: number;
  amountToBePaid: number;
  status: number;
  position: number;
}

export interface commissionInfo {
  documentId: string;
  documentDate: string;
  amount: number;
  amountToBePaid: number;
  status: number;
  position: number;
}

@Component({
  selector: 'gc-payout-table',
  standalone: true,
  templateUrl: './payout-table.component.html',
  styleUrl: './payout-table.component.scss',
  imports: [
    MatTableModule,
    MatCheckboxModule,
    MatProgressBarModule,
    ActionContainerComponent,
    MatIconModule,
    TranslocoModule,
    AmountWithCurrencyPipe,
    TranslocoDatePipe,
    PushPipe,
    LoaderComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PayoutTableComponent {
  private readonly currencyService = inject(CurrencyService);
  private readonly commissionFacade = inject(CommissionsFacade);
  private readonly dialogService = inject(DialogService);
  private readonly currentData = signal<Commission[]>([]);
  selectedCommissionsChange = output<number | null>();
  currency$ = this.currencyService.getDefaultCurrency$();
  defaultCurrencyLoadingStatus$ =
    this.currencyService.getDefaultCurrencyLoadingStatus$();

  readonly totalCommissions = computed(() =>
    Number(
      this.currentData()
        .reduce((sum, commission) => sum + commission.amount, 0)
        .toFixed(2)
    )
  );

  readonly totalCommissionsToBePaid = computed(() =>
    Number(
      this.currentData()
        .reduce((sum, commission) => sum + commission.amountToBePaid, 0)
        .toFixed(2)
    )
  );

  displayedColumns: string[] = [
    'select',
    'documentId',
    'documentDate',
    'amount',
    'amountToBePaid',
    'status',
    'actions',
  ];
  dataCommission = this.commissionFacade.getCommissionsForCurrentFilters();
  dataSource = new MatTableDataSource<Commission>();
  selection = new SelectionModel<Commission>(true, []);

  constructor() {
    effect(() => {
      const commissionData = this.dataCommission();
      setTimeout(() => {
        this.updateCommissions(commissionData?.data ?? []);
      });
    });
  }

  onPreviewClick(event: MouseEvent, commission: Commission): void {
    event.stopPropagation();
    this.dialogService.open<PayoutTablePreviewComponent>(
      PayoutTablePreviewComponent,
      { commission },
      AUTO_SIZES_DIALOG_CONFIG
    );
  }

  onEditClick(event: MouseEvent, commission: Commission): void {
    event.stopPropagation();
    this.dialogService.open<PayoutTableFormComponent>(
      PayoutTableFormComponent,
      { commission },
      AUTO_SIZES_DIALOG_CONFIG
    );
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  checkboxLabel(row?: commissionInfo): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.position + 1}`;
  }

  toggleAllRows() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.selection.select(...this.dataSource.data);
    this.updateSelectionAndEmit();
  }

  onRowSelection(row: Commission): void {
    this.selection.toggle(row);
    this.updateSelectionAndEmit();
  }

  private updateCommissions(data: Commission[]): void {
    this.dataSource.data = data;
    this.currentData.set(data);
  }

  private updateSelectionAndEmit(): void {
    if (this.selection.selected.length === 0) {
      this.selectedCommissionsChange.emit(null);
    } else {
      const totalAmountToBePaid = this.selection.selected.reduce(
        (sum, commission) => sum + commission.amountToBePaid,
        0
      );
      this.selectedCommissionsChange.emit(totalAmountToBePaid);
    }
  }
}
