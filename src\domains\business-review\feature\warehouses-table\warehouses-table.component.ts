import { SelectionModel } from '@angular/cdk/collections';
import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  inject,
} from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { BusinessReviewService } from '@gc/business-review/data-access';
import { LoaderComponent } from '@gc/shared/ui';
import { Warehouse } from '@gc/warehouse/models';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { LetDirective, PushPipe } from '@ngrx/component';
import { BehaviorSubject, Observable, filter, map, tap } from 'rxjs';
import { BusinessReviewFormService } from '../services/business-review-form.service';

@Component({
  selector: 'gc-warehouses-table',
  standalone: true,
  imports: [
    MatTableModule,
    MatCheckboxModule,
    TranslocoDirective,
    PushPipe,
    LetDirective,
    AsyncPipe,
    LoaderComponent,
  ],
  templateUrl: './warehouses-table.component.html',
  styleUrls: [
    './warehouses-table.component.scss',
    './warehouses-table-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WarehousesTableComponent implements OnChanges {
  private readonly _translocoService = inject(TranslocoService);
  private readonly _businessReviewService = inject(BusinessReviewService);
  private readonly _businessReviewFormService = inject(
    BusinessReviewFormService
  );

  @Input() selectedIds: string[] | undefined = undefined;

  @Output() selectionChange = new EventEmitter<string[]>();

  displayedColumns: string[] = ['select', 'code', 'label'];

  dataSource!: MatTableDataSource<Warehouse>;

  selection: SelectionModel<Warehouse> = new SelectionModel<Warehouse>(
    true,
    []
  );

  showAll$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  warehouses$: Observable<MatTableDataSource<Warehouse>> =
    this._businessReviewService.warehouses$$.pipe(
      filter(Boolean),
      map((warehouses) => new MatTableDataSource<Warehouse>(warehouses)),
      tap((datasource) => this._initNewDataSource(datasource))
    );

  isLoading$ = this._businessReviewFormService.isLoadingAdvancedSettingsData$$;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedIds'].currentValue && this.dataSource?.data) {
      this._updateSelectionToMatchSelectedIdsInputValue();
    }
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numDisplayedRows = this.dataSource.filteredData.length;
    return numSelected === numDisplayedRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.dataSource.filteredData);
    }
    this._emitCurrentSelection();
  }

  /** The aria label for the checkbox on the passed row */
  checkboxAriaLabel(row?: Warehouse): string {
    if (!row) {
      return `${
        this.isAllSelected()
          ? this._translocoService.translate(
              'businessReview.advanced-settings-tab.warehouses.table.checkbox-aria-label.deselect-all'
            )
          : this._translocoService.translate(
              'businessReview.advanced-settings-tab.warehouses.table.checkbox-aria-label.select-all'
            )
      }`;
    }
    return `${
      this.selection.isSelected(row)
        ? this._translocoService.translate(
            'businessReview.advanced-settings-tab.warehouses.table.checkbox-aria-label.deselect',
            { value: row.code }
          )
        : this._translocoService.translate(
            'businessReview.advanced-settings-tab.warehouses.table.checkbox-aria-label.select',
            { value: row.code }
          )
    }`;
  }

  onShowAllCheckboxChange(): void {
    this.showAll$$.next(!this.showAll$$.value);
    this._applyFilter();
    this._deselectNotDisplayedRows();
  }

  onRowSelectionChange(event: MouseEvent, _warehouse: Warehouse): void {
    event.stopPropagation();
    this._emitCurrentSelection();
  }

  private _emitCurrentSelection(): void {
    this.selectionChange.emit(
      this.selection.selected.map((warehouse) => warehouse.id)
    );
  }

  private _initNewDataSource(dataSource: MatTableDataSource<Warehouse>): void {
    this.dataSource = dataSource;
    this._updateSelectionToMatchSelectedIdsInputValue();

    // Check the 'showAll' checkbox if at least one warehouse in selection is not visible or unusable.
    this.showAll$$.next(
      this.selection.selected.some(
        (warehouse) => !warehouse.visible || warehouse.unusable
      )
    );
    this._applyFilter();
  }

  private _applyFilter(): void {
    this.dataSource.filterPredicate = (data: Warehouse): boolean => {
      return this.showAll$$.value ? true : data.visible && !data.unusable;
    };
    this.dataSource.filter = '' + this.showAll$$.value; // Cette ligne est nécessaire pour déclencher le filtre.
  }

  private _deselectNotDisplayedRows(): void {
    const rowsToDeselect = this.selection.selected.filter(
      (warehouse) => !this.dataSource.filteredData.includes(warehouse)
    );

    if (rowsToDeselect.length > 0) {
      this.selection.deselect(...rowsToDeselect);
      this._emitCurrentSelection();
    }
  }

  private _updateSelectionToMatchSelectedIdsInputValue(): void {
    this.selection.clear();
    this.selection.select(
      ...this.dataSource.data.filter((warehouse) =>
        this.selectedIds?.includes(warehouse.id)
      )
    );
  }
}
