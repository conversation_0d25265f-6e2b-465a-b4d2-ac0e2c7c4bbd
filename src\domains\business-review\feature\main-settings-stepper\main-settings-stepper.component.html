<mat-stepper
  linear
  (selectedIndexChange)="isLastStepChange.emit($event === 2)"
  *transloco="let t">
  <mat-step
    [label]="t('businessReview.main-settings-tab.steps.company')"
    [stepControl]="companiesFC">
    <gc-companies-step />

    <div
      data-testid="actions-steps-company"
      class="mt-28 text-right"
      *ngrxLet="isLoading$ as isLoading">
      <button
        matStepperNext
        mat-raised-button
        color="primary"
        [disabled]="companiesFC.invalid || isLoading">
        @if (isLoading) {
          <gc-loader />
        } @else {
          {{ t('businessReview.main-settings-tab.steps.next') }}
        }
      </button>
    </div>
  </mat-step>
  <mat-step
    [label]="t('businessReview.main-settings-tab.steps.periodicity')"
    [stepControl]="periodicityFG">
    <gc-periodicity-step />

    <div class="text-right">
      <button matStepperPrevious mat-stroked-button color="primary">
        {{ t('businessReview.main-settings-tab.steps.previous') }}
      </button>

      <button
        matStepperNext
        mat-raised-button
        color="primary"
        [disabled]="periodicityFG.invalid">
        {{ t('businessReview.main-settings-tab.steps.complete') }}
      </button>
    </div>
  </mat-step>
  <mat-step [label]="t('businessReview.main-settings-tab.steps.summary')">
    <gc-summary-step />

    <div data-testid="actions-steps-summary" class="text-right">
      <button matStepperPrevious mat-stroked-button color="primary">
        {{ t('businessReview.main-settings-tab.steps.previous') }}
      </button>
    </div>
  </mat-step>
</mat-stepper>
