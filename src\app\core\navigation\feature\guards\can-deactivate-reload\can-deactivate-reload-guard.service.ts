import { Injectable, inject } from '@angular/core';
import { LockableComponent } from '@gc/core/navigation/models';
import { DialogService } from '@gc/shared/ui';
import { TranslocoService } from '@jsverse/transloco';
import { Observable, of, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CanDeactivateReloadGuard {
  private readonly _dialogService = inject(DialogService);
  private readonly _translocoService = inject(TranslocoService);

  canDeactivate(component: LockableComponent): Observable<boolean> {
    if (!component.canDeactivate || component.canDeactivate()) return of(true);

    return this._translocoService
      .selectTranslate(
        'default-messages.confirm-loss-unsaved-changes',
        {},
        'sharedDialog'
      )
      .pipe(
        switchMap((confirmMessage: string) => {
          return this._dialogService.confirm(confirmMessage);
        })
      );
  }
}
