<div
  data-testid="app-menu-container"
  class="container"
  *transloco="let t; read: 'menu'">
  @if (deviceAccessService.allowedFor(ALLOWED_DEVICES.dunning)) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.dunning">
      {{ t('dunning') }}
    </a>
  }
  @if (deviceAccessService.allowedFor(ALLOWED_DEVICES.reports)) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.reports">
      {{ t('reports') }}
    </a>
  }
  @if (deviceAccessService.allowedFor(ALLOWED_DEVICES.codeParameters)) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.codeParameters">
      {{ t('code-parameters') }}
    </a>
  }
  @if (deviceAccessService.allowedFor(ALLOWED_DEVICES.accounting)) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.accounting">
      {{ t('accounting') }}
    </a>
  }

  @if (
    (navigationRightsService.canAccessBusinessReview() | ngrxPush) &&
    deviceAccessService.allowedFor(ALLOWED_DEVICES.businessReview)
  ) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.businessReview">
      {{ t('business-review') }}
    </a>
  }

  @if (
    userModulesService.canAccessModule(CodeModuleCommercial.Deb) &&
    deviceAccessService.allowedFor(ALLOWED_DEVICES.deb)
  ) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.deb">
      {{ t('deb') }}
    </a>
  }

  @if (
    flagService.canAccessFeature('salesCommission') &&
    deviceAccessService.allowedFor(ALLOWED_DEVICES.salesCommission)
  ) {
    <a class="menu-item" [routerLink]="'/' + urlPaths.salesCommission">
      {{ t('sales-commissions') }}
    </a>
  }
</div>
