@use '@angular/material' as mat;
@use 'gc-material-typography' as gc-material-typography;

.all-fields-required-message {
  @include mat.m2-typography-level(
    gc-material-typography.$typography,
    'caption'
  );
}

.title-container {
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 10px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 0 1rem;
  width: 100%;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(33, 150, 243, 10%);
  border-radius: 4px;
  border-left: 4px solid #1976d2; // Blue 700 color

  mat-icon {
    color: #1976d2; // Blue 700 color
  }

  span {
    @include mat.m2-typography-level(
      gc-material-typography.$typography,
      'body-2'
    );
  }
}
