<ng-container>
  <div
    mat-dialog-title
    class="title-container"
    *transloco="let t; read: 'deb.deb-line-preview'">
    <mat-icon [color]="'primary'"> visibility </mat-icon>
    <span>
      {{ t('title') }}
    </span>
  </div>
  <mat-dialog-content class="content-container">
    <gc-deb-declaration-details
      [lineDetails]="details().data!"
      [isDetailsLoading]="details().isLoading!"
      [debSummaryInfo]="debSummaryInfo" />
    <div *transloco="let t" class="action-button-container">
      <button
        mat-stroked-button
        color="primary"
        type="button"
        cdkFocusInitial
        mat-dialog-close>
        {{ t('sharedAction.close') }}
      </button>
    </div>
  </mat-dialog-content>
</ng-container>
