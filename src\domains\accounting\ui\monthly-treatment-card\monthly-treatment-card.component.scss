.card-edition {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    border-width: 1px;
    border-style: solid;
    padding: 10px 14px 14px;

    .header {
        display: flex;
        align-items: center;
        width: 100%;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        padding-bottom: 10px;
        margin-bottom: 10px;
    }

    .body:not(:empty) {
        width: 100%;
        padding: 10px 20px;
    }

    .actions-container {
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}
