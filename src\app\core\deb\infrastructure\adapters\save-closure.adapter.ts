import { inject, Injectable } from '@angular/core';
import { SaveClosurePort } from '@gc/core/deb/domains/ports';
import { MonthToClose } from '../../domains/models';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { DebClosureParamsRequest } from '@gc/core/deb/infrastructure/api/request';

@Injectable()
export class SaveClosureAdapter implements SaveClosurePort {
  private readonly api = inject(DebApiService);

  for(monthToClose: MonthToClose) {
    const closureParams: DebClosureParamsRequest = {
      companyId: monthToClose.companyId,
      declarationMonth: monthToClose.month,
      invoicesIncluded: monthToClose.invoicesIncluded,
      deliveryNotesIncluded: monthToClose.deliveryNotesIncluded,
      archiveOnly: monthToClose.archiveOnly,
      lightItems: monthToClose.declarations,
    };

    return this.api.saveClosure(closureParams);
  }
}
