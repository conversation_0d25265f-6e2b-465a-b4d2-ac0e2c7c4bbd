import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebParameters } from '@gc/core/deb/domains/models';

@Component({
  selector: 'gc-deb-parameters',
  standalone: true,
  styleUrl: './deb-parameters.component.scss',
  imports: [MatCardModule, TranslocoDirective],
  templateUrl: './deb-parameters.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebParametersComponent {
  parameters = input<DebParameters>();
}
