import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { AppInsightActivatorService } from './app-insight-activator.service';
import { IAuthentication } from '@isagri-ng/security';
import { TokenManagerService } from '@gc/shared/api/authentication/data-access';
import { Observable, of } from 'rxjs';
import {
  ClientContext,
  ClientContextDataProviderStub,
  IClientContextDataProvider,
  UserContext,
} from '@isagri-ng/core/configuration';
import {
  AppInsightsNotificationTraceLevel,
  ApplicationInsightsParameter,
  MonitoringContext,
} from '@isagri-ng/monitoring';
import { waitForAsync } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';

const IsagriNgAuthenticationStub: Partial<IAuthentication> = {
  get onLogin$() {
    return of(true);
  },
};
describe('AppInsightActivatorService', () => {
  let spectator: SpectatorService<AppInsightActivatorService>;
  let service: AppInsightActivatorService;

  const createService = createServiceFactory({
    service: AppInsightActivatorService,
    mocks: [HttpClient, TokenManagerService],
    providers: [
      { provide: IAuthentication, useValue: IsagriNgAuthenticationStub },
      {
        provide: IClientContextDataProvider,
        useClass: ClientContextDataProviderStub,
      },
    ],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  describe('getMonitoringContext', () => {
    let getClientContextSpy: jest.SpyInstance<ClientContext>;
    let fromClientContextSpy: jest.SpyInstance<MonitoringContext>;

    const fakeMonitoringContext: MonitoringContext = {
      clientId: '798899659',
      domainId: 65455,
      radicalApplication: 'GC',
    };

    beforeEach(() => {
      const authenticationService = spectator.inject(IAuthentication);
      jest
        .spyOn(authenticationService, 'onLogin$', 'get')
        .mockReturnValueOnce(of(true));

      const clientContextService = spectator.inject(IClientContextDataProvider);
      getClientContextSpy = jest.spyOn(
        clientContextService,
        'getClientContext'
      );
      getClientContextSpy.mockReturnValueOnce({} as ClientContext);

      fromClientContextSpy = jest.spyOn(service, 'fromClientContext');
      fromClientContextSpy.mockReturnValueOnce(fakeMonitoringContext);
    });

    it('should call getClientContext method, and fromClientContext and return a MonitoringContext', waitForAsync(() => {
      service
        .getMonitoringContext()
        .subscribe((monitorContext: MonitoringContext) => {
          expect(getClientContextSpy).toHaveBeenCalled();
          expect(fromClientContextSpy).toHaveBeenCalled();
          expect(monitorContext).toStrictEqual(fakeMonitoringContext);
        });
    }));
  });

  describe('getApplicationInsightsParameter', () => {
    let getBearerTokenWhenAuthenticatedSpy: jest.SpyInstance<
      Observable<string>
    >;
    const fakeBearer = 'Bearer 54649494964694';

    let getMonitoringParametersSpy: jest.SpyInstance<
      Observable<ApplicationInsightsParameter>
    >;
    const fakeAppInsightKey = '644464646';
    const fakeAppInsightNotificationLevel =
      AppInsightsNotificationTraceLevel.standard;

    beforeEach(() => {
      const tokenManagerService = spectator.inject(TokenManagerService);
      getBearerTokenWhenAuthenticatedSpy = jest.spyOn(
        tokenManagerService,
        'getBearerTokenWhenAuthenticated'
      );
      getBearerTokenWhenAuthenticatedSpy.mockReturnValueOnce(of(fakeBearer));

      const httpClient = spectator.inject(HttpClient);
      getMonitoringParametersSpy = jest.spyOn(
        httpClient,
        'get'
      ) as unknown as jest.SpyInstance<
        Observable<ApplicationInsightsParameter>
      >;
      getMonitoringParametersSpy.mockReturnValueOnce(
        of({
          key: fakeAppInsightKey,
          notificationLevel: fakeAppInsightNotificationLevel,
          connectionString: null,
        })
      );
    });

    it('should call get method of HttpClient with correct url and getBearerTokenWhenAuthenticated methods', waitForAsync(() => {
      service.getApplicationInsightsParameter().subscribe(() => {
        expect(getBearerTokenWhenAuthenticatedSpy).toHaveBeenCalled();
        expect(getMonitoringParametersSpy).toHaveBeenCalledWith(
          'v1/monitoring/parameters'
        );
      });
    }));

    it('should return a ApplicationInsightsParameter', waitForAsync(() => {
      const expectedParameter: ApplicationInsightsParameter = {
        key: fakeAppInsightKey,
        notificationLevel: fakeAppInsightNotificationLevel,
        connectionString: null,
      };
      service
        .getApplicationInsightsParameter()
        .subscribe((parameter: ApplicationInsightsParameter) => {
          expect(parameter).toStrictEqual(expectedParameter);
        });
    }));
  });

  describe('fromClientContext', () => {
    describe('given a ClientContext', () => {
      const fakeClientContext: ClientContext = {
        clientId: '798899659',
        domainId: 65455,
        radicalApplication: 'GC',
        userId: '',
        authenticatedUser: {} as UserContext,
        currentUiCulture: '',
        dataSetLabel: '',
        installedLicence: '',
        password: '',
      };

      it('should return a MonitoringContext', () => {
        const expectedMonitoringContext: MonitoringContext = {
          clientId: '798899659',
          domainId: 65455,
          radicalApplication: 'GC',
        };

        expect(service.fromClientContext(fakeClientContext)).toStrictEqual(
          expectedMonitoringContext
        );
      });
    });
  });
});
