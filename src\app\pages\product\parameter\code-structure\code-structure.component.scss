.container {
    height: 100%;

    .form-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        gc-master-detail-panels-layout {
            flex: 1;
            overflow: hidden;

            .detail-header {
                display: flex;
                align-items: center;
                gap: 10px;

                .locked-icons-container {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
            }

            .panel {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;
                overflow: hidden;

                gc-characteristic-codes-list-view-edit {
                    overflow-y: auto;
                }

                .characteristics-container {
                    flex: 3;
                    overflow: auto;
                }

                .visualization-container {
                    display: flex;
                    flex-flow: column;
                    gap: 10px;
                    flex: 1;
                    margin: 10px 0 5px;
                    padding: 0;
                }
            }
        }

        .action-button-container {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 10px 10px 0;
            gap: 10px;
        }
    }
}
