import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DeviceAccessService } from './device-access.service';
import { DeviceType } from '../../models/types/device.type';

describe('DeviceAccessService', () => {
  let spectator: SpectatorService<DeviceAccessService>;
  let createService: ReturnType<
    typeof createServiceFactory<DeviceAccessService>
  >;
  beforeEach(() => {
    createService = createServiceFactory({
      service: DeviceAccessService,
    });
    spectator = createService({
      providers: [
        {
          provide: DeviceDetectorService,
          useValue: {
            getDeviceInfo: jest.fn().mockReturnValue({ deviceType: 'mobile' }),
          },
        },
      ],
    });
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('constructor', () => {
    createService = createServiceFactory({
      service: DeviceAccessService,
    });
    it('should initialize deviceType from DeviceDetectorService', () => {
      const mockDeviceInfo = { deviceType: 'mobile' };

      const newSpectator = createService({
        providers: [
          {
            provide: DeviceDetectorService,
            useValue: {
              getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
            },
          },
        ],
      });

      const mockService = newSpectator.inject(DeviceDetectorService);
      expect(mockService.getDeviceInfo).toHaveBeenCalled();
      expect(newSpectator.service).toBeTruthy();
    });

    it('should handle different device types', () => {
      const deviceTypes: DeviceType[] = [
        'mobile',
        'tablet',
        'desktop',
        'unknown',
      ];

      deviceTypes.forEach((deviceType) => {
        const mockDeviceInfo = { deviceType };

        const newSpectator = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
              },
            },
          ],
        });

        expect(newSpectator.service).toBeTruthy();
      });
    });
  });

  describe('allowedFor', () => {
    it('should return true when no deviceTypes are provided', () => {
      const result = spectator.service.allowedFor();
      expect(result).toBe(true);
    });

    it('should return true when empty array is provided', () => {
      const result = spectator.service.allowedFor([]);
      expect(result).toBe(true);
    });

    describe('mobile device', () => {
      let spectatorMobile: SpectatorService<DeviceAccessService>;

      beforeEach(() => {
        const createService = createServiceFactory({
          service: DeviceAccessService,
        });
        spectatorMobile = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest
                  .fn()
                  .mockReturnValue({ deviceType: 'mobile' }),
              },
            },
          ],
        });
      });

      it('should return true when current device is in allowed list', () => {
        const result = spectatorMobile.service.allowedFor(['mobile', 'tablet']);
        expect(result).toBe(true);
      });

      it('should return false when current device is not in allowed list', () => {
        const result = spectatorMobile.service.allowedFor([
          'tablet',
          'desktop',
        ]);
        expect(result).toBe(false);
      });
    });

    describe('tablet device', () => {
      let tabletSpectator: SpectatorService<DeviceAccessService>;

      beforeEach(() => {
        const createService = createServiceFactory({
          service: DeviceAccessService,
        });
        const mockDeviceInfo = { deviceType: 'tablet' };
        tabletSpectator = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
              },
            },
          ],
        });
      });

      it('should return true when current device is in allowed list', () => {
        // Device is tablet, and tablet is in the allowed list
        const result = tabletSpectator.service.allowedFor(['tablet']);
        expect(result).toBe(true);
      });

      it('should return false when current device is not in allowed list', () => {
        // Device is tablet, but only mobile and desktop are allowed
        const result = tabletSpectator.service.allowedFor([
          'mobile',
          'desktop',
        ]);
        expect(result).toBe(false);
      });

      const allowedDevicesCombinations = [
        { devices: ['tablet'], expected: true, description: 'tablet only' },
        {
          devices: ['tablet', 'mobile'],
          expected: true,
          description: 'tablet and mobile',
        },
        {
          devices: ['tablet', 'desktop'],
          expected: true,
          description: 'tablet and desktop',
        },
        {
          devices: ['tablet', 'mobile', 'desktop'],
          expected: true,
          description: 'all devices except unknown',
        },
        { devices: ['mobile'], expected: false, description: 'mobile only' },
        { devices: ['desktop'], expected: false, description: 'desktop only' },
        { devices: ['unknown'], expected: false, description: 'unknown only' },
      ];

      allowedDevicesCombinations.forEach(
        ({ devices, expected, description }) => {
          it(`should return ${expected} when allowed devices are ${description}`, () => {
            const result = tabletSpectator.service.allowedFor(
              devices as DeviceType[]
            );
            expect(result).toBe(expected);
          });
        }
      );
    });

    describe('desktop device', () => {
      let desktopSpectator: SpectatorService<DeviceAccessService>;

      beforeEach(() => {
        const createService = createServiceFactory({
          service: DeviceAccessService,
        });
        const mockDeviceInfo = { deviceType: 'desktop' };
        desktopSpectator = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
              },
            },
          ],
        });
      });

      it('should return true when no deviceTypes are provided', () => {
        const result = desktopSpectator.service.allowedFor();
        expect(result).toBe(true);
      });

      it('should return true when empty array is provided', () => {
        const result = desktopSpectator.service.allowedFor([]);
        expect(result).toBe(true);
      });

      it('should return true when current device is in allowed list', () => {
        // Device is desktop, and desktop is in the allowed list
        const result = desktopSpectator.service.allowedFor([
          'desktop',
          'tablet',
        ]);
        expect(result).toBe(true);
      });

      it('should return false when current device is not in allowed list', () => {
        // Device is desktop, but only mobile and tablet are allowed
        const result = desktopSpectator.service.allowedFor([
          'mobile',
          'tablet',
        ]);
        expect(result).toBe(false);
      });

      const allowedDevicesCombinations = [
        { devices: ['desktop'], expected: true, description: 'desktop only' },
        {
          devices: ['desktop', 'mobile'],
          expected: true,
          description: 'desktop and mobile',
        },
        {
          devices: ['desktop', 'tablet'],
          expected: true,
          description: 'desktop and tablet',
        },
        {
          devices: ['desktop', 'mobile', 'tablet'],
          expected: true,
          description: 'all devices except unknown',
        },
        { devices: ['mobile'], expected: false, description: 'mobile only' },
        { devices: ['tablet'], expected: false, description: 'tablet only' },
        { devices: ['unknown'], expected: false, description: 'unknown only' },
      ];

      allowedDevicesCombinations.forEach(
        ({ devices, expected, description }) => {
          it(`should return ${expected} when allowed devices are ${description}`, () => {
            const result = desktopSpectator.service.allowedFor(
              devices as DeviceType[]
            );
            expect(result).toBe(expected);
          });
        }
      );
    });

    describe('unknown device', () => {
      let unknownSpectator: SpectatorService<DeviceAccessService>;

      beforeEach(() => {
        const createService = createServiceFactory({
          service: DeviceAccessService,
        });
        const mockDeviceInfo = { deviceType: 'unknown' };
        unknownSpectator = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
              },
            },
          ],
        });
      });

      it('should return true when no deviceTypes are provided', () => {
        const result = unknownSpectator.service.allowedFor();
        expect(result).toBe(true);
      });

      it('should return true when empty array is provided', () => {
        const result = unknownSpectator.service.allowedFor([]);
        expect(result).toBe(true);
      });

      it('should always return false when deviceTypes are provided (unknown device behavior)', () => {
        const deviceTypeCombinations = [
          ['unknown'],
          ['mobile'],
          ['tablet'],
          ['desktop'],
          ['mobile', 'tablet'],
          ['mobile', 'desktop'],
          ['tablet', 'desktop'],
          ['mobile', 'tablet', 'desktop'],
          ['mobile', 'tablet', 'desktop', 'unknown'],
        ];

        deviceTypeCombinations.forEach((devices) => {
          const result = unknownSpectator.service.allowedFor(devices as DeviceType[]);
          expect(result).toBe(false);
        });
      });
    });
  });
});
