import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DeviceAccessService } from './device-access.service';
import { DeviceType } from '../../models/types/device.type';

describe('DeviceAccessService', () => {
  let spectator: SpectatorService<DeviceAccessService>;
  let deviceDetectorService: jest.Mocked<DeviceDetectorService>;

  const createService = createServiceFactory({
    service: DeviceAccessService,
    mocks: [DeviceDetectorService],
  });

  beforeEach(() => {
    spectator = createService({
      providers: [
        {
          provide: DeviceDetectorService,
          useValue: {
            getDeviceInfo: jest.fn().mockReturnValue({ deviceType: 'mobile' }),
          },
        },
      ],
    });
    deviceDetectorService = spectator.inject(DeviceDetectorService);
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('constructor', () => {
    it('should initialize deviceType from DeviceDetectorService', () => {
      const mockDeviceInfo = { deviceType: 'mobile' };

      const newSpectator = createService({
        providers: [
          {
            provide: DeviceDetectorService,
            useValue: {
              getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
            },
          },
        ],
      });

      const mockService = newSpectator.inject(DeviceDetectorService);
      expect(mockService.getDeviceInfo).toHaveBeenCalled();
      expect(newSpectator.service).toBeTruthy();
    });

    it('should handle different device types', () => {
      const deviceTypes: DeviceType[] = [
        'mobile',
        'tablet',
        'desktop',
        'unknown',
      ];

      deviceTypes.forEach((deviceType) => {
        const mockDeviceInfo = { deviceType };

        const newSpectator = createService({
          providers: [
            {
              provide: DeviceDetectorService,
              useValue: {
                getDeviceInfo: jest.fn().mockReturnValue(mockDeviceInfo),
              },
            },
          ],
        });

        expect(newSpectator.service).toBeTruthy();
      });
    });
  });

  describe('allowedFor', () => {
    describe('mobile device', () => {
      beforeEach(() => {
        const mockDeviceInfo = { deviceType: 'mobile' };
        jest
          .spyOn(deviceDetectorService, 'getDeviceInfo')
          .mockReturnValue(mockDeviceInfo as any);
      });

      it('should return true when no deviceTypes are provided', () => {
        const result = spectator.service.allowedFor();
        expect(result).toBe(true);
      });

      it('should return true when empty array is provided', () => {
        const result = spectator.service.allowedFor([]);
        expect(result).toBe(true);
      });

      it('should return true when current device is in allowed list', () => {
        // Device is mobile, and mobile is in the allowed list
        const result = spectator.service.allowedFor(['mobile', 'tablet']);
        expect(result).toBe(true);
      });

      it('should return false when current device is not in allowed list', () => {
        // Device is mobile, but only tablet and desktop are allowed
        const result = spectator.service.allowedFor(['tablet', 'desktop']);
        expect(result).toBe(false);
      });
    });

  });
});
