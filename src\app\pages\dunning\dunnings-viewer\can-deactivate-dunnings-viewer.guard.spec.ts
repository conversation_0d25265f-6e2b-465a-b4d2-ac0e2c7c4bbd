import { waitForAsync } from '@angular/core/testing';
import { DialogService } from '@gc/shared/ui';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Observable, of } from 'rxjs';
import { CanDeactivateDunningsViewerGuard } from './can-deactivate-dunnings-viewer.guard';

describe('CanDeactivateDunningsViewerGuard', () => {
  let spectator: SpectatorService<CanDeactivateDunningsViewerGuard>;

  let translocoService: TranslocoService;
  let dialogService: DialogService;

  let confirmSpy: jest.SpyInstance<Observable<boolean>>;
  let selectTranslateSpy: jest.SpyInstance<Observable<unknown>>;

  const createService = createServiceFactory({
    service: CanDeactivateDunningsViewerGuard,
    mocks: [TranslocoService, DialogService],
  });

  beforeEach(() => {
    spectator = createService();
    translocoService = spectator.inject(TranslocoService);
    dialogService = spectator.inject(DialogService);

    confirmSpy = jest.spyOn(dialogService, 'confirm');
    selectTranslateSpy = jest.spyOn(translocoService, 'selectTranslate');
  });

  it('should be created', () => {
    expect(spectator).toBeTruthy();
    expect(spectator.service).toBeTruthy();
  });

  describe('canDeactivate method', () => {
    describe('Given a state where the selectTranslate method returns a confirm message', () => {
      const confirmMessage = 'confirmMessage';
      beforeEach(() => {
        selectTranslateSpy.mockReturnValue(of(confirmMessage));
      });

      it('should call the selectTranslate method with the correct parameters', () => {
        spectator.service.canDeactivate();
        expect(selectTranslateSpy).toHaveBeenCalled();
      });

      describe('and when the user confirms the loss of unsaved changes', () => {
        beforeEach(() => {
          confirmSpy.mockReturnValue(of(true));
        });

        describe('when canDeactivate is called', () => {
          it('should return an observable with the value true', waitForAsync(() => {
            const result$ = spectator.service.canDeactivate();

            result$.subscribe((canDeactivate: boolean) => {
              expect(canDeactivate).toBe(true);
            });
          }));
        });
      });

      describe('and when the user cancel the navigation to keep the unsaved changes', () => {
        beforeEach(() => {
          confirmSpy.mockReturnValue(of(false));
        });

        describe('when canDeactivate is called', () => {
          it('should return an observable with the value true', waitForAsync(() => {
            const result$ = spectator.service.canDeactivate();

            result$.subscribe((canDeactivate: boolean) => {
              expect(canDeactivate).toBe(false);
            });
          }));
        });
      });
    });
  });
});
