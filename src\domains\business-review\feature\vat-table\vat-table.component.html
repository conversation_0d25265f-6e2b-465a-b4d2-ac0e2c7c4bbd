<div *transloco="let t" class="container">
  <ng-container
    *ngrxLet="{
      vatList: vatDatasource$,
      isLoading: isLoading$,
    } as vm">
    @if (vm.isLoading) {
      <div class="loader-container">
        <gc-loader />
      </div>
    } @else {
      <div
        class="table-container"
        *ngrxLet="vatDatasource$ | ngrxPush as vatList">
        @if (vatList) {
          <table mat-table [dataSource]="vatList">
            <!-- Checkbox Column -->
            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef></th>
              <td mat-cell *matCellDef="let row">
                <mat-checkbox
                  [checked]="selection.isSelected(row)"
                  [aria-label]="checkboxAriaLabel(row)"
                  (click)="$event.stopPropagation()"
                  (change)="$event ? toggleRow($event, row) : null"
                  color="primary" />
              </td>
            </ng-container>

            <!-- Label Column -->
            <ng-container matColumnDef="label">
              <th mat-header-cell *matHeaderCellDef>
                {{
                  t(
                    'businessReview.advanced-settings-tab.vat.table.column-header.label'
                  )
                }}
              </th>
              <td mat-cell *matCellDef="let rate">
                {{
                  t(
                    'businessReview.advanced-settings-tab.vat.table.row-label',
                    {
                      rate: rate | translocoDecimal,
                    }
                  )
                }}
              </td>
            </ng-container>

            <!-- Rate Column -->
            <ng-container matColumnDef="rate">
              <th mat-header-cell *matHeaderCellDef>
                {{
                  t(
                    'businessReview.advanced-settings-tab.vat.table.column-header.rate'
                  )
                }}
              </th>
              <td mat-cell *matCellDef="let rate">
                {{ rate | translocoDecimal }}
              </td>
            </ng-container>

            <tr
              mat-header-row
              *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr
              mat-row
              *matRowDef="let row; columns: displayedColumns"
              (click)="selection.toggle(row)"></tr>
          </table>
        }
      </div>
    }
  </ng-container>
</div>
