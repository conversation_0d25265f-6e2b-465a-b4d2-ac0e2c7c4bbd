<ng-container *transloco="let t; read: 'sales.commissions-tab.payout-summary'">
  <div class="payout-summary-footer-container">
    @if (amountToPay() !== null) {
      <span class="payout-summary-footer-text">
        {{ t('label') }}
        {{ amountToPay()! | amountWithCurrency: currency() }}
      </span>
    }
    <button mat-raised-button color="primary" type="button">
      {{ t('action') }}
    </button>
  </div>
</ng-container>
