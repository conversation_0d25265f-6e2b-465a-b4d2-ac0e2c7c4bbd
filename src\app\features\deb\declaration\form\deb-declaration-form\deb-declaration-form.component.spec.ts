import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebDeclarationFormComponent } from './deb-declaration-form.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebFacade } from '@gc/core/deb/application/facades';
import { LetDirective } from '@ngrx/component';
import { PushPipe } from '@ngrx/component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { CurrencyService } from '@gc/currency/data-access';
import { Currency } from '@gc/shared/models';
import { ResourceState } from '@gc/core/shared/store';
import { DraftDeclaration } from '@gc/core/deb/domains/models';
import { signal } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CurrencyInputComponent } from '@gc/shared/ui';
import { of } from 'rxjs';

describe('DebDeclarationFormComponent', () => {
  let spectator: Spectator<DebDeclarationFormComponent>;
  let component: DebDeclarationFormComponent;
  let debFacade: DebFacade;

  const mockCurrency: Currency = {
    code: 'EUR',
    symbol: '€',
    usualPrecision: 2,
  };

  const mockEuNomenclatures: string[] = ['EU001', 'EU002', 'EU003'];
  const mockDestinationCountryCodes: string[] = ['FR', 'DE', 'ES', 'IT'];
  const mockStatisticalProcedures: string[] = ['CODE1', 'CODE2', 'CODE3'];

  const mockEuNomenclaturesSignal = signal<ResourceState<string[]>>({
    isLoading: false,
    data: mockEuNomenclatures,
    status: 'Success',
    errors: undefined,
  });

  const mockDestinationCountryCodesSignal = signal<ResourceState<string[]>>({
    isLoading: false,
    data: mockDestinationCountryCodes,
    status: 'Success',
    errors: undefined,
  });

  const mockStatisticalProceduresSignal = signal<ResourceState<string[]>>({
    isLoading: false,
    data: mockStatisticalProcedures,
    status: 'Success',
    errors: undefined,
  });

  const mockDraftDeclaration: DraftDeclaration = {
    id: 'test-id',
    euNomenclature: 'EU001',
    destinationCountryCode: 'FR',
    invoicedAmount: 1000,
    statisticalProcedureCode: 'CODE1',
    vatNumber: 'VAT123',
    state: 'initial',
    status: 'enabled',
  };

  const mockDebFacade = {
    getEuNomenclatures: jest.fn().mockReturnValue(mockEuNomenclaturesSignal),
    getDestinationCountryCodesForCurrentCompanyAndDeclarationMonth: jest
      .fn()
      .mockReturnValue(mockDestinationCountryCodesSignal),
    getStatisticalProceduresForCurrentCompany: jest
      .fn()
      .mockReturnValue(mockStatisticalProceduresSignal),
    loadEuNomenclatures: jest.fn(),
    loadDestinationCountryCodes: jest.fn(),
    loadStatisticalProceduresFor: jest.fn(),
  };

  const createComponent = createComponentFactory({
    component: DebDeclarationFormComponent,
    imports: [
      ReactiveFormsModule,
      MatAutocompleteModule,
      MatButtonModule,
      MatOptionModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
    ],
    declarations: [
      MockDirectives(TranslocoDirective, LetDirective),
      MockComponents(CurrencyInputComponent),
      PushPipe,
    ],
    providers: [
      FormBuilder,
      {
        provide: DebFacade,
        useValue: mockDebFacade,
      },
      {
        provide: CurrencyService,
        useValue: {
          getDefaultCurrency$: jest.fn().mockReturnValue(of(mockCurrency)),
        },
      },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    jest.useFakeTimers();

    // Create the component with the inputs already set
    spectator = createComponent({
      props: {
        companyId: 'test-company-id',
        month: new Date('2023-01-01'),
        initialDraftDeclaration: mockDraftDeclaration,
      },
    });

    component = spectator.component;
    debFacade = spectator.inject(DebFacade);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should create', () => {
    component.ngOnChanges({ initialDraftDeclaration: {} as any });
    spectator.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    it('should initialize form controls with values from initialDraftDeclaration', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      expect(component.euNomenclatureFC.value).toBe(
        mockDraftDeclaration.euNomenclature
      );
      expect(component.destinationCountryCodeFC.value).toBe(
        mockDraftDeclaration.destinationCountryCode
      );
      expect(component.invoicedAmountFC.value).toBe(
        mockDraftDeclaration.invoicedAmount
      );
      expect(component.statisticalProcedureCodeFC.value).toBe(
        mockDraftDeclaration.statisticalProcedureCode
      );
      expect(component.vatNumberFC.value).toBe(mockDraftDeclaration.vatNumber);
    });

    it('should create a form group with all required controls', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      expect(component.formGroup.get('euNomenclature')).toBeTruthy();
      expect(component.formGroup.get('destinationCountryCode')).toBeTruthy();
      expect(component.formGroup.get('invoicedAmount')).toBeTruthy();
      expect(component.formGroup.get('statisticalProcedureCode')).toBeTruthy();
      expect(component.formGroup.get('vatNumber')).toBeTruthy();
    });
  });

  describe('constructor', () => {
    it('should set up signals and effects', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      // Advance timers to trigger the effect
      jest.runAllTimers();

      expect(debFacade.loadEuNomenclatures).toHaveBeenCalled();
      expect(debFacade.loadDestinationCountryCodes).toHaveBeenCalledWith(
        'test-company-id',
        new Date('2023-01-01')
      );
      expect(debFacade.loadStatisticalProceduresFor).toHaveBeenCalledWith(
        'test-company-id'
      );
    });
  });

  describe('when company changes', () => {
    it('should load nomenclatures, destination country codes and statistical procedures when month is not null', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      // Advance timers to trigger the effect
      jest.runAllTimers();

      expect(debFacade.loadEuNomenclatures).toHaveBeenCalled();
      expect(debFacade.loadDestinationCountryCodes).toHaveBeenCalledWith(
        'test-company-id',
        new Date('2023-01-01')
      );
      expect(debFacade.loadStatisticalProceduresFor).toHaveBeenCalledWith(
        'test-company-id'
      );
    });
    it("shouldn't load nomenclatures, destination country codes and statistical procedures when has no month selected", () => {
      jest.clearAllMocks();

      // Create a new spectator with month set to undefined
      spectator = createComponent({
        props: {
          companyId: 'test-company-id',
          month: undefined,
          initialDraftDeclaration: mockDraftDeclaration,
        },
      });
      component = spectator.component;
      debFacade = spectator.inject(DebFacade);

      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      expect(debFacade.loadEuNomenclatures).not.toHaveBeenCalled();
      expect(debFacade.loadDestinationCountryCodes).not.toHaveBeenCalled();
      expect(debFacade.loadStatisticalProceduresFor).not.toHaveBeenCalled();
    });
  });

  describe('onSubmit', () => {
    it('should emit the edited draft declaration with form values', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();
      const emitSpy = jest.spyOn(component.editedDraftDeclaration, 'emit');

      component.onSubmit();

      expect(emitSpy).toHaveBeenCalledWith({
        ...mockDraftDeclaration,
        euNomenclature: mockDraftDeclaration.euNomenclature,
        destinationCountryCode: mockDraftDeclaration.destinationCountryCode,
        invoicedAmount: mockDraftDeclaration.invoicedAmount,
        statisticalProcedureCode: mockDraftDeclaration.statisticalProcedureCode,
        vatNumber: mockDraftDeclaration.vatNumber,
      });
    });
  });

  describe('cancel', () => {
    it('should emit null to indicate cancellation', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();
      const emitSpy = jest.spyOn(component.editedDraftDeclaration, 'emit');

      component.cancel();

      expect(emitSpy).toHaveBeenCalledWith(null);
    });
  });

  describe('filteredEuNomenclatures', () => {
    it('should filter EU nomenclatures based on search text', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      component.currentEuNomenclatureSearch.set('EU00');

      // Assert
      const filteredResults = component.filteredEuNomenclatures();
      expect(filteredResults).toContain('EU001');
      expect(filteredResults).toContain('EU002');
    });
  });

  describe('listenForEuNomenclatureSearch', () => {
    it('should call currentEuNomenclatureSearch.set when euNomenclatureFC changes', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      const setSpy = jest.spyOn(component.currentEuNomenclatureSearch, 'set');

      component.euNomenclatureFC.setValue('EU003');

      jest.advanceTimersByTime(300);

      expect(setSpy).toHaveBeenCalledWith('EU003');
    });

    it('should call currentEuNomenclatureSearch.set when form control value changes', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      const setSpy = jest.spyOn(component.currentEuNomenclatureSearch, 'set');

      setSpy.mockClear();

      const newValue = 'EU003';
      component.euNomenclatureFC.setValue(newValue);

      jest.advanceTimersByTime(300);

      expect(setSpy).toHaveBeenCalledWith(newValue);
      component.euNomenclatureFC.setValue(newValue);

      jest.advanceTimersByTime(300);

      expect(setSpy).toHaveBeenCalledWith(newValue);
      expect(setSpy).toHaveBeenCalled();
    });

    it('should demonstrate debounce behavior for currentEuNomenclatureSearch.set', () => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();

      const setSpy = jest.spyOn(component.currentEuNomenclatureSearch, 'set');

      setSpy.mockClear();

      // Set multiple values in rapid succession (within the debounce time)
      component.euNomenclatureFC.setValue('E');
      component.euNomenclatureFC.setValue('EU');
      component.euNomenclatureFC.setValue('EU0');
      component.euNomenclatureFC.setValue('EU00');
      component.euNomenclatureFC.setValue('EU003');

      // At this point, no calls should have been made yet because of debouncing
      expect(setSpy).not.toHaveBeenCalledWith('E');
      expect(setSpy).not.toHaveBeenCalledWith('EU');
      expect(setSpy).not.toHaveBeenCalledWith('EU0');
      expect(setSpy).not.toHaveBeenCalledWith('EU00');

      // Now advance past the debounce time from the last change
      jest.advanceTimersByTime(300);

      // The set method should be called with the final value
      expect(setSpy).toHaveBeenCalledWith('EU003');

      // The intermediate values should not have been used
      const allCalls = setSpy.mock.calls.map((call) => call[0]);
      expect(allCalls).not.toContain('E');
      expect(allCalls).not.toContain('EU');
      expect(allCalls).not.toContain('EU0');
      expect(allCalls).not.toContain('EU00');
    });
  });

  describe('nomenclature field validation', () => {
    beforeEach(() => {
      component.ngOnChanges({ initialDraftDeclaration: {} as any });
      spectator.detectChanges();
    });

    it('should be invalid when nomenclature is more than 15 characters', () => {
      component.euNomenclatureFC.setValue('1234567890123456');
      spectator.detectChanges();

      expect(component.euNomenclatureFC.valid).toBeFalsy();
      expect(component.euNomenclatureFC.hasError('maxlength')).toBeTruthy();
    });

    it('should be valid when nomenclature is exactly 15 characters', () => {
      component.euNomenclatureFC.setValue('123456789012345');
      spectator.detectChanges();

      expect(component.euNomenclatureFC.valid).toBeTruthy();
      expect(component.euNomenclatureFC.hasError('maxlength')).toBeFalsy();
    });

    it('should be invalid when nomenclature is empty', () => {
      component.euNomenclatureFC.setValue('');
      spectator.detectChanges();

      expect(component.euNomenclatureFC.valid).toBeFalsy();
      expect(component.euNomenclatureFC.hasError('required')).toBeTruthy();
    });
  });
});
