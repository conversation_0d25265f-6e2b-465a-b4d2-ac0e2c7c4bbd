import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { WarehousesTableComponent } from './warehouses-table.component';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { BusinessReviewService } from '@gc/business-review/data-access';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { waitForAsync } from '@angular/core/testing';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { warehousesFixture } from '@gc/warehouse/models';
import { WarehouseService } from '@gc/warehouse/data-access';
import { VatService } from '@gc/vat/data-access';

describe('WarehousesTableComponent', () => {
  let spectator: Spectator<WarehousesTableComponent>;
  let businessReviewService: BusinessReviewService;

  const createComponent = createComponentFactory({
    component: WarehousesTableComponent,
    imports: [MatTableDataSource, SelectionModel],
    mocks: [TranslocoService, FiscalYearService, WarehouseService, VatService],
    declarations: [MockDirective(TranslocoDirective)],
  });

  beforeEach(() => {
    spectator = createComponent();
    businessReviewService = spectator.inject(BusinessReviewService);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('MatTableDataSource and SelectionModel', () => {
    describe('Given an initial state', () => {
      it('should have an empty dataSource', () => {
        expect(spectator.component.dataSource).toBeUndefined();
      });

      it('should have an empty selection', () => {
        expect(spectator.component.selection.selected).toEqual([]);
      });
    });

    describe('Given a state where the service emits a list of warehouses', () => {
      const warehouses = warehousesFixture();
      beforeEach(() => {
        businessReviewService.warehouses$$.next(warehouses);
      });

      it('should have a dataSource with the list of warehouses', waitForAsync(() => {
        expect.assertions(2);
        spectator.component.warehouses$.subscribe((data) => {
          expect(spectator.component.dataSource.data).toEqual(warehouses);
          expect(data).toEqual(spectator.component.dataSource);
        });
      }));

      it('should have an empty selection', () => {
        expect(spectator.component.selection.selected).toEqual([]);
      });

      describe('then given a state where the selectedIds input is set', () => {
        beforeEach(() => {
          spectator.setInput('selectedIds', [warehouses[0].id]);
          spectator.fixture.detectChanges();
        });

        it('should update the selection', () => {
          expect.assertions(1);
          spectator.component.warehouses$.subscribe(() => {
            expect(spectator.component.selection.selected).toEqual([
              warehouses[0],
            ]);
          });
        });
      });
    });
  });

  describe('isAllSelected method', () => {
    describe('Given a state where  the service contains a list of warehouses', () => {
      const warehouses = warehousesFixture();
      beforeEach(() => {
        businessReviewService.warehouses$$.next(warehouses);
      });

      it('should return true if the number of selected elements is equal to the number of elements in the list', waitForAsync(() => {
        expect.assertions(1);
        spectator.component.warehouses$.subscribe(() => {
          // select all rows
          spectator.component.selection.select(
            ...spectator.component.dataSource.data
          );
          expect(spectator.component.isAllSelected()).toBe(true);
        });
      }));

      it('should return false if the number of selected elements is not equal to the number of elements in the list', waitForAsync(() => {
        expect.assertions(1);
        spectator.component.warehouses$.subscribe(() => {
          // select all rows
          spectator.component.selection.select(
            spectator.component.dataSource.data[0]
          );
          expect(spectator.component.isAllSelected()).toBe(false);
        });
      }));

      it('should return false if there is no selected row', waitForAsync(() => {
        expect.assertions(1);
        spectator.component.warehouses$.subscribe(() => {
          // select all rows
          spectator.component.selection.clear();
          expect(spectator.component.isAllSelected()).toBe(false);
        });
      }));
    });
  });

  describe('toggleAllRows method', () => {
    const warehouses = warehousesFixture();
    beforeEach(() => {
      businessReviewService.warehouses$$.next(warehouses);
      spectator.component.warehouses$.subscribe();
    });

    describe('Given a state where no row is selected', () => {
      it('should select all displayed rows', waitForAsync(() => {
        expect(spectator.component.selection.selected).toEqual([]);
        // Act
        spectator.component.toggleAllRows();
        expect(spectator.component.selection.selected).toEqual(
          spectator.component.dataSource.filteredData
        );
      }));
    });

    describe('Given a state where all rows are selected', () => {
      beforeEach(() => {
        spectator.component.selection.select(
          ...spectator.component.dataSource.data
        );
      });
      it('should deselect all rows', () => {
        expect(spectator.component.selection.selected).toEqual(
          spectator.component.dataSource.data
        );
        // Act
        spectator.component.toggleAllRows();
        expect(spectator.component.selection.selected).toEqual([]);
      });
    });

    describe('Given a state where some rows are selected', () => {
      beforeEach(() => {
        spectator.component.selection.select(
          spectator.component.dataSource.data[0]
        );
      });
      it('should select all displayed rows', waitForAsync(() => {
        expect(spectator.component.selection.selected).toEqual([
          spectator.component.dataSource.data[0],
        ]);
        // Act
        spectator.component.toggleAllRows();
        expect(spectator.component.selection.selected).toEqual(
          spectator.component.dataSource.filteredData
        );
      }));
    });
  });
});
