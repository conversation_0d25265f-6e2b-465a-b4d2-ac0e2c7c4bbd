:host {
  height: 100%;
  display: flex;
  flex-direction: column;
}

gc-company-single-select {
  display: block;
  width: 212px;
}

gc-deb-parameters {
  display: block;
  flex: 1;
}

gc-deb-filters {
  display: block;
  flex: 2;
}

.closed-month-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.no-enclose-month-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  gap: 5px;
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

/**
  * Atomic CSS inspired by TailwindCSS
  * @see https://tailwindcss.com
  */
.mt-4 {
  margin-top: 1rem; /* 16px */
}

.ml-auto {
  margin-left: auto;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.gap-05 {
  gap: 0.5rem;
}

.grow {
  flex-grow: 1;
}

:host(.mat-mdc-card-content:last-child) {
  padding-bottom: 0.25rem !important;
}
