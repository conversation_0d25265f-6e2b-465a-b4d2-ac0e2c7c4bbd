import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';
import { commissionsServicesProviders } from '@gc/core/sales/commissions';
import { commissionsInfrastructureProviders } from '@gc/core/sales/commissions/infrastructure';
import { CommissionsComponent } from './commissions.component';
import { representativesServicesProviders } from '@gc/core/sales/representatives';
import { representativesInfrastructureProviders } from '@gc/core/sales/representatives/infrastructure';

export const ROUTES: Routes = [
  {
    path: '',
    component: CommissionsComponent,
    title: TitleKeyTab.COMMISSIONS,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.COMMISSIONS)],
    providers: [
      commissionsServicesProviders(),
      commissionsInfrastructureProviders(),
      representativesServicesProviders(),
      representativesInfrastructureProviders(),
    ],
  },
];
