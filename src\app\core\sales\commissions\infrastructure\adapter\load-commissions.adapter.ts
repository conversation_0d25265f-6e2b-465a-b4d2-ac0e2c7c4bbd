import { inject, Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';
import { LoadCommissionsPort } from '../../domains/ports';
import { Commission, CommissionsFilters } from '../../domains/models';
import { CommissionsApiService } from '../api';
import { CommissionResponse } from '../api/response';

@Injectable()
export class LoadCommissionsAdapter implements LoadCommissionsPort {
  private readonly api = inject(CommissionsApiService);

  for(filters: CommissionsFilters | null): Observable<Commission[]> {
    if (!filters) {
      return of([]);
    }

    return this.api.loadCommissions().pipe(
      map((response: CommissionResponse[]) => {
        return response.map((commission) => commission as Commission);
      })
    );
  }
}
