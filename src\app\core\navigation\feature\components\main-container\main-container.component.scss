@use 'gc-variables';

.main-container {
    height: 100%;

    mat-drawer-container {
        width: 100%;
        height: 100%;

        mat-drawer-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .navigation-container {
                flex: 1;
                overflow: hidden;
                padding: 20px;
                width: 100%;
                max-width: gc-variables.$page-max-width;
                margin: auto;
            }
        }
    }
}

/**
  * Atomic CSS inspired by TailwindCSS
  * @see https://tailwindcss.com
  */

.overflow-y-auto {
    overflow-y: auto !important; // !important to override .navigation-container style
}
