<ng-container>
  <div
    mat-dialog-title
    class="title-container"
    *transloco="let t; read: 'deb.declaration-edit-dialog'">
    @if (formConfig.draftDeclaration) {
      <mat-icon [color]="'primary'"> mode_edit </mat-icon>
      <span> {{ t('title.update') }} </span>
    } @else {
      <mat-icon [color]="'primary'"> post_add </mat-icon>
      <span> {{ t('title.add') }} </span>
    }
  </div>
  <mat-dialog-content
    class="content-container"
    *transloco="let t; read: 'sharedErrors.form'">
    <span class="all-fields-required-message">
      {{ t('required') }}
    </span>
    @if (!formConfig.draftDeclaration) {
      <div
        class="warning-message"
        *transloco="let t; read: 'deb.declaration-edit-dialog'">
        <mat-icon color="primary">info</mat-icon>
        <span>{{ t('warning-message') }}</span>
      </div>
    }
    <gc-deb-declaration-form
      [initialDraftDeclaration]="formConfig.draftDeclaration"
      [companyId]="formConfig.companyId"
      [month]="formConfig.month"
      (editedDraftDeclaration)="onEdit($event)" />
  </mat-dialog-content>
</ng-container>
