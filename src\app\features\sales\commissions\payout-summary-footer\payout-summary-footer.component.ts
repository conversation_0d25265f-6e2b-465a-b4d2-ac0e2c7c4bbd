import { Component, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { AmountWithCurrencyPipe } from '@gc/currency/ui';
import { Currency } from '@gc/shared/models';
import { TranslocoDirective } from '@jsverse/transloco';

@Component({
  selector: 'gc-payout-summary-footer',
  standalone: true,
  imports: [
    TranslocoDirective,
    AmountWithCurrencyPipe,
    MatButtonModule,
    MatInputModule,
  ],
  templateUrl: './payout-summary-footer.component.html',
  styleUrls: [
    './payout-summary-footer.component.scss',
    './payout-summary-footer-theme.component.scss',
  ],
})
export class PayoutSummaryFooterComponent {
  currency = input.required<Currency>();

  amountToPay = input.required<number | null>();
}
