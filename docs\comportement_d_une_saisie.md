# Comportement standard d'une saisie Reactive Form

## Objectif

Ce document recense pour l'instant le comportement standard d'une saisie, des cas d'utilisations et points de vigilance.
**<span style="color:red">EN COURS DE CONSTRUCTION ET REFLEXION - EQUIPE PIRATS**</span>

# Ergonomie et comportement

Les comportements et cas d'utilisations mais sans solution technique pour l'instant.

## Header systématique

A-t-on systématiquement un header, avec la possibilité de retour sur le menu précédent...

## L'utilisateur identifie qu'une saisie ou modification est en cours

Le bouton Enregistrer s'active ou se désactive

## L'utilisateur est averti s'il risque de perdre sa saisie

Si l'utilisateur quitte la saisie, il a une confirmation de l'abandon de ses modifications

-   retour menu précédent
-   fermeture de l'onglet du navigateur
-   navigation (précédent / suivant)
-   bouton Annuler

## L'utilisateur est averti s'il y a des erreurs à l'enregistrement

En cas d'erreur à l'enregistrement, la saisie doit rester dans un état "modification en cours non enregistrée".

Cas d'erreur à gérer

-   Erreur non issue de l'API (ex: coupure internet)
-   Erreur technique issue de l'API
-   Erreur métier en cours de transition vers (422) anciennement (409) issue de l'API

L'utilisateur doit voir d'une façon ou d'une autre l'erreur (ergonomie d'affichage à définir UIX).
L'API peut retourner des messages métiers et clair et compréhensibles pour l'utilisateur (pas limité à 15 caractères ;) )

## L'utilisateur peut-il continuer à travailler pendant l'enregistrement

Si l'utilisateur clique sur Enregistrer puis continue à saisir, que se passe-t-il lorsque l'enregistrement va avoir réussi (asynchrone) ou échoué ?
L'utilisateur peut-il imprimer un règlement dès qu'il clique sur enregistrer au risque que le règlement n'existe pas.

## Un bouton peut-il être "désactivé" après le click

L'utilisateur peut-il cliquer plusieurs fois de suite sur "Générer une relance", peut-on bloquer l'action d'un bouton et la débloquer de façon pertinente en cas de succès ou d'échec de l'action)

## Le loader

L'expérience Embeded (loader systématique lors d'un appel service) n'a pas été très concluante ou plutôt elle apporte certaines contraintes
(je laisse ORassi compléter ce paragraphe)

# Question technique

Pour l'instant, les idées sont simplement listées

## Préconisation

Avoir un modèle qui va pouvoir être transformé en composant Reactive Form via un FormBuilder
Organiser mon modèle pour ne pas avoir de propriété 'sans impact' pour l'état de saisie dans le FormGroup à enregistrer
(ex: une propriété visuelle dont on veut conserver la valeur, une case à cocher pour masquer quelque chose ne doit mettre ma saisie en dirty)

## Utilisation du store sur les données saisies

L'utilisation du data-store est-elle pertinente pour les données saisissables ?
J'ai mis ci-dessous mes idées / arguments, ce n'est donc pas exhaustif.
. ce choix ne risque-t-il pas d'entrainer des difficultés pour maîtriser quelle est la valeur valide. Celle de mon store ou la Value de mon FormControl (REX embedded-Reglement).
. construire des règles de gestion (validator ou autre) qui s'appuieraient en partie sur les données du store et sur les Value de FormContre semblent difficile à maintenir
. Cela reviendrait à émettre un Dispatch pour enregistrer et écouter le retour réussi mais aussi compris les erreurs (donc identifier que l'erreur http est bien liée à mon appel d'enregistrement et pas une autre accès service) pour poursuivre de façon cohérente mon action
