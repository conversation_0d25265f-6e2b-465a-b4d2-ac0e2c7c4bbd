import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { MonthlyAccountingReportsAvailabilityApi } from '@gc/shared/api/data-access';

export class MonthlyTreatmentAvailableEditionsAdapter {
  static fromApi(
    api: MonthlyAccountingReportsAvailabilityApi
  ): MonthlyTreatmentAvailableEdition[] {
    return [
      {
        id: MonthlyTreatmentEditionsEnum.SALES,
        isAvailable: api.categorySalesExist,
      },
      {
        id: MonthlyTreatmentEditionsEnum.DETAILS,
        isAvailable: api.saleDetailsExist,
      },
      {
        id: MonthlyTreatmentEditionsEnum.RECEIPTS,
        isAvailable: api.paymentListingExist,
      },
      {
        id: MonthlyTreatmentEditionsEnum.DEBT,
        isAvailable: api.unpaidListingExist,
      },
      {
        id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
        isAvailable: false,
      },
    ];
  }
}
