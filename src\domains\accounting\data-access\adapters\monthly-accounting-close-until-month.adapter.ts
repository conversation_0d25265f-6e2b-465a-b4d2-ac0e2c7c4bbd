import { MonthlyAccountingCloseUntilMonth } from '@gc/accounting/models';
import { MonthlyAccountingReportsClosureParameterApi } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';

export class MonthlyAccountingCloseUntilMonthAdapter {
  public static toApi(
    parameters: MonthlyAccountingCloseUntilMonth
  ): MonthlyAccountingReportsClosureParameterApi {
    return {
      companyId: parameters.companyId,
      month: DateAdapter.dateToStringAPIWithoutDay(parameters.month) as string,
    };
  }
}
