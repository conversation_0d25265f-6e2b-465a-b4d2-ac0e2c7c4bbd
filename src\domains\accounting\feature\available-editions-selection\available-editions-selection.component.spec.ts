import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { AvailableEditionsSelectionComponent } from './available-editions-selection.component';
import { MockDirectives, MockModule } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import {
  monthlyTreatmentHistoryActions,
  MonthlyTreatmentHistoryStoreModule,
  MonthlyTreatmentStoreModule,
  selectAvailableMonthlyEditionsLoadingStatus,
} from '@gc/accounting/data-access';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { waitForAsync } from '@angular/core/testing';
import { MonthlyTreatmentEditionsEnum } from '@gc/accounting/models';
import { LoadingStatus } from '@gc/shared/models';

describe('AvailableEditionsSelectionComponent', () => {
  let spectator: Spectator<AvailableEditionsSelectionComponent>;
  let component: AvailableEditionsSelectionComponent;

  let store: Store;

  const createComponent = createComponentFactory({
    component: AvailableEditionsSelectionComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockModule(MonthlyTreatmentStoreModule),
      MockModule(MonthlyTreatmentHistoryStoreModule),
    ],
    mocks: [Store],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    store = spectator.inject(Store);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    let selectAvailableMonthlyEditionsLoadingStatusSpy: jest.SpyInstance<
      Observable<LoadingStatus>
    >;

    let handleAvailableEditionsRetrievalSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      selectAvailableMonthlyEditionsLoadingStatusSpy = jest.spyOn(
        store,
        'select'
      ) as jest.SpyInstance<Observable<LoadingStatus>>;

      selectAvailableMonthlyEditionsLoadingStatusSpy.mockReturnValue(
        of('IN_PROGRESS')
      );

      handleAvailableEditionsRetrievalSpy = jest
        .spyOn(component, 'handleAvailableEditionsRetrieval')
        .mockImplementation();
    });
    it('should call handleAvailableEditionsRetrieval method', () => {
      component.ngOnInit();

      expect(handleAvailableEditionsRetrievalSpy).toHaveBeenCalled();
    });

    it('should call select method with selectAvailableMonthlyEditionsLoadingStatus', () => {
      component.ngOnInit();

      expect(
        selectAvailableMonthlyEditionsLoadingStatusSpy
      ).toHaveBeenCalledWith(selectAvailableMonthlyEditionsLoadingStatus);
    });
  });

  describe('handleAvailableEditionsRetrieval', () => {
    describe('given any state', () => {
      describe('when select(selectSelectedEnclosedMonth) method of Store push a nuw value', () => {
        it('should dispatch loadSelectedMonthAvailableEditions action', waitForAsync(() => {
          jest
            .spyOn(store, 'select')
            .mockReturnValue(of(new Date('2024-02-02')));
          const dispatchSpy = jest.spyOn(store, 'dispatch');

          component.handleAvailableEditionsRetrieval();

          expect(dispatchSpy).toHaveBeenCalledWith(
            monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions()
          );
        }));
      });
    });
  });

  describe('toggleEdition', () => {
    describe('given any state', () => {
      describe('when method is called with an edition', () => {
        it('should dispatch toggleMonthlyTreatmentEditionSelection action', waitForAsync(() => {
          const dispatchSpy = jest.spyOn(store, 'dispatch');

          component.toggleEdition(MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY);

          expect(dispatchSpy).toHaveBeenCalledWith(
            monthlyTreatmentHistoryActions.toggleMonthlyTreatmentEditionSelection(
              {
                edition: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
              }
            )
          );
        }));
      });
    });
  });
});
