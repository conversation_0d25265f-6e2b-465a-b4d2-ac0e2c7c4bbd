import { waitForAsync } from '@angular/core/testing';
import { FormArray, FormBuilder } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import {
  BlockReloadDirective,
  MainContainerComponent,
} from '@gc/core/navigation/feature';
import {
  ProductCharacteristicActions,
  ProductCharacteristicSelectionService,
  ProductCharacteristicsFormService,
  ProductCharacteristicsFormValidationService,
  ProductCharacteristicsReloadService,
  ProductCharacteristicsService,
  ProductCharacteristicStoreModule,
} from '@gc/product/data-access';
import { FixedCharacteristicEditInfoIconComponent } from '@gc/product/feature';
import {
  anyProductCharacteristic,
  CodeSizeWarning,
  ProductCharacteristic,
  productCharacteristics,
} from '@gc/product/models';
import {
  CODE_SIZE_MAX,
  ProductCharacteristicsFormHelper,
} from '@gc/product/utils';
import { DialogService } from '@gc/shared/ui';
import { Spectator } from '@ngneat/spectator';
import { createComponentFactory } from '@ngneat/spectator/jest';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import {
  MockComponents,
  MockDirectives,
  MockModule,
  MockPipes,
} from 'ng-mocks';
import { Observable, of, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { CodeStructureComponent } from './code-structure.component';

describe('CodeStructureComponent', () => {
  let spectator: Spectator<CodeStructureComponent>;
  let component: CodeStructureComponent;
  let productCharacteristicsService: ProductCharacteristicsService;
  let productCharacteristicsFormService: ProductCharacteristicsFormService;
  let productCharacteristicsReloadService: ProductCharacteristicsReloadService;
  let productCharacteristicsFormHelper: ProductCharacteristicsFormHelper;
  let formBuilder: FormBuilder;
  let characteristicsFormArray: FormArray;
  let dialogService: DialogService;
  let translocoService: TranslocoService;

  let store: Store;
  let dispatchSpy: jest.SpyInstance<void>;

  let selectTranslateSpy: jest.SpyInstance<Observable<string>>;

  const createComponent = createComponentFactory({
    component: CodeStructureComponent,
    declarations: [
      MockComponents(
        MainContainerComponent,
        MatButton,
        MatIcon,
        FixedCharacteristicEditInfoIconComponent
      ),
      MockDirectives(TranslocoDirective, BlockReloadDirective),
      MockPipes(PushPipe),
      MockModule(ProductCharacteristicStoreModule),
    ],
    mocks: [
      TranslocoService,
      ProductCharacteristicsFormService,
      ProductCharacteristicsService,
      DialogService,
      ProductCharacteristicSelectionService,
      ProductCharacteristicsReloadService,
      ProductCharacteristicsFormValidationService,
      Store,
    ],
    providers: [FormBuilder],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    spectator.inject(ProductCharacteristicsFormValidationService);
    productCharacteristicsService = spectator.inject(
      ProductCharacteristicsService
    );
    productCharacteristicsFormService = spectator.inject(
      ProductCharacteristicsFormService
    );
    formBuilder = spectator.inject(FormBuilder);
    dialogService = spectator.inject(DialogService);
    translocoService = spectator.inject(TranslocoService);
    productCharacteristicsFormHelper = spectator.inject(
      ProductCharacteristicsFormHelper
    );
    productCharacteristicsReloadService = spectator.inject(
      ProductCharacteristicsReloadService
    );
    store = spectator.inject(Store);

    characteristicsFormArray = formBuilder.array([...productCharacteristics]);

    selectTranslateSpy = jest.spyOn(
      translocoService,
      'selectTranslate'
    ) as jest.SpyInstance<Observable<string>>;
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });

  describe('canDeactivate', () => {
    it('should call canReloadCodeStructure', () => {
      component.canDeactivate();
      const productCharacteristicsReloadServiceSpy = jest.spyOn(
        productCharacteristicsReloadService,
        'canReloadCodeStructure'
      );
      expect(productCharacteristicsReloadServiceSpy).toHaveBeenCalled();
    });
  });

  describe('onInit', () => {
    let getCharacteristicsSpy: jest.SpyInstance<
      Observable<ProductCharacteristic[]> | undefined
    >;
    let initFormGroupSpy: jest.SpyInstance<void>;

    describe('given a state with a anyProductCharacteristic', () => {
      beforeEach(() => {
        getCharacteristicsSpy = jest
          .spyOn(productCharacteristicsService, 'getCharacteristics$')
          .mockReturnValue(of([anyProductCharacteristic]));
        jest
          .spyOn(
            productCharacteristicsFormService,
            'productCharacteristicsFormArray',
            'get'
          )
          .mockReturnValue(characteristicsFormArray);

        initFormGroupSpy = jest.spyOn(
          productCharacteristicsFormService,
          'initFormGroup'
        );
      });

      it('should call getCharacteristics method of productCharacteristicsService', waitForAsync(() => {
        component.ngOnInit();

        expect(getCharacteristicsSpy).toHaveBeenCalled();
      }));

      it('should call initFormGroup method of productCharacteristicsFormService', waitForAsync(() => {
        component.ngOnInit();

        expect(initFormGroupSpy).toHaveBeenCalled();
      }));

      it('should initialCharacteristics with productCharacteristics', waitForAsync(() => {
        const initialCharacteristicsSpy = jest.spyOn(
          productCharacteristicsReloadService,
          'initialCharacteristics',
          'set'
        );

        component.ngOnInit();

        expect(initialCharacteristicsSpy).toHaveBeenCalledWith([
          ...productCharacteristics,
        ]);
      }));
    });
  });

  describe('onSave method', () => {
    let getSaveObservableSpy: jest.SpyInstance<Observable<void>>;

    beforeEach(() => {
      getSaveObservableSpy = jest
        .spyOn(component, 'getSaveObservable$')
        .mockReturnValue(of(void 0));
    });
    describe('given a state where saveInProgress property is true', () => {
      beforeEach(() => {
        component.saveInProgress = true;
      });
      it("shouldn't call getSaveObservable method while a other saving", waitForAsync(() => {
        component.onSave();
        expect(getSaveObservableSpy).not.toHaveBeenCalled();
      }));
    });
    describe('given a state where saveInProgress property is false', () => {
      beforeEach(() => {
        component.saveInProgress = false;
      });

      describe('and confirmSave return an Observable of true', () => {
        beforeEach(() => {
          jest.spyOn(component, 'confirmSave$').mockReturnValue(of(true));
        });

        it('should call getSaveObservable method', waitForAsync(() => {
          component.onSave();
          expect(getSaveObservableSpy).toHaveBeenCalled();
        }));

        it('should set saveInProgress back to false', waitForAsync(() => {
          component.onSave();
          expect(component.saveInProgress).toBe(false);
        }));
      });

      describe('and confirmSave return an Observable of false', () => {
        beforeEach(() => {
          jest.spyOn(component, 'confirmSave$').mockReturnValue(of(false));
        });

        it('should not call getSaveObservable method', waitForAsync(() => {
          component.onSave();
          expect(getSaveObservableSpy).not.toHaveBeenCalled();
        }));

        it('should set saveInProgress back to false', waitForAsync(() => {
          component.onSave();
          expect(component.saveInProgress).toBe(false);
        }));
      });
    });
  });

  describe('getSaveObservable method', () => {
    beforeEach(() => {
      jest
        .spyOn(
          productCharacteristicsFormService,
          'productCharacteristicsFormArray',
          'get'
        )
        .mockReturnValue(characteristicsFormArray);
    });

    describe('given a state where saveCharacteristics works', () => {
      let saveCharacteristicsSpy: jest.SpyInstance<Observable<void>>;
      beforeEach(() => {
        saveCharacteristicsSpy = jest
          .spyOn(productCharacteristicsService, 'saveCharacteristics$')
          .mockReturnValue(of(void 0));
      });
      it('should call saveCharacteristics method with correct data', waitForAsync(() => {
        const expectedCharacteristics = [...productCharacteristics];

        component
          .getSaveObservable$()
          .subscribe(() =>
            expect(saveCharacteristicsSpy).toHaveBeenCalledWith(
              expectedCharacteristics
            )
          );
      }));
      it('should affect the value of productCharacteristicsFormArray at initialCharacteristics', waitForAsync(() => {
        const expectedCharacteristics = characteristicsFormArray.getRawValue();
        const initialCharacteristicsSpy = jest.spyOn(
          productCharacteristicsReloadService,
          'initialCharacteristics',
          'set'
        );

        component
          .getSaveObservable$()
          .subscribe(() =>
            expect(initialCharacteristicsSpy).toHaveBeenCalledWith(
              expectedCharacteristics
            )
          );
      }));

      it('should call resetFormStatus of productCharacteristicsFormService', waitForAsync(() => {
        const resetFormStatusSpy = jest.spyOn(
          productCharacteristicsFormService,
          'resetFormStatus'
        );

        component
          .getSaveObservable$()
          .subscribe(() => expect(resetFormStatusSpy).toHaveBeenCalled());
      }));

      it('should dispatch characteristicsSaved action', waitForAsync(() => {
        dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

        component
          .getSaveObservable$()
          .subscribe(() =>
            expect(dispatchSpy).toHaveBeenCalledWith(
              ProductCharacteristicActions.characteristicsSaved()
            )
          );
      }));
    });
  });

  describe('confirmSave method', () => {
    let dialogConfirmSpy: jest.SpyInstance<Observable<boolean>>;

    const confirmMessage = 'Veuillez confirmer';
    const codeSizeWarnings = [
      'first warning message',
      'second warning message',
    ];
    const reorderWarningMessage = 'Characteristics have been reordered';

    beforeEach(() => {
      dialogConfirmSpy = jest
        .spyOn(dialogService, 'confirm')
        .mockReturnValue(of(true));

      jest.spyOn(component, 'getCodeSizeWarnings').mockReturnValue([]);
      jest
        .spyOn(component, 'getCharacteristicsReorderWarningMessage$')
        .mockReturnValue(of(reorderWarningMessage));
      jest
        .spyOn(component, 'getConfirmSaveMessage$')
        .mockReturnValue(of(confirmMessage));
    });

    describe('given any state', () => {
      it('should call confirm method of DialogService with message', waitForAsync(() => {
        component.confirmSave$().subscribe(() => {
          expect(dialogConfirmSpy).toHaveBeenCalledWith([confirmMessage]);
        });
      }));
    });

    describe('given a state where confirm method of DialogService return an Obserable of true', () => {
      beforeEach(() => {
        dialogConfirmSpy.mockReturnValue(of(true));
      });
      it('should return an Observable of true', waitForAsync(() => {
        component.confirmSave$().subscribe((hasConfirmed) => {
          expect(hasConfirmed).toBe(true);
        });
      }));
    });

    describe('given a state where confirm method of DialogService return an Obserable of false', () => {
      beforeEach(() => {
        dialogConfirmSpy.mockReturnValue(of(false));
      });
      it('should return an Observable of false', waitForAsync(() => {
        component.confirmSave$().subscribe((hasConfirmed) => {
          expect(hasConfirmed).toBe(false);
        });
      }));
    });

    describe('given a state where there is code size warnings', () => {
      beforeEach(() => {
        jest
          .spyOn(component, 'getCodeSizeWarnings')
          .mockReturnValue(codeSizeWarnings);
      });

      it('should call dialog confirm with code size warnings', waitForAsync(() => {
        component.confirmSave$().subscribe(() => {
          expect(dialogConfirmSpy).toHaveBeenCalledWith([
            ...codeSizeWarnings,
            confirmMessage,
          ]);
        });
      }));
    });

    describe('given a state where characteristicsHaveBeenReorderedSnapshot of ProductCharacteristicsFormService return true', () => {
      beforeEach(() => {
        jest
          .spyOn(
            productCharacteristicsFormService,
            'characteristicsHaveBeenReorderedSnapshot',
            'get'
          )
          .mockReturnValue(true);
      });

      it('should call dialog confirm with reorder warning message', waitForAsync(() => {
        component.confirmSave$().subscribe(() => {
          expect(dialogConfirmSpy).toHaveBeenCalledWith([
            reorderWarningMessage,
            confirmMessage,
          ]);
        });
      }));
    });
  });

  describe('cancel method', () => {
    let dialogServiceConfirmSpy: jest.SpyInstance<Observable<boolean>>;

    const messageConfirm = 'confirmation ?';
    describe('given a state with a confirm dialog', () => {
      beforeEach(() => {
        jest
          .spyOn(productCharacteristicsService, 'getCharacteristics$')
          .mockReturnValue(of([anyProductCharacteristic]));

        selectTranslateSpy.mockReturnValue(of(messageConfirm));

        jest
          .spyOn(
            productCharacteristicsReloadService,
            'initialCharacteristics',
            'get'
          )
          .mockReturnValue([anyProductCharacteristic]);

        dialogServiceConfirmSpy = (
          jest.spyOn(dialogService, 'confirm') as jest.SpyInstance<
            Observable<boolean>
          >
        ).mockReturnValue(of(true));
      });
      it('should call selectTranslate method', waitForAsync(() => {
        component.cancel();
        expect(selectTranslateSpy).toHaveBeenCalledWith(
          'sharedDialog.default-messages.confirm-loss-unsaved-changes'
        );
      }));

      it('should call confirm method of dialogService with a messageConfirm', waitForAsync(() => {
        component.cancel();
        expect(dialogServiceConfirmSpy).toHaveBeenCalledWith(messageConfirm);
      }));

      it('should call resetForm method of productCharacteristicsFormService', waitForAsync(() => {
        const resetFormSpy = jest.spyOn(
          productCharacteristicsFormService,
          'resetForm'
        );
        const expectedInitialCharacteristics = [anyProductCharacteristic];

        component.cancel();

        expect(resetFormSpy).toHaveBeenCalledWith(
          expectedInitialCharacteristics
        );
      }));
    });
    describe('given a state with a reject dialog', () => {
      beforeEach(() => {
        jest
          .spyOn(productCharacteristicsService, 'getCharacteristics$')
          .mockReturnValue(of([anyProductCharacteristic]));

        selectTranslateSpy.mockReturnValue(of(messageConfirm));

        dialogServiceConfirmSpy = (
          jest.spyOn(dialogService, 'confirm') as jest.SpyInstance<
            Observable<boolean>
          >
        ).mockReturnValue(of(false));
      });

      it("shouldn't call resetForm method of productCharacteristicsFormService", waitForAsync(() => {
        const resetFormSpy = jest.spyOn(
          productCharacteristicsFormService,
          'resetForm'
        );
        const expectedInitialCharacteristics = [anyProductCharacteristic];

        component.cancel();

        expect(resetFormSpy).not.toHaveBeenCalledWith(
          expectedInitialCharacteristics
        );
      }));
    });
  });

  describe('getCodeSizeWarnings', () => {
    it('should return a complete description of code size warnings', () => {
      const codeSizeWarnings: CodeSizeWarning[] = [
        { characteristicLabel: 'Couleur', expectedSize: 2 },
        { characteristicLabel: 'Famille article', expectedSize: 5 },
      ];
      jest
        .spyOn(productCharacteristicsFormHelper, 'checkCodesSize')
        .mockReturnValue(codeSizeWarnings);
      const translateSpy = jest.spyOn(translocoService, 'translate');

      component.getCodeSizeWarnings();

      expect(translateSpy).toHaveBeenCalledTimes(2);
      expect(translateSpy).toHaveBeenCalledWith(
        'code-structure-tab.validation.warnings.code-size',
        {
          expectedSize: codeSizeWarnings[0].expectedSize,
          characteristicLabel: codeSizeWarnings[0].characteristicLabel,
        },
        'product-parameter'
      );
      expect(translateSpy).toHaveBeenCalledWith(
        'code-structure-tab.validation.warnings.code-size',
        {
          expectedSize: codeSizeWarnings[1].expectedSize,
          characteristicLabel: codeSizeWarnings[1].characteristicLabel,
        },
        'product-parameter'
      );
    });

    it('should not return code size warnings', () => {
      const codeSizeWarnings: CodeSizeWarning[] = [];
      jest
        .spyOn(productCharacteristicsFormHelper, 'checkCodesSize')
        .mockReturnValue(codeSizeWarnings);
      const translateSpy = jest.spyOn(translocoService, 'translate');

      component.getCodeSizeWarnings();

      expect(translateSpy).not.toHaveBeenCalled();
    });
  });

  describe('getConfirmSaveMessage', () => {
    const confirmMessage = 'Veuillez confirmer';

    beforeEach(() => {
      selectTranslateSpy.mockReturnValue(of(confirmMessage));
    });

    it('should call selectTranslate method of TranslocoService', waitForAsync(() => {
      component.getConfirmSaveMessage$().subscribe(() => {
        expect(selectTranslateSpy).toHaveBeenCalledWith(
          'code-structure-tab.save.confirm-message',
          {},
          'product-parameter'
        );
      });
    }));
  });

  describe('getCharacteristicsReorderWarningMessage', () => {
    const warningMessage = 'Characteristics have been reordered';

    beforeEach(() => {
      selectTranslateSpy.mockReturnValue(of(warningMessage));
    });

    it('should call selectTranslate method of TranslocoService', waitForAsync(() => {
      component.getCharacteristicsReorderWarningMessage$().subscribe(() => {
        expect(selectTranslateSpy).toHaveBeenCalledWith(
          'code-structure-tab.validation.characteristics-have-been-reordered',
          {},
          'product-parameter'
        );
      });
    }));
  });

  describe('canDisplayVisualizationPanel$', () => {
    // Initialize the required observables before each test
    beforeEach(() => {
      // Initialize all required observables with default values
      component.selectCharacteristicsLength$ = of(CODE_SIZE_MAX);
      component.productCharacteristicsCodeSizesUnderZero$ = of(false);
      component.productCharacteristicsCodeSizesEmpty$ = of(false);

      // Recreate the canDisplayVisualizationPanel$ observable with the actual implementation
      component.canDisplayVisualizationPanel$ = combineLatest([
        component.selectCharacteristicsLength$,
        component.productCharacteristicsCodeSizesUnderZero$,
        component.productCharacteristicsCodeSizesEmpty$,
      ]).pipe(
        map(
          ([
            characteristicsLength,
            productCharacteristicsCodeSizesUnderZero,
            productCharacteristicsCodeSizesEmpty,
          ]) => {
            return (
              characteristicsLength <= CODE_SIZE_MAX &&
              !productCharacteristicsCodeSizesUnderZero &&
              !productCharacteristicsCodeSizesEmpty
            );
          }
        )
      );
    });

    it('should emit true when all conditions are met', waitForAsync(() => {
      // Set up the observables with values that should result in true
      component.selectCharacteristicsLength$ = of(CODE_SIZE_MAX);
      component.productCharacteristicsCodeSizesUnderZero$ = of(false);
      component.productCharacteristicsCodeSizesEmpty$ = of(false);

      // Recreate the canDisplayVisualizationPanel$ with the updated observables
      component.canDisplayVisualizationPanel$ = combineLatest([
        component.selectCharacteristicsLength$,
        component.productCharacteristicsCodeSizesUnderZero$,
        component.productCharacteristicsCodeSizesEmpty$,
      ]).pipe(
        map(
          ([
            characteristicsLength,
            productCharacteristicsCodeSizesUnderZero,
            productCharacteristicsCodeSizesEmpty,
          ]) => {
            return (
              characteristicsLength <= CODE_SIZE_MAX &&
              !productCharacteristicsCodeSizesUnderZero &&
              !productCharacteristicsCodeSizesEmpty
            );
          }
        )
      );

      // Subscribe to the canDisplayVisualizationPanel$ and verify it emits true
      component.canDisplayVisualizationPanel$.subscribe((result) => {
        expect(result).toBe(true);
      });
    }));

    it('should emit false when characteristicsLength is greater than CODE_SIZE_MAX', waitForAsync(() => {
      // Set up the observables with characteristicsLength > CODE_SIZE_MAX
      component.selectCharacteristicsLength$ = of(CODE_SIZE_MAX + 1);
      component.productCharacteristicsCodeSizesUnderZero$ = of(false);
      component.productCharacteristicsCodeSizesEmpty$ = of(false);

      // Recreate the canDisplayVisualizationPanel$ with the updated observables
      component.canDisplayVisualizationPanel$ = combineLatest([
        component.selectCharacteristicsLength$,
        component.productCharacteristicsCodeSizesUnderZero$,
        component.productCharacteristicsCodeSizesEmpty$,
      ]).pipe(
        map(
          ([
            characteristicsLength,
            productCharacteristicsCodeSizesUnderZero,
            productCharacteristicsCodeSizesEmpty,
          ]) => {
            return (
              characteristicsLength <= CODE_SIZE_MAX &&
              !productCharacteristicsCodeSizesUnderZero &&
              !productCharacteristicsCodeSizesEmpty
            );
          }
        )
      );

      // Subscribe to the canDisplayVisualizationPanel$ and verify it emits false
      component.canDisplayVisualizationPanel$.subscribe((result) => {
        expect(result).toBe(false);
      });
    }));

    it('should emit false when codeSizesUnderZero$ emits true', waitForAsync(() => {
      // Set up the observables with codeSizesUnderZero$ = true
      component.selectCharacteristicsLength$ = of(CODE_SIZE_MAX - 5);
      component.productCharacteristicsCodeSizesUnderZero$ = of(true);
      component.productCharacteristicsCodeSizesEmpty$ = of(false);

      // Recreate the canDisplayVisualizationPanel$ with the updated observables
      component.canDisplayVisualizationPanel$ = combineLatest([
        component.selectCharacteristicsLength$,
        component.productCharacteristicsCodeSizesUnderZero$,
        component.productCharacteristicsCodeSizesEmpty$,
      ]).pipe(
        map(
          ([
            characteristicsLength,
            productCharacteristicsCodeSizesUnderZero,
            productCharacteristicsCodeSizesEmpty,
          ]) => {
            return (
              characteristicsLength <= CODE_SIZE_MAX &&
              !productCharacteristicsCodeSizesUnderZero &&
              !productCharacteristicsCodeSizesEmpty
            );
          }
        )
      );

      // Subscribe to the canDisplayVisualizationPanel$ and verify it emits false
      component.canDisplayVisualizationPanel$.subscribe((result) => {
        expect(result).toBe(false);
      });
    }));

    it('should emit false when codeSizesEmpty$ emits true', waitForAsync(() => {
      // Set up the observables with codeSizesEmpty$ = true
      component.selectCharacteristicsLength$ = of(CODE_SIZE_MAX - 5);
      component.productCharacteristicsCodeSizesUnderZero$ = of(false);
      component.productCharacteristicsCodeSizesEmpty$ = of(true);

      // Recreate the canDisplayVisualizationPanel$ with the updated observables
      component.canDisplayVisualizationPanel$ = combineLatest([
        component.selectCharacteristicsLength$,
        component.productCharacteristicsCodeSizesUnderZero$,
        component.productCharacteristicsCodeSizesEmpty$,
      ]).pipe(
        map(
          ([
            characteristicsLength,
            productCharacteristicsCodeSizesUnderZero,
            productCharacteristicsCodeSizesEmpty,
          ]) => {
            return (
              characteristicsLength <= CODE_SIZE_MAX &&
              !productCharacteristicsCodeSizesUnderZero &&
              !productCharacteristicsCodeSizesEmpty
            );
          }
        )
      );

      // Subscribe to the canDisplayVisualizationPanel$ and verify it emits false
      component.canDisplayVisualizationPanel$.subscribe((result) => {
        expect(result).toBe(false);
      });
    }));
  });
});
