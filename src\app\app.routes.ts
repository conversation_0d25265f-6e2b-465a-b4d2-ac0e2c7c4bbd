import { Routes } from '@angular/router';
import {
  CodeModuleCommercial,
  DEFAULT_ROUTE,
  URL_PATHS,
} from '@gc/core/navigation/models';
import { authenticationGuard } from '@isagri-ng/security/authentication';
import { UserDefaultParamsGuard } from './core/guards/user-default-params/user-default-params.guard';
import {
  canNavigateToBusinessReview,
  isDeviceAllowed,
} from './core/navigation/feature';
import { canLoadFeature } from './core/navigation/feature/guards/can-access-feature/can-load-feature.guard';
import { canAccessModule } from './core/navigation/feature/guards/can-access-enabled-module/can-access-enabled-module.guard';
import { ALLOWED_DEVICES } from './navigation/device-access';

export const ROUTES: Routes = [
  {
    path: '',
    redirectTo: DEFAULT_ROUTE,
    pathMatch: 'full',
  },
  {
    path: '',
    canActivate: [authenticationGuard, UserDefaultParamsGuard],
    children: [
      {
        path: URL_PATHS.dunning,
        loadChildren: () =>
          import('./pages/dunning/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.reports,
        loadChildren: () =>
          import('./pages/report/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.product,
        loadChildren: () =>
          import('./pages/product/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.accounting,
        loadChildren: () =>
          import('./pages/accounting/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.stimulsoftViewer,
        loadChildren: () =>
          import('./pages/stimulsoft-viewers/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.businessReview,
        canActivate: [canNavigateToBusinessReview],
        loadChildren: () =>
          import('./pages/business-review/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.deb,
        canActivate: [canAccessModule(CodeModuleCommercial.Deb)],
        loadChildren: () =>
          import('@gc/features/deb/routes').then((m) => m.ROUTES),
      },
      {
        path: URL_PATHS.salesCommission,
        canLoad: [canLoadFeature(), isDeviceAllowed()],
        data: {
          feature: 'salesCommission',
          allowedDevices: ALLOWED_DEVICES.salesCommission, // rien tous auto, et si on precise on autorise que ce qui sont autorisé
        },
        loadChildren: () =>
          import('./features/sales/commissions/routes').then((m) => m.ROUTES),
      },
    ],
  },
  {
    path: URL_PATHS.login,
    loadChildren: () => import('./pages/login/routes').then((m) => m.ROUTES),
  },
  {
    path: URL_PATHS.invalidConfig,
    loadComponent: () =>
      import('./pages/invalid-config/invalid-config.component').then(
        (m) => m.InvalidConfigComponent
      ),
  },
  {
    path: URL_PATHS.unavailableModule,
    loadComponent: () =>
      import('./pages/unavailable-module/unavailable-module.component').then(
        (m) => m.UnavailableModuleComponent
      ),
  },
  {
    path: URL_PATHS.unavailableDevice,
    loadComponent: () =>
      import(
        './navigation/unavailable-device/unavailable-device.component'
      ).then((m) => m.UnavailableDeviceComponent),
  },
];
