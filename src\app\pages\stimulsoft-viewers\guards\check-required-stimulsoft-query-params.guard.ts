import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { ReportFamilies } from '@gc/shared/stimulsoft/models';

export const checkRequiredStimulsoftQueryParams: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  _: RouterStateSnapshot
) => {
  const router = inject(Router);

  const reportFamily: ReportFamilies = route.queryParams[
    StimulsoftQueryParamsKeys.REPORT_FAMILY
  ] as ReportFamilies;

  const reportId: ReportFamilies = route.queryParams[
    StimulsoftQueryParamsKeys.REPORT_ID
  ] as ReportFamilies;

  if (reportFamily === undefined || reportId === undefined) {
    router.navigate([URL_PATHS.invalidConfig]);
    throw new Error(
      'checkRequiredStimulsoftQueryParams: reportFamily or reportId is undefined'
    );
  }

  return true;
};
