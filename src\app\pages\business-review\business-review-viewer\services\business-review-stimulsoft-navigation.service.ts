import { Injectable, inject } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable, map } from 'rxjs';
import { QueryParamsKeys, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { BusinessReviewSupplementaryStimulsoftProperties } from '../models/business-review-supplementary-stimulsoft-properties.model';
import { StimulsoftPropertyKey } from '@gc/shared/stimulsoft/models';

const MIN_DATES_REQUIRED = 2;

@Injectable({
  providedIn: 'root',
})
export class BusinessReviewStimulsoftNavigationService {
  private readonly _activatedRoute = inject(ActivatedRoute);

  getBusinessReviewStimulsoftPropertiesFromQueryParams$(): Observable<BusinessReviewSupplementaryStimulsoftProperties> {
    return this._activatedRoute.queryParams.pipe(
      map((queryParams: Params) => {
        const dates = this._getDates(queryParams);
        return {
          companyIds: queryParams[QueryParamsKeys.COMPANIES_IDS] as string[],
          vatRate: this._getVatRate(queryParams),
          dateRange1startDate: dates[0],
          dateRange1endDate: dates[1],
          dateRange2startDate: dates[2] ?? null,
          dateRange2endDate: dates[3] ?? null,
          dateRange3startDate: dates[4] ?? null,
          dateRange3endDate: dates[5] ?? null,
          dateRange4startDate: dates[6] ?? null,
          dateRange4endDate: dates[7] ?? null,
          dateRange5startDate: dates[8] ?? null,
          dateRange5endDate: dates[9] ?? null,
          [StimulsoftPropertyKey.TREATMENT_ID]: queryParams[
            StimulsoftQueryParamsKeys.TREATMENT_ID
          ] as string,
        };
      })
    );
  }

  private _getDates(queryParams: Params): string[] {
    const datesJson = queryParams[QueryParamsKeys.DATES] as string;
    const dates = datesJson ? (JSON.parse(datesJson) as string[]) : undefined;
    if (!dates || dates.length < MIN_DATES_REQUIRED) {
      throw new Error(
        'Missing dates in query parameters: at least one date range is required.'
      );
    }
    return dates;
  }

  private _getVatRate(queryParams: Params): number | undefined {
    const vatFromQuery = queryParams[QueryParamsKeys.VAT_RATE] as string;
    const vatAsNumber = Number(vatFromQuery);
    return vatFromQuery && !isNaN(vatAsNumber) ? vatAsNumber : undefined;
  }
}
