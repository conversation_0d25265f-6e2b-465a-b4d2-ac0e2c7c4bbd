import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  RouterStateSnapshot,
} from '@angular/router';
import { HeaderTitleService } from '../../services/header-title.service';

export const setHeaderTitleGuard = (titleKey: string): CanActivateFn => {
  return (
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot,
    headerTitleService: HeaderTitleService = inject(HeaderTitleService)
  ) => {
    headerTitleService.updateCurrentTitle(titleKey);
    return true;
  };
};
