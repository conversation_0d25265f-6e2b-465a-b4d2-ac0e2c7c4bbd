import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { TokenManagerService } from '@gc/shared/api/authentication/data-access';
import {
  ClientContext,
  IClientContextDataProvider,
} from '@isagri-ng/core/configuration';
import {
  ApplicationInsightsParameter,
  IAppInsightActivatorService,
  MonitoringContext,
} from '@isagri-ng/monitoring';
import { IAuthentication } from '@isagri-ng/security';
import { map, Observable, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AppInsightActivatorService implements IAppInsightActivatorService {
  private readonly _authenticationService = inject(IAuthentication);
  private readonly _clientContextService = inject(IClientContextDataProvider);
  private readonly _httpClient = inject(HttpClient);
  private readonly _tokenManagerService = inject(TokenManagerService);

  public getMonitoringContext(): Observable<MonitoringContext> {
    return this._authenticationService.onLogin$.pipe(
      map(() =>
        this.fromClientContext(this._clientContextService.getClientContext())
      )
    );
  }

  public getApplicationInsightsParameter(): Observable<ApplicationInsightsParameter> {
    return this._tokenManagerService
      .getBearerTokenWhenAuthenticated()
      .pipe(
        switchMap((_bearer: string) =>
          this._httpClient.get<ApplicationInsightsParameter>(
            'v1/monitoring/parameters'
          )
        )
      );
  }

  public fromClientContext(clientContext: ClientContext): MonitoringContext {
    return {
      clientId: clientContext.clientId,
      domainId: clientContext.domainId,
      radicalApplication: clientContext.radicalApplication,
    } as MonitoringContext;
  }
}
