import { CustomerTypeEnumApi, Dunning<PERSON><PERSON> } from '@gc/shared/api/data-access';

export const anyDunningApi: DunningApi = {
  enterpriseCode: 'AZE0',
  customerCode: 'ZEER',
  enterpriseId: '1d84388d-a9d4-4f75-b32b-fc1fec8bb0de',
  customerId: '07dffc4c-8242-46f4-84b8-ca1ba2b6b316',
  customerInformations: {
    phoneNumber: undefined,
    postCode: undefined,
    townOrVillage: 'Paris',
    lastName: undefined,
    firstName: undefined,
    companyName: undefined,
    customerType: CustomerTypeEnumApi.Company,
    eMail: undefined,
  },
  totalAmountOutstanding: 123,
  dueDates: [],
};
