import { Component, Input } from '@angular/core';
import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'gc-no-navigation-container',
  templateUrl: './no-navigation-container.component.html',
  styleUrls: [
    './no-navigation-container.component.scss',
    './no-navigation-container-theme.component.scss',
  ],
  standalone: true,
  imports: [HeaderComponent],
})
export class NoNavigationContainerComponent {
  @Input() title!: string;
}
