import { DunningDueDateApi } from '@gc/shared/api/data-access';
export class InvoicesCountAdapter {
  public static fromDueDatesApi(dueDates: DunningDueDateApi[]): number {
    const defaultAmountInvoiceDueDate = 0;
    const invoiceIds = dueDates?.map(
      (dueDate: DunningDueDateApi) => dueDate.invoiceId
    );
    const uniqueInvoiceIds = invoiceIds?.filter(
      (invoiceId: string, i: number) => invoiceIds.indexOf(invoiceId) === i
    );
    return uniqueInvoiceIds?.length ?? defaultAmountInvoiceDueDate;
  }
}
