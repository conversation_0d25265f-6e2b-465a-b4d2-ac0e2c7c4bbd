<mat-card
  class="h-full gc-card-content-grow"
  *transloco="let t; read: 'deb.deb-declaration-table-component'">
  <mat-card-header>
    <mat-card-title>
      {{ t('title') }}
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    @if (
      (!inputDraftDeclarationsState().isLoading &&
        !!inputDraftDeclarationsState().data?.length) ||
      draftDeclarations().length
    ) {
      <div class="header">
        <div class="disabled-lines-info">
          {{ t('disabled-lines-info-part1') }}
          (<mat-slide-toggle
            [disabled]="true"
            [checked]="false"></mat-slide-toggle
          >)
          {{ t('disabled-lines-info-part2') }}
        </div>
        <div
          role="button"
          tabindex="0"
          class="add-line-container"
          (click)="onAddDebLineButtonClick()"
          (keydown.enter)="onAddDebLineButtonClick()">
          <mat-icon [color]="'primary'">post_add</mat-icon>
          <span class="add-line"> {{ t('add-line') }} </span>
        </div>
      </div>
      <table
        mat-table
        [dataSource]="draftDeclarations()"
        class="gc-table-cell-center">
        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            @if (
              draftDeclaration.missingFields?.length &&
              draftDeclaration.status === 'enabled'
            ) {
              <gc-action-container
                [iconeName]="'info'"
                [color]="'accent'"
                [tooltipLabel]="
                  draftDeclaration.missingFields | formatMissingFields
                " />
            }
          </td>
        </ng-container>
        <ng-container matColumnDef="nomenclature">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ t('table.nomenclature') }}
          </th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            {{ draftDeclaration['euNomenclature'] }}
          </td>
        </ng-container>
        <ng-container matColumnDef="destinationCountryCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ t('table.destination-country-code') }}
          </th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            {{ draftDeclaration['destinationCountryCode'] }}
          </td>
        </ng-container>
        <ng-container matColumnDef="invoicedAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ t('table.invoiced-amount') }}
          </th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            {{
              draftDeclaration['invoicedAmount']
                | amountWithCurrency: currency()
            }}
          </td>
        </ng-container>
        <ng-container matColumnDef="statisticalProcedureCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ t('table.statistical-procedure-code') }}
          </th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            {{ draftDeclaration['statisticalProcedureCode'] }}
          </td>
        </ng-container>

        <ng-container matColumnDef="vatNumber">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            {{ t('table.vat-number') }}
          </th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            {{ draftDeclaration['vatNumber'] }}
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
          <td mat-cell *matCellDef="let draftDeclaration; let rowIndex = index">
            <span class="actions-container">
              <gc-action-container
                [iconeName]="'visibility'"
                [tooltipLabel]="t('preview')"
                (click)="openDetailsDialog(draftDeclaration)" />

              <ng-container *transloco="let t; read: 'sharedAction'">
                <gc-action-container
                  [iconeName]="'mode_edit'"
                  [tooltipLabel]="t('update')"
                  (click)="openUpdateDialogAndUpdateToList(draftDeclaration)" />
              </ng-container>

              <gc-action-container>
                <mat-slide-toggle
                  action
                  color="primary"
                  [matTooltip]="
                    draftDeclaration.status === 'disabled'
                      ? t('disabled-line')
                      : t('enabled-line')
                  "
                  [checked]="draftDeclaration.status === 'enabled'"
                  (change)="
                    toggleDeclarationState(draftDeclaration.id)
                  "></mat-slide-toggle>
              </gc-action-container>
            </span>
          </td>
        </ng-container>

        <tr
          mat-header-row
          *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr
          mat-row
          *matRowDef="let draftDeclaration; columns: displayedColumns"
          [ngClass]="{
            'disabled-row': draftDeclaration.status === 'disabled',
          }"></tr>
      </table>
    } @else if (
      areFiltersApplied() &&
      !inputDraftDeclarationsState().isLoading &&
      !inputDraftDeclarationsState().data?.length
    ) {
      <div class="user-feedback-container">
        <p>{{ t('no-data') }}</p>
        <button
          type="button"
          color="primary"
          mat-raised-button
          [disabled]="filters() === null"
          (click)="onAddDebLineButtonClick()">
          {{ t('add-line') }}
        </button>
      </div>
    } @else if (inputDraftDeclarationsState().isLoading) {
      <div class="user-feedback-container">
        <gc-loader [label]="t('is-loading')" />
      </div>
    } @else if (!areFiltersApplied()) {
      <div class="user-feedback-container">
        <p>{{ t('need-apply-filter') }}</p>
      </div>
    }
  </mat-card-content>
</mat-card>
