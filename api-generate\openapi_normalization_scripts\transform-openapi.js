const fs = require('fs');
const path = require('path');
const { Glob } = require('glob');
const {
    removeGlobalHeadersFromPathsParameters,
} = require('./openapi_normalizer_functions/remove-global-headers-from-paths-parameters.js');
const {
    addDomainInPathsTags,
} = require('./openapi_normalizer_functions/add-domain-in-paths-tags.js');
const {
    normalizePaths,
} = require('./openapi_normalizer_functions/normalize-paths.js');

const OPENAPI_FILES_PATH = path
    .resolve(__dirname, '../openapi_files_last_version/**/*.json')
    .replace(/\\/g, '/');

const OPENAPI_NORMALIZED_FILES_FOLDER = path.resolve(
    __dirname,
    '../openapi_files_last_version_normalized'
);

let foundFiles = false;

const g = new Glob(OPENAPI_FILES_PATH, { withFileTypes: true });
g.stream()
    .on('data', async (file) => {
        if (file.isFile() && file.name.endsWith('.json')) {
            foundFiles = true;
            try {
                fs.readFile(file.fullpath(), 'utf8', (err, data) => {
                    if (err) {
                        throw new Error(err);
                    }
                    const [_, domain, version] = path
                        .parse(file.name)
                        .name.split('_');
                    let openapiObj = JSON.parse(data);
                    openapiObj = addDomainInPathsTags(openapiObj, domain);
                    openapiObj =
                        removeGlobalHeadersFromPathsParameters(openapiObj);
                    openapiObj = normalizePaths(openapiObj, domain, version);

                    const outputFilePath =
                        OPENAPI_NORMALIZED_FILES_FOLDER + '/' + file.name;
                    fs.writeFile(
                        outputFilePath,
                        JSON.stringify(openapiObj, null, 2),
                        (err) => {
                            if (err) {
                                console.error(
                                    "Erreur lors de l'écriture du fichier :",
                                    err
                                );
                                process.exit(1);
                            }
                            console.log(
                                `Le fichier transformé a été généré avec succès : ${file.name}`
                            );
                        }
                    );
                });
            } catch (error) {
                console.error(`Error reading or parsing ${file.name}:`, error);
            }
        }
    })
    .on('end', () => {
        if (!foundFiles) {
            console.error(
                '❌ No JSON files found! You need to copy/paste the openapi.json files from stoplight'
            );
        }
    });
