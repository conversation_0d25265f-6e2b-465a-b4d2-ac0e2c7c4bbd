/* eslint-disable max-lines-per-function */
import { createReducer, on } from '@ngrx/store';
import { monthlyTreatmentHistoryActions } from '../actions/monthly-treatment-history.actions';
import { MonthlyTreatmentHistoryState } from '../models/monthly-treatment-history-state.model';
import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { LoadingStatus } from '@gc/shared/models';

const initialMonthlyEditions: {
  availableMonthlyEditions: null;
  availableMonthlyEditionsLoadingStatus: LoadingStatus;
  selectedMonthlyEditions: MonthlyTreatmentEditionsEnum[];
} = {
  availableMonthlyEditions: null,
  availableMonthlyEditionsLoadingStatus: 'NOT_LOADED',
  selectedMonthlyEditions: [],
};

const initialMonths: {
  enclosedMonthsOfSelectedYear: Date[];
  enclosedMonthsLoadingStatus: LoadingStatus;
  selectedEnclosedMonth: null;
} = {
  enclosedMonthsOfSelectedYear: [],
  enclosedMonthsLoadingStatus: 'NOT_LOADED',
  selectedEnclosedMonth: null,
};

export const initialState: MonthlyTreatmentHistoryState = {
  enclosedYearsLoadingStatus: 'NOT_LOADED',
  uncloseSelectedMonthStatus: 'NOT_PROCESSED',
  ...initialMonthlyEditions,
  ...initialMonths,
};

export const monthlyTreatmentHistoryReducer = createReducer(
  initialState,
  on(
    monthlyTreatmentHistoryActions.changeSelectedEnclosedMonth,
    (_state, { month }): MonthlyTreatmentHistoryState => {
      return {
        ..._state,
        selectedEnclosedMonth: month,
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.changeSelectedEnclosedYear,
    (_state, { year }): MonthlyTreatmentHistoryState => {
      return {
        ..._state,
        selectedEnclosedYear: year,
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears,
    (_state): MonthlyTreatmentHistoryState => {
      return {
        ...initialState,
        enclosedYearsLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYearsSuccess,
    (_state, { years }): MonthlyTreatmentHistoryState => {
      const latestYear = years.length ? Math.max(...years) : null;
      return {
        ...initialState,
        enclosedYears: years,
        selectedEnclosedYear: latestYear,
        enclosedYearsLoadingStatus: 'LOADED',
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonths,
    (_state): MonthlyTreatmentHistoryState => {
      return {
        ..._state,
        ...initialMonthlyEditions,
        ...initialMonths,
        enclosedMonthsLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonthsSuccess,
    (_state, { months }): MonthlyTreatmentHistoryState => {
      const latestMonth = months.length
        ? [...months].sort((d1, d2) => d2.getTime() - d1.getTime())[0]
        : null;
      return {
        ..._state,
        enclosedMonthsOfSelectedYear: months,
        selectedEnclosedMonth: latestMonth,
        enclosedMonthsLoadingStatus: 'LOADED',
        ...initialMonthlyEditions,
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions,
    (_state): MonthlyTreatmentHistoryState => {
      return {
        ..._state,
        ...initialMonthlyEditions,
        availableMonthlyEditionsLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditionsSuccess,
    (_state, { availableMonthlyEditions }): MonthlyTreatmentHistoryState => {
      const selectedEditionsDefault: MonthlyTreatmentEditionsEnum[] = [];

      availableMonthlyEditions?.forEach(
        (availableMonthlyEdition: MonthlyTreatmentAvailableEdition) => {
          if (availableMonthlyEdition?.isAvailable) {
            selectedEditionsDefault.push(availableMonthlyEdition.id);
          }
        }
      );

      return {
        ..._state,
        availableMonthlyEditions,
        selectedMonthlyEditions: selectedEditionsDefault,
        availableMonthlyEditionsLoadingStatus: 'LOADED',
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.toggleMonthlyTreatmentEditionSelection,
    (_state, { edition }): MonthlyTreatmentHistoryState => {
      const newSelectedMonthlyEditions = [..._state.selectedMonthlyEditions];
      const index = newSelectedMonthlyEditions.indexOf(edition);
      if (index === -1) {
        newSelectedMonthlyEditions.push(edition);
      } else {
        newSelectedMonthlyEditions.splice(index, 1);
      }
      return {
        ..._state,
        selectedMonthlyEditions: newSelectedMonthlyEditions,
      };
    }
  ),
  on(
    monthlyTreatmentHistoryActions.uncloseSelectedMonth,
    (_state): MonthlyTreatmentHistoryState => ({
      ..._state,
      uncloseSelectedMonthStatus: 'IN_PROGRESS',
    })
  ),
  on(
    monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess,
    (_state): MonthlyTreatmentHistoryState => ({
      ...initialState,
    })
  )
);
