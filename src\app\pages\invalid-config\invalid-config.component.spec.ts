import { MockDirective } from 'ng-mocks';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { InvalidConfigComponent } from './invalid-config.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { CompanyService } from '@gc/company/data-access';
import { UserLocalStorageService } from '@gc/user/data-access';
import { Company } from '@gc/company/models';
import { Observable, of } from 'rxjs';
import { waitForAsync } from '@angular/core/testing';
import { Router } from '@angular/router';

describe('InvalidConfigComponent', () => {
  let spectator: Spectator<InvalidConfigComponent>;
  let component: InvalidConfigComponent;

  const companyA: Company = {
    id: '*********',
    code: 'companyA',
  };
  const companyB: Company = {
    id: '*********',
    code: 'companyB',
  };

  const createComponent = createComponentFactory({
    component: InvalidConfigComponent,
    mocks: [CompanyService, UserLocalStorageService, Router],
    declarations: [MockDirective(TranslocoDirective)],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });

  describe('ngOnInit', () => {
    let companiesGetterSpy: jest.SpyInstance<Observable<Company[]>>;

    beforeEach(() => {
      companiesGetterSpy = jest.spyOn(
        spectator.inject(CompanyService),
        'companies$',
        'get'
      );
    });
    describe('given a state where companies$ getter of CompanyService return a list of one company', () => {
      beforeEach(() => {
        companiesGetterSpy.mockReturnValue(of([companyA]));
      });
      it('should set isSingleCompany to true and set companies property', waitForAsync(() => {
        component.ngOnInit();
        expect(component.isSingleCompany).toBe(true);
        expect(component.companies).toStrictEqual([companyA]);
      }));
    });
    describe('given a state where companies$ getter of CompanyService return a list of more than one company', () => {
      beforeEach(() => {
        companiesGetterSpy.mockReturnValue(of([companyA, companyB]));
      });
      it('should set isSingleCompany to false and set companies property', waitForAsync(() => {
        component.ngOnInit();
        expect(component.isSingleCompany).toBe(false);
        expect(component.companies).toStrictEqual([companyA, companyB]);
      }));
    });
  });

  describe('confirmDefaultConfig', () => {
    let userLocalStorageServiceSetSpy: jest.SpyInstance;
    let navigateSpy: jest.SpyInstance;

    beforeEach(() => {
      userLocalStorageServiceSetSpy = jest.spyOn(
        spectator.inject(UserLocalStorageService),
        'set'
      );
      navigateSpy = jest.spyOn(spectator.inject(Router), 'navigate');
    });

    describe('given a state where isSingleCompany is true', () => {
      beforeEach(() => {
        component.isSingleCompany = true;
        component.companies = [companyA];
        component.defaultDateControl.setValue(new Date('2025-01-01'));
      });

      it('should call set method of UserLocalStorageService with default params and call navigate method of router', () => {
        component.confirmDefaultConfig();
        expect(userLocalStorageServiceSetSpy).toHaveBeenCalledWith({
          defaultCompanyId: companyA.id,
          defaultDate: new Date('2025-01-01'),
        });
        expect(navigateSpy).toHaveBeenCalledWith(['']);
      });
    });

    describe('given a state where isSingleCompany is false', () => {
      beforeEach(() => {
        component.isSingleCompany = false;
        component.companyIdControl.setValue(companyA.id);
        component.defaultDateControl.setValue(new Date('2025-01-01'));
      });

      it('should call set method of UserLocalStorageService with default params and call navigate method of router', () => {
        component.confirmDefaultConfig();
        expect(userLocalStorageServiceSetSpy).toHaveBeenCalledWith({
          defaultCompanyId: companyA.id,
          defaultDate: new Date('2025-01-01'),
        });
        expect(navigateSpy).toHaveBeenCalledWith(['']);
      });
    });
  });
});
