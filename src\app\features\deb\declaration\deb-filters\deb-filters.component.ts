import {
  Component,
  ChangeDetectionStrategy,
  inject,
  output,
  input,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ReactiveFormsModule, NonNullableFormBuilder } from '@angular/forms';
import { TranslocoDirective } from '@jsverse/transloco';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { CapitalizePipe } from '@gc/shared/ui';
import { Filters } from '@gc/core/deb/domains/models';

@Component({
  selector: 'gc-deb-filters',
  standalone: true,
  styleUrl: './deb-filters.component.scss',
  imports: [
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslocoDirective,
    TranslocoDatePipe,
    CapitalizePipe,
  ],
  templateUrl: './deb-filters.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebFiltersComponent {
  private readonly _fb = inject(NonNullableFormBuilder);
  declarationMonth = input.required<Date>();
  applyFilters = output<Partial<Filters>>();

  debFiltersFG = this._fb.group({
    deliveryNoteRMAFC: this._fb.control(true),
    invoicesCreditNotesFC: this._fb.control(true),
  });

  constructor() {
    this.debFiltersFG.valueChanges.pipe(takeUntilDestroyed()).subscribe(() => {
      this._forceOneCheckboxToBeChecked();
    });
  }

  applyFiltersSubmit(): void {
    const { deliveryNoteRMAFC, invoicesCreditNotesFC } =
      this.debFiltersFG.value;
    this.applyFilters.emit({
      includeDeliveryNotes: deliveryNoteRMAFC,
      includeInvoices: invoicesCreditNotesFC,
    });
  }

  private _forceOneCheckboxToBeChecked(): void {
    if (
      !this.debFiltersFG.get('deliveryNoteRMAFC')!.value &&
      !this.debFiltersFG.get('invoicesCreditNotesFC')!.value
    ) {
      this.debFiltersFG.get('deliveryNoteRMAFC')!.setValue(true);
    }
  }
}
