import { Injectable, inject } from '@angular/core';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { VatService } from '@gc/vat/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { Warehouse } from '@gc/warehouse/models';
import { BehaviorSubject, Observable, forkJoin, of, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BusinessReviewService {
  private readonly _vatService = inject(VatService);
  private readonly _warehouseService = inject(WarehouseService);
  private readonly _fiscalYearService = inject(FiscalYearService);

  public warehouses$$ = new BehaviorSubject<Warehouse[]>([]);

  public vatRates$$ = new BehaviorSubject<number[]>([]);

  getData(companyIds: string[]): Observable<{
    vatRates: number[];
    warehouses: Warehouse[];
  }> {
    return forkJoin({
      vatRates: this._getVatListByCompanies(companyIds),
      warehouses: this._getWarehouses(companyIds),
    }).pipe(
      tap(({ vatRates, warehouses }) => {
        this.vatRates$$.next(vatRates);
        this.warehouses$$.next(warehouses);
      })
    );
  }

  getFiscalYears(companyIds: string[]): Observable<FiscalYearDateRange[]> {
    if (companyIds.length === 0) {
      return of([]);
    }
    return this._fiscalYearService.getSharedFiscalYearsDateRanges(companyIds);
  }

  private _getWarehouses(companyIds: string[]): Observable<Warehouse[]> {
    if (companyIds.length === 0) {
      return of([]);
    }
    return this._warehouseService.getWarehouses(companyIds);
  }

  private _getVatListByCompanies(companyIds: string[]): Observable<number[]> {
    if (companyIds.length === 0) {
      return of([]);
    }
    return this._vatService.getDeduplicatedRatesValuesByCompanies(companyIds);
  }
}
