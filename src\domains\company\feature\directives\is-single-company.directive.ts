import {
  ChangeDetectorRef,
  DestroyRef,
  Directive,
  Input,
  OnInit,
  TemplateRef,
  ViewContainerRef,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CompanyService } from '@gc/company/data-access';

@Directive({
  selector: '[gcIsSingleCompany]',
  standalone: true,
})
export class IsSingleCompanyDirective implements OnInit {
  private readonly _templateRef = inject<TemplateRef<unknown>>(TemplateRef);
  private readonly _viewContainer = inject(ViewContainerRef);
  private readonly _companyService = inject(CompanyService);
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _destroyRef = inject(DestroyRef);

  private _hasView = false;

  @Input() set gcIsSingleCompany(_: string) {
    // par défaut, on considère que le dossier est multi entreprise
    this._displayComponent(false);
  }

  ngOnInit(): void {
    this._companyService.isSingleCompany$
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((isSingleCompany: boolean) => {
        this._displayComponent(isSingleCompany);
      });
  }

  private _displayComponent(isSingleCompany: boolean): void {
    if (!isSingleCompany && !this._hasView) {
      this._viewContainer.createEmbeddedView(this._templateRef);
      this._hasView = true;
    } else if (isSingleCompany && this._hasView) {
      this._viewContainer.clear();
      this._hasView = false;
    }
    this._cdr.markForCheck();
  }
}
