import { DueDateDocumentTypeEnumApi } from '@gc/shared/api/data-access';
import { DueDateDocumentType } from '@gc/due-date/models';

export class DueDateDocumentTypeAdapter {
  public static fromApi(
    documentType: DueDateDocumentTypeEnumApi | unknown
  ): DueDateDocumentType {
    if (documentType === DueDateDocumentTypeEnumApi.Invoice) {
      return DueDateDocumentType.INVOICE;
    }
    return DueDateDocumentType.UNKNOWN;
  }
}
