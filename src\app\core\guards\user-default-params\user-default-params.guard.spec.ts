import { DateAdapter } from '@gc/shared/utils';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { UserDefaultParamsGuard } from './user-default-params.guard';
import { URL_PATHS } from '@gc/core/navigation/models';
import { QueryParamsKeys } from '@gc/shared/models';
import {
  UserLocalStorageService,
  UserNavigationService,
  UserStoreService,
  userFixture,
} from '@gc/user/data-access';
import { User } from '@gc/user/models';
import { waitForAsync } from '@angular/core/testing';

describe('UserDefaultParamsGuard', () => {
  let spectator: SpectatorService<UserDefaultParamsGuard>;
  let userStoreService: UserStoreService;

  let userLocalStorageService: UserLocalStorageService;
  let userNavigationService: UserNavigationService;
  let router: Router;
  let navigateSpy: jest.SpyInstance<Promise<boolean>>;
  let getUserDefaultDataFromQueryParamsSpy: jest.SpyInstance<User | undefined>;
  let updateUserSpy: jest.SpyInstance<void>;
  let setSpy: jest.SpyInstance<void>;
  let getSpy: jest.SpyInstance<User | undefined>;

  const createService = createServiceFactory({
    service: UserDefaultParamsGuard,
    mocks: [Router],
  });

  beforeEach(() => {
    spectator = createService();
    userLocalStorageService = spectator.inject(UserLocalStorageService);
    userNavigationService = spectator.inject(UserNavigationService);
    router = spectator.inject(Router);
    userStoreService = spectator.inject(UserStoreService);
    navigateSpy = jest.spyOn(router, 'navigate');
  });

  describe('canActivate', () => {
    const user: User = userFixture();
    const companyId = user.defaultCompanyId;
    const date = DateAdapter.dateToStringAPI(user.defaultDate as Date);

    describe(`given a state where the route has ${QueryParamsKeys.DEFAULT_COMPANY_ID} and ${QueryParamsKeys.DEFAULT_DATE} in the queryParams`, () => {
      const mockRoute = {
        queryParams: {
          [QueryParamsKeys.DEFAULT_COMPANY_ID]: companyId,
          [QueryParamsKeys.DEFAULT_DATE]: date,
          anyQueryParam1: 'anyValue',
          anyQueryParam2: 'anyValue',
          anyQueryParam3: 'anyValue',
        },
      } as unknown as ActivatedRouteSnapshot;

      beforeEach(() => {
        getUserDefaultDataFromQueryParamsSpy = jest
          .spyOn(userNavigationService, 'getUserDefaultDataFromQueryParams')
          .mockReturnValue(user);
        updateUserSpy = jest.spyOn(userStoreService, 'updateUser');
        setSpy = jest.spyOn(userLocalStorageService, 'set');
      });

      it('should store the user in localstorage', () => {
        spectator.service.canActivate(mockRoute);

        expect(getUserDefaultDataFromQueryParamsSpy).toHaveBeenCalledWith(
          mockRoute.queryParams
        );
        expect(setSpy).toHaveBeenCalledWith(user);
      });

      it('should store the user in UserStoreService', waitForAsync(() => {
        spectator.service.canActivate(mockRoute);
        expect(updateUserSpy).toHaveBeenCalledWith(user);
        userStoreService.user$.subscribe((result) => {
          expect(result).toStrictEqual(user);
        });
      }));

      it('should return the true', () => {
        const result = spectator.service.canActivate(mockRoute);
        expect(result).toEqual(true);
      });

      it(`should not navigate`, () => {
        spectator.service.canActivate(mockRoute);
        expect(navigateSpy).not.toHaveBeenCalled();
      });
    });

    describe(`given a state where the route has not ${QueryParamsKeys.DEFAULT_COMPANY_ID} and ${QueryParamsKeys.DEFAULT_DATE} in the queryParams`, () => {
      const mockRoute = {
        queryParams: {
          anyQueryParam1: 'anyValue',
          anyQueryParam2: 'anyValue',
          anyQueryParam3: 'anyValue',
        },
      } as unknown as ActivatedRouteSnapshot;

      beforeEach(() => {
        getUserDefaultDataFromQueryParamsSpy = jest.spyOn(
          userNavigationService,
          'getUserDefaultDataFromQueryParams'
        );
      });

      describe('and user is not retrieved in the local storage so getUserDataFromLocalStorage returns undefined', () => {
        beforeEach(() => {
          getSpy = jest
            .spyOn(userLocalStorageService, 'get')
            .mockReturnValue(undefined);
          updateUserSpy = jest.spyOn(userStoreService, 'updateUser');
          setSpy = jest.spyOn(userLocalStorageService, 'set');
        });

        it('should try to get the user from the localStorage', () => {
          spectator.service.canActivate(mockRoute);

          expect(setSpy).not.toHaveBeenCalled();
          expect(getUserDefaultDataFromQueryParamsSpy).toHaveBeenCalledWith(
            mockRoute.queryParams
          );
          expect(getSpy).toHaveBeenCalled();
        });

        it('should store the user in UserStoreService', waitForAsync(() => {
          spectator.service.canActivate(mockRoute);
          expect(updateUserSpy).not.toHaveBeenCalled();
          userStoreService.user$.subscribe((result) => {
            expect(result).toBeUndefined();
          });
        }));

        it('should return the false', () => {
          const result = spectator.service.canActivate(mockRoute);
          expect(result).toEqual(false);
        });

        it(`should navigate to ${URL_PATHS.invalidConfig}`, () => {
          spectator.service.canActivate(mockRoute);
          expect(navigateSpy).toHaveBeenCalledWith([URL_PATHS.invalidConfig]);
        });
      });

      describe('and user is stored in the local storage so userLocalStorageService can return the user', () => {
        beforeEach(() => {
          getSpy = jest
            .spyOn(userLocalStorageService, 'get')
            .mockReturnValue(user);
          updateUserSpy = jest.spyOn(userStoreService, 'updateUser');
        });

        it('should retrieve the user from the localStorage', () => {
          spectator.service.canActivate(mockRoute);

          expect(getUserDefaultDataFromQueryParamsSpy).toHaveBeenCalledWith(
            mockRoute.queryParams
          );
          expect(setSpy).not.toHaveBeenCalled();
          expect(getSpy).toHaveBeenCalled();
        });

        it('should store the user in UserStoreService', waitForAsync(() => {
          spectator.service.canActivate(mockRoute);
          expect(updateUserSpy).toHaveBeenCalledWith(user);
          userStoreService.user$.subscribe((result) => {
            expect(result).toStrictEqual(user);
          });
        }));

        it('should return true', () => {
          const result = spectator.service.canActivate(mockRoute);
          expect(result).toEqual(true);
        });

        it(`should not navigate`, () => {
          spectator.service.canActivate(mockRoute);
          expect(navigateSpy).not.toHaveBeenCalled();
        });
      });
    });
  });
});
