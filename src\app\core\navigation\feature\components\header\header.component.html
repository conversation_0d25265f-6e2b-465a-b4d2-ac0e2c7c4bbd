<mat-toolbar color="primary">
  <div data-testid="header-container" class="container">
    <div class="left-container">
      <ng-content select="[openMenuButton]" />
      <img src="./assets/img/icone-533d0542.svg" alt="logo" />
    </div>
    <div class="center-container">
      <div>{{ this.title$ | ngrxPush }}</div>
    </div>
    <div class="right-container">
      <ng-content select="[openProfileButton]" />
    </div>
  </div>
</mat-toolbar>
<div class="sub-header">
  <ng-content select="[subHeader]" />
</div>
