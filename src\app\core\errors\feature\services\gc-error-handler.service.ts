import { HttpErrorResponse } from '@angular/common/http';
import { <PERSON>rror<PERSON>andler, Injectable, inject } from '@angular/core';
import { ENVIRONMENT } from '@gc/environment';
import { ITraceService } from '@isagri-ng/core/diagnostics';

@Injectable({ providedIn: 'root' })
export class GcErrorHandlerService implements ErrorHandler {
  private readonly _traceService = inject(ITraceService);

  handleError(error: Error | unknown): void {
    if (error instanceof Error && !(error instanceof HttpErrorResponse)) {
      this._traceService.logErrorException(error);
    }

    // Default behaviour of Angular ErrorHandler is to call console.error.
    // Here we override the implementation of handleError but we want to keep original behaviour (ie console.error) and we cannont disable this rule globally.
    if (!ENVIRONMENT.production) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  }
}
