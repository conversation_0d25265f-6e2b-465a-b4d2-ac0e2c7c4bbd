import { CustomerTypeEnumApi } from '@gc/shared/api/data-access';
import { DunningCustomerTypeAdapter } from './dunning-customer-type.adapter';

describe('DunningCustomerInformationsAdapter', () => {
  describe('given all CustomerType enum values', () => {
    describe('when transform method is called on each level', () => {
      it('should succeed to transform and not throw error', () => {
        const levelEnumValues: CustomerTypeEnumApi[] = Object.values(
          CustomerTypeEnumApi
        ) as CustomerTypeEnumApi[];
        const levelEnumNumberValues = levelEnumValues.filter(
          (v) => !isNaN(Number(v))
        );

        levelEnumNumberValues.forEach((value: CustomerTypeEnumApi) =>
          expect(DunningCustomerTypeAdapter.fromApi(value)).not.toBeNull()
        );
      });
    });
  });
});
