import { inject, Injectable, WritableSignal } from '@angular/core';
import { ResourceState } from '@gc/core/shared/store';
import { handleStoreLoading } from '@gc/core/shared/store/operators';
import { RepresentativesStore, RepresentativesStoreEnum } from '../store';
import { LoadRepresentativesUseCase } from '../use-cases';
import { AutoStartLoading } from '@gc/core/shared/store/decorators';
import { Representative } from '../../domains/models';

@Injectable()
export class RepresentativesFacade {
  private readonly store: RepresentativesStore = inject(RepresentativesStore);
  private readonly loadRepresentatives = inject(LoadRepresentativesUseCase);

  getRepresentativesForCurrentCompany() {
    return this.store.get(RepresentativesStoreEnum.REPRESENTATIVES);
  }

  @AutoStartLoading(RepresentativesStoreEnum.REPRESENTATIVES)
  loadRepresentativesForCompany(companyId: string) {
    this.loadRepresentatives
      .for(companyId)
      .pipe(
        handleStoreLoading(this.store, RepresentativesStoreEnum.REPRESENTATIVES)
      )
      .subscribe();
  }

  getSelectedRepresentative(): WritableSignal<ResourceState<Representative>> {
    return this.store.get(RepresentativesStoreEnum.SELECTED_REPRESENTATIVE);
  }

  clearRepresentative() {
    this.store.clear(RepresentativesStoreEnum.SELECTED_REPRESENTATIVE);
  }

  setSelectedRepresentative(representative: Representative): void {
    this.store.update(RepresentativesStoreEnum.SELECTED_REPRESENTATIVE, {
      data: representative,
      isLoading: false,
      status: 'Success',
    });
  }
}
