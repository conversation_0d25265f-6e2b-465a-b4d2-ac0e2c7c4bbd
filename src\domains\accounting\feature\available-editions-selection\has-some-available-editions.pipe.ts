import { Pipe, PipeTransform } from '@angular/core';
import { MonthlyTreatmentAvailableEdition } from '@gc/accounting/models';

@Pipe({
  name: 'hasSomeAvailableEditions',
  standalone: true,
})
export class HasSomeAvailableEditionsPipe implements PipeTransform {
  transform(
    availableEditions: MonthlyTreatmentAvailableEdition[] | null | undefined
  ): boolean {
    if (!availableEditions) return false;
    return availableEditions?.some(
      (edition: MonthlyTreatmentAvailableEdition) => edition.isAvailable
    );
  }
}
