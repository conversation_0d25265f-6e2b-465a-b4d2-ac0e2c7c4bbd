import { provideHttpClientTesting } from '@angular/common/http/testing';
import { LegacyApiService, CompanyApi } from '@gc/shared/api/data-access';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import {
  BehaviorSubject,
  Observable,
  of,
  skip,
  take,
  tap,
  throwError,
} from 'rxjs';
import { companiesApiFixture } from '../fixtures/company-api.fixture';
import { CompanyService } from './company.service';
import { CompanyAdapter } from '../adapters/company.adapter';
import { waitForAsync } from '@angular/core/testing';
import {
  companiesFixture,
  companiesIdFixture,
  Company,
} from '@gc/company/models';
import { LoadingStatus } from '@gc/shared/models';

describe('CompanyService', () => {
  const mockLegacyApiService: Partial<LegacyApiService> = {
    companyGetCompanies1: jest.fn(),
  };

  let spectator: SpectatorService<CompanyService>;
  let service: CompanyService;

  const createService = createServiceFactory({
    service: CompanyService,
    providers: [
      provideHttpClientTesting(),
      {
        provide: LegacyApiService,
        useValue: mockLegacyApiService,
      },
    ],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('getters', () => {
    describe('companies$ getter', () => {
      const [company1, company2] = companiesFixture();
      let loadCompaniesSpy: jest.SpyInstance<void>;

      beforeEach(() => {
        loadCompaniesSpy = jest.spyOn(service as any, '_loadCompanies');
      });
      describe('given a state where userCompaniesLoadingStatus$$ value is NOT_LOADED', () => {
        beforeEach(() => {
          loadCompaniesSpy.mockImplementation(() => {
            (
              (service as any)._userCompanies$$ as BehaviorSubject<Company[]>
            ).next([company1, company2]);

            (
              (service as any)
                ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
            ).next('LOADED');
          });
        });
        it("should call loadCompanies internal method and return the user's companies when status is LOADED", waitForAsync(() => {
          service.companies$.subscribe((companies) => {
            expect(companies).toStrictEqual([company1, company2]);
            expect(loadCompaniesSpy).toHaveBeenCalled();
          });
        }));
      });

      describe('given a state where userCompaniesLoadingStatus$$ value is LOADED', () => {
        beforeEach(() => {
          (
            (service as any)
              ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          ).next('LOADED');
          (
            (service as any)._userCompanies$$ as BehaviorSubject<Company[]>
          ).next([company1, company2]);
        });
        it("should not call loadCompanies internal method and return the user's companies", waitForAsync(() => {
          service.companies$.subscribe((companies) => {
            expect(companies).toStrictEqual([company1, company2]);
            expect(loadCompaniesSpy).not.toHaveBeenCalled();
          });
        }));
      });

      describe('given a state where userCompaniesLoadingStatus$$ value is IN_PROGRESS', () => {
        beforeEach(() => {
          (
            (service as any)
              ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          ).next('IN_PROGRESS');
        });

        it("should not call loadCompanies internal method and return the user's companies when LOADED", async () => {
          await new Promise<void>((done) => {
            service.companies$.subscribe((companies: Company[]) => {
              expect(companies).toStrictEqual([company1, company2]);

              done();
            });
            // Simulate companies retrieval and update
            (
              (service as any)._userCompanies$$ as BehaviorSubject<Company[]>
            ).next([company1, company2]);
            // Simulate loading status update
            (
              (service as any)
                ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
            ).next('LOADED');
          }).then();
        });
      });
    });

    describe('companiesIds$ getter', () => {
      describe('given a state where companies$ getter return a Company list', () => {
        const [company1, company2] = companiesFixture();

        beforeEach(() => {
          jest
            .spyOn(service, 'companies$', 'get')
            .mockReturnValue(of([company1, company2]));
        });
        it('should a list of companies ids', waitForAsync(() => {
          service.companiesIds$.subscribe((ids) => {
            expect(ids[0]).toBe(company1.id);
            expect(ids[1]).toBe(company2.id);
          });
        }));
      });
    });

    describe('isSingleCompany$ getter', () => {
      const [company1, company2] = companiesFixture();
      describe('given a state where companies$ getter return a list of 2 Company', () => {
        beforeEach(() => {
          jest
            .spyOn(service, 'companies$', 'get')
            .mockReturnValue(of([company1, company2]));
        });
        it('should return false', waitForAsync(() => {
          service.isSingleCompany$.subscribe((isSingleCompany) => {
            expect(isSingleCompany).toBe(false);
          });
        }));
      });

      describe('given a state where companies$ getter return a list of 1 Company', () => {
        beforeEach(() => {
          jest
            .spyOn(service, 'companies$', 'get')
            .mockReturnValue(of([company1]));
        });
        it('should return true', waitForAsync(() => {
          service.isSingleCompany$.subscribe((isSingleCompany) => {
            expect(isSingleCompany).toBe(true);
          });
        }));
      });
    });

    describe('companyIdIfSingleCompany$ getter', () => {
      describe('given a state where companiesIds$ getter returns a list of 1 company id', () => {
        const [companyId] = companiesIdFixture();
        beforeEach(() => {
          jest
            .spyOn(service, 'companiesIds$', 'get')
            .mockReturnValue(of([companyId]));
        });

        it('should return an obervable with the company id', waitForAsync(() => {
          spectator.service.companyIdIfSingleCompany$.subscribe((value) => {
            expect(value).toEqual(companyId);
          });
        }));
      });

      describe('given a state where companies getter of CompanyService returns a list of more than 1 company id', () => {
        const companiesIds = companiesIdFixture();
        beforeEach(() => {
          jest
            .spyOn(service, 'companiesIds$', 'get')
            .mockReturnValue(of(companiesIds));
        });

        it('should return an obervable with the company id', waitForAsync(() => {
          spectator.service.companyIdIfSingleCompany$.subscribe((value) => {
            expect(value).toEqual(undefined);
          });
        }));
      });
    });
  });

  describe('loadCompanies method', () => {
    let companyAdapterFromApiSpy: jest.SpyInstance<CompanyAdapter>;

    beforeEach(() => {
      companyAdapterFromApiSpy = jest.spyOn(CompanyAdapter, 'fromApi');
    });

    describe('given a state where companyGetCompanies of LegacyApiService return a list of CompanyApi', () => {
      const [companyApi1, companyApi2] = companiesApiFixture();
      const mockCompaniesApi: CompanyApi[] = [companyApi1, companyApi2];
      const [company1, company2] = companiesFixture();

      beforeEach(() => {
        companyAdapterFromApiSpy.mockReturnValueOnce(company1);
        companyAdapterFromApiSpy.mockReturnValueOnce(company2);

        (
          mockLegacyApiService.companyGetCompanies1! as unknown as jest.SpyInstance<
            Observable<CompanyApi[]>
          >
        ).mockReturnValue(of(mockCompaniesApi));
      });
      it('should map the CompanyApi into Company and push the Company list in userCompanies subject', async () => {
        await new Promise<void>((done) => {
          const firstValueOfBehaviorSubject = 1;
          ((service as any)._userCompanies$$ as BehaviorSubject<Company[]>)
            .pipe(skip(firstValueOfBehaviorSubject))
            .subscribe((companies: Company[]) => {
              expect(companies).toStrictEqual([company1, company2]);

              expect(
                mockLegacyApiService.companyGetCompanies1
              ).toHaveBeenCalledWith();
              expect(companyAdapterFromApiSpy).toHaveBeenCalled();

              done();
            });
          (service as any)._loadCompanies();
        }).then();
      });

      it('should change status of userCompaniesLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to LOADED', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'LOADED',
                ]);
                done();
              },
            });

          (service as any)._loadCompanies();
        }).then();
      });
    });

    describe('given a state where companyGetCompanies of LegacyApiService throw an error', () => {
      beforeEach(() => {
        (
          mockLegacyApiService.companyGetCompanies1! as unknown as jest.SpyInstance<
            Observable<CompanyApi[]>
          >
        ).mockImplementation(() =>
          throwError(() => new Error('Error when retrieving company'))
        );
      });

      it('should not push a Company list in userCompanies subject and change status of userCompaniesLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to ERROR', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._userCompaniesLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'ERROR',
                ]);

                const userCompaniesSnapshot = (
                  (service as any)._userCompanies$$ as BehaviorSubject<
                    Company[]
                  >
                ).value;

                expect(userCompaniesSnapshot).toBeNull();
                done();
              },
            });

          (service as any)._loadCompanies();
        }).then();
      });
    });
  });
});
