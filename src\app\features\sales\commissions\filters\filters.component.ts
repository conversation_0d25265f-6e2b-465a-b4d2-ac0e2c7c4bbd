import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  effect,
  inject,
  OnInit,
  signal,
  Signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { CompanyService } from '@gc/company/data-access';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { RepresentativesFacade } from '@gc/core/sales/representatives/application/facades';
import { Representative } from '@gc/core/sales/representatives/domains/models';
import { ResourceState } from '@gc/core/shared/store';
import { MatDatepickerDirective } from '@isagri-ng/ui/angular-material';
import { TranslocoDirective } from '@jsverse/transloco';
import { debounceTime, tap } from 'rxjs';
import { SharedUserFacade } from '@gc/core/shared/user/application/facades';
import { startWith } from 'rxjs/operators';

@Component({
  selector: 'gc-filters',
  standalone: true,
  templateUrl: './filters.component.html',
  styleUrls: ['./filters.component.scss', './filters-theme.component.scss'],
  imports: [
    TranslocoDirective,
    CommonModule,
    ReactiveFormsModule,
    CompanySingleSelectComponent,
    MatFormFieldModule,
    MatAutocompleteModule,
    MatOptionModule,
    MatDatepickerModule,
    MatDatepickerDirective,
    MatIconModule,
    MatInputModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FiltersComponent implements OnInit {
  private readonly companyService = inject(CompanyService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly fb = inject(FormBuilder);

  private readonly sharedUserFacade = inject(SharedUserFacade);
  private readonly representativesFacade = inject(RepresentativesFacade);
  private readonly commissionFacade = inject(CommissionsFacade);
  private readonly selectedRepresentative: Signal<
    ResourceState<Representative>
  >;

  filteredRepresentatives = signal<Representative[]>([]);
  readonly representatives: Signal<ResourceState<Representative[]>>;

  companyIdFC = this.fb.nonNullable.control<string>('', {
    validators: [Validators.required],
  });

  representativeFC = this.fb.nonNullable.control<Representative | string>('', {
    validators: [Validators.required],
  });

  dateCommissionsDueFC = this.fb.nonNullable.control<Date>(new Date(), {
    validators: [Validators.required],
  });

  filterFG = this.fb.group({
    companyIdFC: this.companyIdFC,
    representativeFC: this.representativeFC,
    dateCommissionsDueFC: this.dateCommissionsDueFC,
  });

  constructor() {
    this.handleCompanyChange();
    this.listenForRepresentativeSearch();
    this.representatives =
      this.representativesFacade.getRepresentativesForCurrentCompany();
    this.setDefaultCompanyValue();
    this.manageCommissionWhenSelectedRepresentativeChange();
    this.manageRepresentativesInputOptions();
    this.selectedRepresentative =
      this.representativesFacade.getSelectedRepresentative();
  }

  ngOnInit(): void {
    this.filterFG.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        debounceTime(250),
        tap(() => {
          const representativeId = this.selectedRepresentative().data?.id;

          if (!this.filterFG.valid || !representativeId) {
            this.commissionFacade.clearCommissions();
            return;
          }

          this.commissionFacade.loadCommissionsForFilter({
            companyId: this.companyIdFC.value,
            representativeId,
            date: this.dateCommissionsDueFC.value.toString(),
          });
        })
      )
      .subscribe();
  }

  private handleCompanyChange() {
    this.companyIdFC.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((companyId) => {
        this.representativesFacade.loadRepresentativesForCompany(companyId);
      });
  }

  private listenForRepresentativeSearch() {
    this.representativeFC.valueChanges
      .pipe(
        debounceTime(250),
        takeUntilDestroyed(this.destroyRef),
        startWith('')
      )
      .subscribe((input?: Representative | string) => {
        if (!input) {
          this.filteredRepresentatives.set(this.representatives().data ?? []);
          return;
        }

        if (typeof input === 'string') {
          const filteredRepresentatives = this.representatives().data?.filter(
            (filteredRepresentative) =>
              filteredRepresentative.fullname
                .toLowerCase()
                .includes(input.toLowerCase())
          );
          this.filteredRepresentatives.set(filteredRepresentatives ?? []);
          return;
        }

        // * we have a representative
        this.representativesFacade.setSelectedRepresentative(input);
      });
  }

  onRepresentativeBlur() {
    const input = this.representativeFC.value;
    if (!input) {
      return;
    }

    if (typeof input === 'string') {
      this.representativeFC.setErrors({ representativeNotFound: true });
      this.representativesFacade.clearRepresentative();
    }
  }

  private manageCommissionWhenSelectedRepresentativeChange() {
    effect(() => {
      const representativeId = this.selectedRepresentative().data?.id;
      if (!representativeId || !this.filterFG?.valid) {
        setTimeout(() => {
          this.commissionFacade.clearCommissions();
        });
        return;
      }

      this.commissionFacade.loadCommissionsForFilter({
        companyId: this.companyIdFC?.value,
        representativeId,
        date: this.dateCommissionsDueFC?.value.toString(),
      });
    });
  }

  private setDefaultCompanyValue() {
    effect(() => {
      const defaultCompanyForCurrentUserState =
        this.sharedUserFacade.getCompanyForCurrentUser()();
      if (!defaultCompanyForCurrentUserState) {
        return;
      }

      this.companyIdFC.setValue(defaultCompanyForCurrentUserState.data!);
    });
  }

  private manageRepresentativesInputOptions() {
    effect(() => {
      const representatives = this.representatives();
      if (!representatives.data) {
        return;
      }

      setTimeout(() => {
        this.filteredRepresentatives.set(representatives.data!);
      });
    });
  }

  displayFn(representative?: Representative | null) {
    if (!representative) {
      return '';
    }
    return representative.fullname;
  }
}
