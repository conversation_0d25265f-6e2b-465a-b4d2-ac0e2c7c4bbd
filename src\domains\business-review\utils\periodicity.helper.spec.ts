import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { CalendarYear } from '../models/calendar-year.model';
import { PeriodicityHelper } from './periodicity.helper';

describe('PeriodicityHelper', () => {
  describe('getCalendarYearsFromDate', () => {
    it('should return an array of calendar years', () => {
      const fromDate = new Date('2021-01-01');
      const nLastyears = 5;

      const result = PeriodicityHelper.getCalendarYearsFromDate(
        nLastyears,
        fromDate
      );

      expect(result.length).toEqual(nLastyears);

      expect(result).toEqual([
        {
          startDate: new Date(2021, 0, 1),
          endDate: new Date(2021, 11, 31),
          viewValue: '2021',
        },
        {
          startDate: new Date(2020, 0, 1),
          endDate: new Date(2020, 11, 31),
          viewValue: '2020',
        },
        {
          startDate: new Date(2019, 0, 1),
          endDate: new Date(2019, 11, 31),
          viewValue: '2019',
        },
        {
          startDate: new Date(2018, 0, 1),
          endDate: new Date(2018, 11, 31),
          viewValue: '2018',
        },
        {
          startDate: new Date(2017, 0, 1),
          endDate: new Date(2017, 11, 31),
          viewValue: '2017',
        },
      ]);
    });
  });

  describe('getDatesRangesFromCalendarYear', () => {
    it('should return an array of dates in US string format', () => {
      const requestedDateRangesCount = 5;
      const calendarYear = {
        startDate: new Date('2021-01-01'),
        endDate: new Date('2021-12-31'),
        viewValue: '2021',
      } as CalendarYear;

      const result = PeriodicityHelper.getDatesRangesFromCalendarYear(
        calendarYear,
        requestedDateRangesCount
      );

      expect(result.length).toEqual(requestedDateRangesCount * 2);
      expect(result).toStrictEqual([
        '1/1/2021',
        '12/31/2021',
        '1/1/2020',
        '12/31/2020',
        '1/1/2019',
        '12/31/2019',
        '1/1/2018',
        '12/31/2018',
        '1/1/2017',
        '12/31/2017',
      ]);
    });
  });

  describe('getDatesRangesFromFiscalYear', () => {
    const fromFiscalYear: FiscalYearDateRange = {
      dateFrom: new Date('2021-08-01'),
      dateTo: new Date('2021-12-31'),
    };

    it('should return an empty array when the parameter fiscal year is null', () => {
      const fiscalYearsList = [
        {
          dateFrom: new Date('2021-01-01'),
          dateTo: new Date('2021-07-31'),
        },
      ] as FiscalYearDateRange[];
      expect(
        PeriodicityHelper.getDatesRangesFromFiscalYear(null, fiscalYearsList, 5)
      ).toEqual([]);
    });

    describe('when there is enough consecutives fiscal years to reach the required count', () => {
      const fiscalYearsList = [
        fromFiscalYear,
        {
          dateFrom: new Date('2021-01-01'),
          dateTo: new Date('2021-07-31'),
        },
        {
          dateFrom: new Date('2020-08-01'),
          dateTo: new Date('2020-12-31'),
        },
        {
          dateFrom: new Date('2020-01-01'),
          dateTo: new Date('2020-07-31'),
        },
        {
          dateFrom: new Date('2019-08-01'),
          dateTo: new Date('2019-12-31'),
        },
        {
          dateFrom: new Date('2019-01-01'),
          dateTo: new Date('2019-07-31'),
        },
      ] as FiscalYearDateRange[];

      it('should return an array of dates in US string format matching the required count', () => {
        const requiredCount = 5;
        const result = PeriodicityHelper.getDatesRangesFromFiscalYear(
          fromFiscalYear,
          fiscalYearsList,
          requiredCount
        );

        expect(result).toEqual([
          '8/1/2021',
          '12/31/2021',
          '1/1/2021',
          '7/31/2021',
          '8/1/2020',
          '12/31/2020',
          '1/1/2020',
          '7/31/2020',
          '8/1/2019',
          '12/31/2019',
        ]);
      });
    });

    describe('when there is less consecutives fiscal years than the required count', () => {
      const fiscalYearsList = [
        fromFiscalYear,
        {
          dateFrom: new Date('2021-01-01'),
          dateTo: new Date('2021-07-31'),
        },
        {
          dateFrom: new Date('2000-08-01'),
          dateTo: new Date('2000-12-31'),
        },
      ] as FiscalYearDateRange[];

      it('should return an array of dates in US string format with the consecutives years', () => {
        const requiredCount = 5;

        const result = PeriodicityHelper.getDatesRangesFromFiscalYear(
          fromFiscalYear,
          fiscalYearsList,
          requiredCount
        );

        expect(result).toEqual([
          '8/1/2021',
          '12/31/2021',
          '1/1/2021',
          '7/31/2021',
        ]);
      });
    });

    describe('when there is no consecutives fiscal years', () => {
      const requiredCount = 5;

      const fiscalYearsList = [
        fromFiscalYear,
        {
          dateFrom: new Date('2022-01-01'),
          dateTo: new Date('2022-07-31'),
        },
        {
          dateFrom: new Date('2000-08-01'),
          dateTo: new Date('2000-12-31'),
        },
      ] as FiscalYearDateRange[];

      it('should return an array of dates in US string format with 1 date range', () => {
        const result = PeriodicityHelper.getDatesRangesFromFiscalYear(
          fromFiscalYear,
          fiscalYearsList,
          requiredCount
        );

        expect(result).toEqual(['8/1/2021', '12/31/2021']);
      });
    });
  });
});
