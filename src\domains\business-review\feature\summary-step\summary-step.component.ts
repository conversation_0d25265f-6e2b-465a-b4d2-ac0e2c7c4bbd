import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { BehaviorSubject, Observable, combineLatest, map, take } from 'rxjs';
import { LetDirective, PushPipe } from '@ngrx/component';
import { TranslocoModule } from '@jsverse/transloco';
import {
  TranslocoDatePipe,
  TranslocoDecimalPipe,
} from '@jsverse/transloco-locale';
import { AsyncPipe } from '@angular/common';
import { BusinessReviewFormService } from '../services/business-review-form.service';
import { CompanyService } from '@gc/company/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-summary-step',
  standalone: true,
  imports: [
    TranslocoModule,
    MatListModule,
    MatIconModule,
    PushPipe,
    LetDirective,
    AsyncPipe,
    TranslocoDecimalPipe,
    TranslocoDatePipe,
  ],
  templateUrl: './summary-step.component.html',
  styleUrl: './summary-step.component.scss',
})
export class SummaryStepComponent implements OnInit {
  private readonly _service = inject(BusinessReviewFormService);
  private readonly _companyService = inject(CompanyService);
  private readonly _destroyRef = inject(DestroyRef);

  companiesFC = this._service.companiesFC;

  selectedDateRange$: Observable<{
    startDate: Date | undefined;
    endDate: Date | undefined;
  }> = this._service.selectedDateRange$;

  get periodicityKind(): string {
    return this._service.periodicityKind;
  }

  companiesCodes$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  vatRate$ = this._service.vatRateFC.valueChanges;

  ngOnInit(): void {
    combineLatest([
      this._companyService.companies$.pipe(take(1)),
      this.companiesFC.valueChanges,
    ])
      .pipe(
        map(([companies, companyIds]) =>
          companies
            ?.filter(({ id }) => companyIds.includes(id))
            .map(({ code }) => code)
            .join(', ')
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((v) => this.companiesCodes$$.next(v));
  }
}
