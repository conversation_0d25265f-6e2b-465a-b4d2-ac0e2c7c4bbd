import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { Paths, TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';
import { MonthlyTreatmentComponent } from './monthly-treatment.component';

export const ROUTES: Routes = [
  {
    path: '',
    redirectTo: Paths.ACCOUNTING_VISUALIZATION,
    pathMatch: 'full',
  },
  {
    path: '',
    component: MonthlyTreatmentComponent,
    title: TitleKeyTab.ACCOUNTING,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.ACCOUNTING)],
    children: [
      {
        path: Paths.ACCOUNTING_VISUALIZATION,
        loadChildren: () =>
          import('./monthly-visualization/routes').then((m) => m.ROUTES),
      },
      {
        path: Paths.ACCOUNTING_HISTORY,
        loadChildren: () =>
          import('./monthly-history/routes').then((m) => m.ROUTES),
      },
    ],
  },
];
