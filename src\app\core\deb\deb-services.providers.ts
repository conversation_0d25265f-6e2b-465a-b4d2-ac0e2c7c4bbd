import { Provider } from '@angular/core';
import { DebFacade } from '@gc/core/deb/application/facades';
import {
  ClosedMonthsUseCase,
  ClosureUseCase,
  DestinationCountryCodesUseCase,
  DraftDeclarationsUseCase,
  EuNomenclaturesUseCase,
  LoadDetailsUseCase,
  ParametersUseCase,
  StatisticalProceduresUseCase,
  UncloseMonthUseCase,
} from '@gc/core/deb/application/use-cases';
import { GetCompanyUseCase } from '@gc/core/shared/user/application/use-cases';

export function provideDebServices(): Provider[] {
  return [
    DebFacade,
    ClosedMonthsUseCase,
    ParametersUseCase,
    DraftDeclarationsUseCase,
    ClosureUseCase,
    GetCompanyUseCase,
    UncloseMonthUseCase,
    LoadDetailsUseCase,
    EuNomenclaturesUseCase,
    StatisticalProceduresUseCase,
    DestinationCountryCodesUseCase,
  ];
}
