import { <PERSON><PERSON><PERSON>y, FormBuilder, Validators } from '@angular/forms';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentService,
  selectSalesByProductCurrentFilter,
} from '@gc/accounting/data-access';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { ProductCharacteristicFiltersFormGroup } from './models/product-characteristic-filters-form-group.model';
import { SalesByProductCategoryTreatmentIdRetrieverCardComponent } from './sales-by-product-category-treatment-id-retriever-card.component';
import { FormGroupManagerService } from './services/form-group-manager.service';
import { MockComponents } from 'ng-mocks';
import { CharacteristicSelectComponent } from '@gc/product/feature';

describe('SalesByProductCategoryTreatmentIdRetrieverCardComponent', () => {
  let spectator: Spectator<SalesByProductCategoryTreatmentIdRetrieverCardComponent>;
  let component: SalesByProductCategoryTreatmentIdRetrieverCardComponent;
  let formGroupManagerService: FormGroupManagerService;
  let store: Store;

  let dispatchSpy: jest.SpyInstance<void>;
  let selectSpy: jest.SpyInstance;

  const createComponent = createComponentFactory({
    component: SalesByProductCategoryTreatmentIdRetrieverCardComponent,
    declarations: [MockComponents(CharacteristicSelectComponent)],
    mocks: [
      TranslocoService,
      MonthlyTreatmentService,
      FormGroupManagerService,
      Store,
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
    formGroupManagerService = spectator.inject(FormGroupManagerService);
    store = spectator.inject(Store);

    dispatchSpy = jest.spyOn(store, 'dispatch');
    selectSpy = jest.spyOn(store, 'select').mockReturnValue(of(void 0));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    const defaultCharacteristicsEnum = [
      ProductCharacteristicEnum.CUSTOM_0,
      ProductCharacteristicEnum.VINTAGE,
    ];

    describe('given a state where selectSalesByProductDefaultFilter return a value', () => {
      beforeEach(() => {
        selectSpy.mockReturnValue(of(defaultCharacteristicsEnum));
      });
      it('should dispatch loadSalesByProductDefaultFilter action, set product characteristic filters form group and call internal methods', () => {
        const initSpy = jest
          .spyOn(formGroupManagerService, 'init')
          .mockImplementation();
        const handleRequiredStateSpy = jest
          .spyOn(component, 'handleRequiredState')
          .mockImplementation();
        const handleSelectFiltersSpy = jest
          .spyOn(component, 'handleSelectFilters')
          .mockImplementation();
        const handleStoreUpdateWhenValueChangeSpy = jest
          .spyOn(component, 'handleStoreUpdateWhenValueChange')
          .mockImplementation();

        component.ngOnInit();

        expect(selectSpy).toHaveBeenCalledWith(
          selectSalesByProductCurrentFilter
        );
        expect(initSpy).toHaveBeenCalledWith(defaultCharacteristicsEnum);
        expect(handleRequiredStateSpy).toHaveBeenCalled();
        expect(handleSelectFiltersSpy).toHaveBeenCalled();
        expect(handleStoreUpdateWhenValueChangeSpy).toHaveBeenCalled();
      });
    });
  });

  describe('handleRequiredState', () => {
    describe('given a state with no productCharacteristicFG', () => {
      it('should return an error', () => {
        expect(() => component.handleRequiredState()).toThrow(
          'productCharacteristicFG is not initialized'
        );
      });
    });

    describe('given a state wit productCharacteristicFG is already init', () => {
      const fb = new FormBuilder();

      const fg = fb.group({
        characteristics: fb.array([
          fb.control<ProductCharacteristicEnum | null>(15, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
        ]),
      });

      const characteristic = fg.get('characteristics') as FormArray;

      it('should call switchRequiredValidationState', () => {
        const switchRequiredValidationStateSpy = jest
          .spyOn(formGroupManagerService, 'switchRequiredValidationState')
          .mockImplementation();

        component.productCharacteristicFG = fg;
        component.handleRequiredState();

        characteristic.controls[1].setValue(17);
        expect(switchRequiredValidationStateSpy).toHaveBeenCalled();
      });
    });
  });

  describe('handleSelectFilters', () => {
    describe('given a state with no productCharacteristicFG', () => {
      it('should return an error', () => {
        expect(() => component.handleSelectFilters()).toThrow(
          'productCharacteristicFG is not initialized'
        );
      });
    });

    describe('given a state with productCharacteristicFG set', () => {
      const fb = new FormBuilder();

      const fg: ProductCharacteristicFiltersFormGroup = fb.group({
        characteristics: fb.array([
          fb.control<ProductCharacteristicEnum | null>(15, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
        ]),
      });

      const characteristic = fg.get('characteristics') as FormArray;

      it('should update select filters', () => {
        component.productCharacteristicFG = fg;

        component.handleSelectFilters();

        characteristic.controls[1].setValue(17);
        expect(component.selectFilters).toEqual([15, 17]);
      });
    });
  });

  describe('handleStoreUpdateWhenValueChange', () => {
    describe('given a state with no productCharacteristicFG', () => {
      it('should return an error', () => {
        expect(() => component.handleSelectFilters()).toThrow(
          'productCharacteristicFG is not initialized'
        );
      });
    });

    describe('given a state with productCharacteristicFG set', () => {
      const fb = new FormBuilder();

      const fg: ProductCharacteristicFiltersFormGroup = fb.group({
        characteristics: fb.array([
          fb.control<ProductCharacteristicEnum | null>(15, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
          fb.control<ProductCharacteristicEnum | null>(null, {
            validators: [Validators.required],
          }),
        ]),
      });

      it('should dispatch changeSalesByProductCurrentFilter with FormGroupValue as payload', () => {
        component.productCharacteristicFG = fg;

        component.handleStoreUpdateWhenValueChange();

        component.productCharacteristicFG.controls.characteristics.controls[1].setValue(
          ProductCharacteristicEnum.CUSTOM_0
        );
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.changeSalesByProductCurrentFilter({
            filter: [15, 0, null],
          })
        );
      });
    });

    describe('given a state with productCharacteristicFG undefined', () => {
      it('should throw an error', () => {
        component.productCharacteristicFG = undefined;

        try {
          component.handleStoreUpdateWhenValueChange();
        } catch (e) {
          expect(e).toEqual(
            new Error('productCharacteristicFG is not initialized')
          );
        }
      });
    });
  });
});
