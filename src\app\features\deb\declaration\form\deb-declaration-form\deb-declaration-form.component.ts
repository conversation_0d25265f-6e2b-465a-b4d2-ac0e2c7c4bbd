import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  input,
  OnChanges,
  output,
  Signal,
  signal,
  SimpleChanges,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import {
  NOMENCLATURE_MAX_LENGTH,
  VAT_NUMBER_MAX_LENGTH,
} from '@gc/core/deb/application/constants';
import { DebFacade } from '@gc/core/deb/application/facades';
import { DraftDeclaration } from '@gc/core/deb/domains/models';
import { ResourceState } from '@gc/core/shared/store';
import { CurrencyService } from '@gc/currency/data-access';
import { Currency } from '@gc/shared/models';
import { CurrencyInputComponent } from '@gc/shared/ui';
import { formControlValue } from '@gc/shared/utils';
import { TranslocoDirective } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { debounceTime, defaultIfEmpty } from 'rxjs';
@Component({
  selector: 'gc-deb-declaration-form',
  standalone: true,
  imports: [
    CurrencyInputComponent,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    TranslocoDirective,
    PushPipe,
  ],
  templateUrl: './deb-declaration-form.component.html',
  styleUrl: './deb-declaration-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationFormComponent implements OnChanges {
  private readonly _fb = inject(FormBuilder);
  private readonly destroyRef = inject(DestroyRef);
  private readonly currencyService = inject(CurrencyService);
  private readonly debFacade = inject(DebFacade);

  initialDraftDeclaration = input<DraftDeclaration>();
  companyId = input.required<string>();
  month = input.required<Date>();
  editedDraftDeclaration = output<DraftDeclaration | null>();

  euNomenclatureFC!: FormControl<string | null>;
  destinationCountryCodeFC!: FormControl<string | null>;
  invoicedAmountFC!: FormControl<number | null>;
  statisticalProcedureCodeFC!: FormControl<string | null>;
  vatNumberFC!: FormControl<string | null>;
  VAT_NUMBER_MAX_LENGTH = VAT_NUMBER_MAX_LENGTH;
  nomenclatureMaxLength = NOMENCLATURE_MAX_LENGTH;
  formGroup!: FormGroup;

  currentEuNomenclatureSearch = signal<string | null>(null);

  readonly euNomenclatures: Signal<ResourceState<string[]>>;
  readonly filteredEuNomenclatures: Signal<string[] | undefined>;
  readonly statisticalProcedures: Signal<ResourceState<string[]>>;
  readonly destinationCountryCodes: Signal<ResourceState<string[]>>;

  currency$ = this.currencyService
    .getDefaultCurrency$()
    .pipe(defaultIfEmpty({} as Currency));

  constructor() {
    this.euNomenclatures = this.debFacade.getEuNomenclatures();
    this.statisticalProcedures =
      this.debFacade.getStatisticalProceduresForCurrentCompany();
    this.destinationCountryCodes =
      this.debFacade.getDestinationCountryCodesForCurrentCompanyAndDeclarationMonth();

    this.filteredEuNomenclatures = computed(() =>
      this.euNomenclatures().data?.filter((nomenclature) =>
        nomenclature.includes(this.currentEuNomenclatureSearch() ?? '')
      )
    );

    effect(() => {
      if (!this.companyId() || !this.month()) {
        return;
      }

      const companyId = this.companyId();

      setTimeout(() => {
        this.debFacade.loadEuNomenclatures();
        this.debFacade.loadDestinationCountryCodes(companyId, this.month());
        this.debFacade.loadStatisticalProceduresFor(companyId);
      });
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ('initialDraftDeclaration' in changes) {
      this.euNomenclatureFC = this._fb.control(
        this.initialDraftDeclaration()?.euNomenclature ?? null,
        [Validators.required, Validators.maxLength(this.nomenclatureMaxLength)]
      );
      this.destinationCountryCodeFC = this._fb.control(
        this.initialDraftDeclaration()?.destinationCountryCode ?? null,
        [Validators.required]
      );
      this.invoicedAmountFC = this._fb.control(
        this.initialDraftDeclaration()?.invoicedAmount ?? null,
        [Validators.required]
      );
      this.statisticalProcedureCodeFC = this._fb.control(
        this.initialDraftDeclaration()?.statisticalProcedureCode ?? null,
        [Validators.required]
      );
      this.vatNumberFC = this._fb.control(
        this.initialDraftDeclaration()?.vatNumber ?? null,
        [Validators.required]
      );

      this.formGroup = this._fb.group({
        euNomenclature: this.euNomenclatureFC,
        destinationCountryCode: this.destinationCountryCodeFC,
        invoicedAmount: this.invoicedAmountFC,
        statisticalProcedureCode: this.statisticalProcedureCodeFC,
        vatNumber: this.vatNumberFC,
      });
      this.listenForEuNomenclatureSearch();
    }
  }

  onSubmit(): void {
    const declarationToSubmit = {
      ...this.initialDraftDeclaration(),
      ...this.formGroup.value,
    };

    this.editedDraftDeclaration.emit(declarationToSubmit);
  }

  cancel(): void {
    this.editedDraftDeclaration.emit(null);
  }

  private listenForEuNomenclatureSearch(): void {
    const debounce = 300;

    formControlValue(this.euNomenclatureFC)
      .pipe(debounceTime(debounce), takeUntilDestroyed(this.destroyRef))
      .subscribe((search) => this.currentEuNomenclatureSearch.set(search));
  }
}
