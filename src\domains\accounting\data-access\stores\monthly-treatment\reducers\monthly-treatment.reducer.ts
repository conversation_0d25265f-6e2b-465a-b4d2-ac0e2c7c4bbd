import { SalesDetailsOrderByEnum } from '@gc/accounting/models';
import {
  firstDayOfMonth,
  getNextMonth,
  lastDayOfMonth,
} from '@gc/shared/utils';
import { createReducer, on } from '@ngrx/store';
import { monthlyTreatmentActions } from '../actions/monthly-treatment.actions';
import { MonthlyTreatmentState } from '../models/monthly-treatment-state.model';

export const initialState: MonthlyTreatmentState = {
  enclosureMonthLoadingStatus: 'NOT_LOADED',
  defaultFiltersLoadingStatus: 'NOT_LOADED',
  emptyEditionsLoadingStatus: 'NOT_LOADED',
  enclosingStatus: 'NOT_PROCESSED',
  enclosingDefaultStatus: 'NOT_PROCESSED',
};

export const monthlyTreatmentReducer = createReducer(
  initialState,
  on(
    monthlyTreatmentActions.changeCompanyId,
    (_state, { companyId }): MonthlyTreatmentState => {
      return {
        ..._state,
        companyId,
      };
    }
  ),
  on(
    monthlyTreatmentActions.changeDateRange,
    (_state, { startDate, endDate }): MonthlyTreatmentState => {
      return {
        ..._state,
        range: { start: startDate, end: endDate },
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadDefaultFilters,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        defaultFiltersLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadDefaultFiltersSuccess,
    (_state, { filters }): MonthlyTreatmentState => {
      return {
        ..._state,
        salesByProductCurrentFilter: filters?.productCategories,
        salesDetailsCurrentFilter: filters?.salesDetailOrder,
        defaultFiltersLoadingStatus: 'LOADED',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadDefaultFiltersError,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        defaultFiltersLoadingStatus: 'ERROR',
      };
    }
  ),
  on(
    monthlyTreatmentActions.changeSalesByProductCurrentFilter,
    (_state, { filter }): MonthlyTreatmentState => {
      return {
        ..._state,
        salesByProductCurrentFilter: filter,
      };
    }
  ),
  on(
    monthlyTreatmentActions.changeSalesDetailsCurrentFilter,
    (_state, { filter }): MonthlyTreatmentState => {
      return {
        ..._state,
        salesDetailsCurrentFilter: filter
          ? SalesDetailsOrderByEnum.BUSINESS_TYPE
          : SalesDetailsOrderByEnum.INVOICES,
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadEnclosureMonth,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosureMonthLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadEnclosureMonthSuccess,
    (_state, { enclosingMonthInformations }): MonthlyTreatmentState => {
      const start = firstDayOfMonth(
        enclosingMonthInformations?.enclosureMonth ?? undefined
      );
      const end = lastDayOfMonth(
        enclosingMonthInformations?.enclosureMonth ?? undefined
      );

      return {
        ..._state,
        enclosureMonth: enclosingMonthInformations?.enclosureMonth,
        range: {
          start,
          end,
        },
        hasAlreadyEnclosedMonths:
          enclosingMonthInformations?.hasAlreadyEnclosedMonths,
        enclosureMonthLoadingStatus: 'LOADED',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadEnclosureMonthError,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosureMonthLoadingStatus: 'ERROR',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadHasEmptyEdition,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        emptyEditionsLoadingStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadHasEmptyEditionsSuccess,
    (_state, { isEmpty }): MonthlyTreatmentState => {
      return {
        ..._state,
        emptyEditions: isEmpty,
        emptyEditionsLoadingStatus: 'LOADED',
      };
    }
  ),
  on(
    monthlyTreatmentActions.loadHasEmptyEditionsError,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        emptyEditionsLoadingStatus: 'ERROR',
      };
    }
  ),
  on(
    monthlyTreatmentActions.resetEmptyEditions,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        emptyEditions: undefined,
        emptyEditionsLoadingStatus: 'NOT_LOADED',
      };
    }
  ),
  on(monthlyTreatmentActions.encloseMonth, (_state): MonthlyTreatmentState => {
    return {
      ..._state,
      enclosingStatus: 'IN_PROGRESS',
    };
  }),
  on(
    monthlyTreatmentActions.encloseMonthSuccess,
    (_state): MonthlyTreatmentState => {
      const { enclosureMonth } = _state;

      if (!enclosureMonth) return _state;

      const startNewEnclosureMonth = firstDayOfMonth(
        getNextMonth(enclosureMonth)
      );
      const endNewEnclosureMonth = lastDayOfMonth(getNextMonth(enclosureMonth));

      return {
        ..._state,
        enclosingStatus: 'DONE',
        lastEnclosedMonth: enclosureMonth,
        enclosureMonth: startNewEnclosureMonth,
        range: {
          start: startNewEnclosureMonth,
          end: endNewEnclosureMonth,
        },
      } as MonthlyTreatmentState;
    }
  ),
  on(
    monthlyTreatmentActions.encloseMonthError,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosingStatus: 'ERROR',
      };
    }
  ),
  on(
    monthlyTreatmentActions.resetEnclosingStatus,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosingStatus: 'NOT_PROCESSED',
      };
    }
  ),
  on(
    monthlyTreatmentActions.encloseDefaultMonth,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosingDefaultStatus: 'IN_PROGRESS',
      };
    }
  ),
  on(
    monthlyTreatmentActions.encloseDefaultMonthSuccess,
    (_state, { defaultMonth }): MonthlyTreatmentState => {
      const startNewEnclosureMonth = firstDayOfMonth(
        getNextMonth(defaultMonth)
      );
      const endNewEnclosureMonth = lastDayOfMonth(getNextMonth(defaultMonth));

      return {
        ..._state,
        enclosingDefaultStatus: 'NOT_PROCESSED',
        enclosureMonth: startNewEnclosureMonth,
        hasAlreadyEnclosedMonths: true,
        range: {
          start: startNewEnclosureMonth,
          end: endNewEnclosureMonth,
        },
      };
    }
  ),
  on(
    monthlyTreatmentActions.encloseDefaultMonthError,
    (_state): MonthlyTreatmentState => {
      return {
        ..._state,
        enclosingDefaultStatus: 'ERROR',
      };
    }
  )
);
