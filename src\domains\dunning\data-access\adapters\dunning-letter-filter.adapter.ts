import { DunningLetterFiltersApi } from '@gc/shared/api/data-access';
import { DunningLetterFilter } from '@gc/dunning/models';
import { DunningLevelAdapter } from './dunning-level.adapter';

export class DunningLetterFilterAdapter {
  public static toApi(
    dunningLetterFilter: DunningLetterFilter
  ): DunningLetterFiltersApi {
    const { companyId, dunningLevel } = dunningLetterFilter;
    return {
      companyId,
      dunningLevel: dunningLevel
        ? DunningLevelAdapter.toApi(dunningLevel)
        : undefined,
    };
  }
}
