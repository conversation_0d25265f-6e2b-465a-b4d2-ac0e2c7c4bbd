import { SpectatorDirective } from '@ngneat/spectator';
import { createDirectiveFactory } from '@ngneat/spectator/jest';
import { IsSingleCompanyDirective } from './is-single-company.directive';
import { CompanyService } from '@gc/company/data-access';
import { Observable, of } from 'rxjs';

describe('IsSingleCompanyDirective', () => {
  let spectator: SpectatorDirective<IsSingleCompanyDirective>;

  let isSingleCompanyGetterSpy: jest.SpyInstance<Observable<boolean>>;

  const createDirective = createDirectiveFactory({
    directive: IsSingleCompanyDirective,
    mocks: [CompanyService],
    template: `<div *gcIsSingleCompany>Test</div>`,
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createDirective();
    const companyService = spectator.inject(CompanyService);
    isSingleCompanyGetterSpy = jest.spyOn(
      companyService,
      'isSingleCompany$',
      'get'
    );
  });

  describe('given a state with one company', () => {
    beforeEach(() => {
      isSingleCompanyGetterSpy.mockReturnValue(of(true));
      spectator.detectChanges();
    });

    describe('when directive is applyed on DOM element', () => {
      it('then should not create an instance', () => {
        expect(spectator.query('div')).toBeFalsy();
      });
    });
  });

  describe('given a state with several companies', () => {
    beforeEach(() => {
      isSingleCompanyGetterSpy.mockReturnValue(of(false));
      spectator.detectChanges();
    });

    describe('when directive is applyed on DOM element', () => {
      it('then should create an instance', () => {
        expect(spectator.query('div')).toBeTruthy();
      });
    });
  });
});
