import { Component, EventEmitter, Input, Output } from '@angular/core';

import { MonthlyTreatmentCardComponent } from '@gc/accounting/ui';
import { TranslocoDirective } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';

@Component({
  selector: 'gc-account-entry-list-treatment-id-retriever-card',
  standalone: true,
  imports: [MonthlyTreatmentCardComponent, TranslocoDirective, PushPipe],
  templateUrl:
    './account-entry-list-treatment-id-retriever-card.component.html',
  styleUrls: [
    './account-entry-list-treatment-id-retriever-card.component.scss',
  ],
})
export class AccountEntryListTreatmentIdRetrieverCardComponent {
  @Input() invalid!: boolean;
  @Output() consult = new EventEmitter<void>();
}
