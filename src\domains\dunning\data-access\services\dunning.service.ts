import { Injectable, inject } from '@angular/core';
import {
  DunningLevelResponseBusinessResultAPIApi,
  SaveDunningsResponseBusinessResultAPIApi,
  DunningLevelProposalApi,
  DunningResponseApi,
  DunningLevelApi,
  LegacyApiService,
} from '@gc/shared/api/data-access';
import { Observable, map } from 'rxjs';
import { DunningPaginationAdapter } from '../adapters/dunning-pagination.adapter';
import { DueDateService } from '@gc/due-date/data-access';
import { DueDate } from '@gc/due-date/models';
import {
  DunningDueDate,
  DunningLevelsConfig,
  DunningsSaveBusinessResult,
  DunningsToSave,
  DunningsToSaveAll,
  DunningFilter,
  DunningPagination,
} from '@gc/dunning/models';
import { DunningDueDateAdapter } from '../adapters/dunning-due-date.adapter';
import { DunningLevelAdapter } from '../adapters/dunning-level.adapter';
import { DunningNextLevelProposalAdapter } from '../adapters/dunning-next-level-proposal.adapter';
import { SaveDunningsResponseBusinessResultAPIApiAdapter } from '../adapters-api/business-result-api-save-dunnings-response-api.adapter';
import { DunningFilterAdapter } from '../adapters/dunning-filter.adapter';
import { DunningToSaveAdapter } from '../adapters-api/dunning-to-save.adapter';
import { DunningToSaveAllAdapter } from '../adapters-api/dunning-to-save-all.adapter';

@Injectable({
  providedIn: 'root',
})
export class DunningService {
  private readonly _dueDateService = inject(DueDateService);
  private readonly _legacyApiService = inject(LegacyApiService);

  public getDunnings(
    dunningFilter: DunningFilter
  ): Observable<DunningPagination> {
    const filterApi = DunningFilterAdapter.toApi(dunningFilter);
    return this._legacyApiService
      .dunningsGetDunningsByAdvancedFilters(filterApi)
      .pipe(
        map((dunningResponseApi: DunningResponseApi) =>
          DunningPaginationAdapter.fromApi(dunningResponseApi)
        )
      );
  }

  public getDunningLevelsConfig(): Observable<DunningLevelsConfig> {
    return this._legacyApiService.dunningsGetDunningLevels().pipe(
      map(
        (
          dunningLevelResponseBusinessResultAPIApi: DunningLevelResponseBusinessResultAPIApi
        ) =>
          ({
            levels:
              dunningLevelResponseBusinessResultAPIApi.result?.dunningLevels.map(
                (levelApi: DunningLevelApi) =>
                  DunningLevelAdapter.fromApi(levelApi)
              ),
            nextLevelsProposals:
              dunningLevelResponseBusinessResultAPIApi.result?.dunningLevelProposal.map(
                (levelProposalApi: DunningLevelProposalApi) =>
                  DunningNextLevelProposalAdapter.fromApi(levelProposalApi)
              ),
          }) as DunningLevelsConfig
      )
    );
  }

  public getDunningInvoiceAllDueDates(
    invoiceId: string
  ): Observable<DunningDueDate[]> {
    return this._dueDateService
      .getAllDueDatesByInvoiceId(invoiceId)
      .pipe(
        map((dueDates: DueDate[]) =>
          dueDates.map((dueDate: DueDate) =>
            DunningDueDateAdapter.fromDueDate(dueDate)
          )
        )
      );
  }

  public saveSelectedDunnings(
    dunningsToSave: DunningsToSave
  ): Observable<DunningsSaveBusinessResult> {
    const dunningsToSaveApi = DunningToSaveAdapter.toApi(dunningsToSave);
    return this._legacyApiService
      .dunningsSave(dunningsToSaveApi)
      .pipe(
        map((reponseAPI: SaveDunningsResponseBusinessResultAPIApi) =>
          SaveDunningsResponseBusinessResultAPIApiAdapter.fromApi(reponseAPI)
        )
      );
  }

  public saveAllDunnings(
    dunningsToSaveAll: DunningsToSaveAll
  ): Observable<DunningsSaveBusinessResult> {
    const dunningsToSaveAllApi =
      DunningToSaveAllAdapter.toApi(dunningsToSaveAll);
    return this._legacyApiService.dunningsSaveAll(dunningsToSaveAllApi).pipe(
      map((reponseAPI: SaveDunningsResponseBusinessResultAPIApi) => {
        return SaveDunningsResponseBusinessResultAPIApiAdapter.fromApi(
          reponseAPI
        );
      })
    );
  }
}
