<gc-main-container [scrollable]="true">
  <ng-container *transloco="let t; read: 'sales'">
    <ng-container
      *ngrxLet="{
        currency: currency$,
      } as vm">
      <mat-tab-group
        mat-stretch-tabs="false"
        mat-align-tabs="center"
        class="gc-full-height-tabs">
        <mat-tab [label]="t('page.navigation-tab.commissions') | uppercase">
          <div class="commissions-container">
            <div class="scrollable-content">
              <section class="filters-container">
                <gc-filters />
              </section>
              <section class="table-container">
                <gc-payout-table
                  (selectedCommissionsChange)="setAmountToPay($event)" />
              </section>
            </div>
            <section class="summary-container">
              <gc-payout-summary-footer
                [amountToPay]="amountToPay"
                [currency]="vm.currency" />
            </section>
          </div>
        </mat-tab>

        <mat-tab [label]="t('page.navigation-tab.history') | uppercase">
          <!-- TODO history component -->
        </mat-tab>
      </mat-tab-group>
    </ng-container>
  </ng-container>
</gc-main-container>
