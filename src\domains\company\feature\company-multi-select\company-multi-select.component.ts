import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  inject,
} from '@angular/core';
import { Company, CompanyId } from '@gc/company/models';

import {
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';
import {
  MatCheckboxChange,
  MatCheckboxModule,
} from '@angular/material/checkbox';
import { Observable, filter, map, take } from 'rxjs';
import { CompanyMultiSelectDefaultSelectionMode } from './company-multi-select-default-selection-mode.model';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AsyncPipe } from '@angular/common';
import { BaseControlValueAccessorComponent } from '@gc/shared/form/utils';
import { CompanyService } from '@gc/company/data-access';

@Component({
  selector: 'gc-company-multi-select',
  templateUrl: './company-multi-select.component.html',
  styleUrls: [
    './company-multi-select.component.scss',
    './company-multi-select-theme.component.scss',
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: CompanyMultiSelectComponent,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    MatOptionModule,
    AsyncPipe,
  ],
})
export class CompanyMultiSelectComponent
  extends BaseControlValueAccessorComponent<CompanyId[]>
  implements OnInit
{
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _companyService = inject(CompanyService);

  @Input() label!: string;
  @Input() selectAllLabel!: string;
  @Input() inputId!: string;
  @Input() defaultSelectionMode?: CompanyMultiSelectDefaultSelectionMode;

  @Input() invalid = false;
  @Input() errorText?: string;

  companiesOptions!: Company[];

  override ngOnInit(): void {
    super.ngOnInit();

    this._companyService.companies$
      .pipe(
        filter((companies: Company[]) => companies.length > 0),
        take(1)
      )
      .subscribe((companies: Company[]) => {
        this.companiesOptions = companies;
        this._cdr.markForCheck();
        this.handleDefaultSelection(companies);
      });
  }

  onCompaniesChanged(companiesId: CompanyId[]): void {
    this.setValue(companiesId);
  }

  isIndeterminate$(): Observable<boolean> {
    return this.getValue$().pipe(
      map((companiesId: CompanyId[] | undefined) => {
        return (
          !!companiesId &&
          !!this.companiesOptions.length &&
          companiesId.length > 0 &&
          companiesId.length < this.companiesOptions.length
        );
      })
    );
  }

  isChecked$(): Observable<boolean> {
    return this.getValue$().pipe(
      map((companiesId: CompanyId[] | undefined) => {
        return (
          !!companiesId &&
          !!this.companiesOptions.length &&
          companiesId.length === this.companiesOptions.length
        );
      })
    );
  }

  toggleSelection(change: MatCheckboxChange): void {
    if (change.checked) {
      this.setValue(this.companiesOptions.map(({ id }) => id));
    } else {
      this.setValue([]);
    }
  }

  handleDefaultSelection(companies: Company[]): void {
    if (companies.length === 1) {
      this.resetParent([companies[0].id]);
    } else if (
      this.defaultSelectionMode === CompanyMultiSelectDefaultSelectionMode.ALL
    ) {
      this.resetParent(companies.map(({ id }) => id));
    }
  }
}
