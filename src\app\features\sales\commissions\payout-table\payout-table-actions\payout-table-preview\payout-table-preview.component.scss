:host {
  display: block;
  width: 100%;
}

mat-card {
  margin: 16px;
}

table {
  width: 100%;
}

.mat-mdc-header-cell {
  color: grey;
  font-weight: 500;
}

.mat-mdc-card-header {
  padding: 16px;
}

.mat-mdc-card-content {
  padding: 16px;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 10px;
  padding-bottom: 20px;
}

.action-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20px;
  gap: 10px;
}

.user-feedback-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
