import { inject, Injectable } from '@angular/core';
import { GetParametersPort } from '@gc/core/deb/domains/ports';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { map } from 'rxjs';
import { DebParameters } from '@gc/core/deb/domains/models';

@Injectable()
export class GetParametersAdapter implements GetParametersPort {
  private readonly api = inject(DebApiService);

  for(companyId: string) {
    return this.api.getParameters(companyId).pipe(
      map((response) => {
        return {
          ...response,
          declarationType: response.declarationType === 4 ? 'light' : 'full',
        } as DebParameters;
      })
    );
  }
}
