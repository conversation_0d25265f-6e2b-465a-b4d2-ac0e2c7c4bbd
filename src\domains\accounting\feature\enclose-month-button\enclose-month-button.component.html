<ng-container *transloco="let t; read: 'accounting.monthly-edition-tab'">
  <ng-container
    *ngrxLet="{
      enclosureMonthLoadingStatus: enclosureMonthLoadingStatus$,
      enclosureMonth: enclosureMonth$,
      showLoader: showLoader$,
      showEmptyDataLoader: showEmptyDataLoader$,
    } as vm">
    @if (vm.showLoader) {
      <gc-loader [label]="t('enclose.is-loading-enclosing-month')" />
    } @else if (vm.showEmptyDataLoader) {
      <gc-loader [label]="t('enclose.is-checking-empty-data')" />
    } @else if (
      vm.enclosureMonth && vm?.enclosureMonthLoadingStatus === 'LOADED'
    ) {
      @if (rangeFormGroup && hasEnclosureMonth) {
        <button
          mat-raised-button
          color="primary"
          (click)="encloseMonth()"
          [disabled]="!rangeFormGroup.valid || !companyId">
          {{ t('enclose.label') }}
          {{
            vm.enclosureMonth
              | translocoDate: { month: 'long', year: 'numeric' }
              | capitalize
          }}
        </button>
      } @else if (!hasEnclosureMonth) {
        <button
          mat-raised-button
          color="primary"
          (click)="onDefaultEncloseClicked()">
          {{ t('enclose.label') }}
        </button>
      }
    }
  </ng-container>
</ng-container>
