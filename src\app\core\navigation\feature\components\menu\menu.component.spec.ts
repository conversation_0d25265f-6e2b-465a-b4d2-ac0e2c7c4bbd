import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { MenuComponent } from './menu.component';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { NavigationRightsService } from '../../services/navigation-rights.service';
import { FlagService } from '../../services/flag.service';
import { UserModulesService } from '../../services/user-modules.service';
import { PushPipe } from '@ngrx/component';
import { of } from 'rxjs';

describe('MenuComponent', () => {
  let spectator: Spectator<MenuComponent>;

  const createComponent = createComponentFactory({
    component: MenuComponent,
    declarations: [MockDirective(TranslocoDirective)],
    imports: [PushPipe],
    mocks: [NavigationRightsService, FlagService, UserModulesService],
    shallow: true,
  });

  beforeEach(() => {
    spectator = createComponent();
    const navigationRightsService = spectator.inject(NavigationRightsService);
    const userModulesService = spectator.inject(UserModulesService);
    const flagService = spectator.inject(FlagService);

    jest
      .spyOn(navigationRightsService, 'canAccessBusinessReview')
      .mockReturnValue(of(false));
    jest
      .spyOn(userModulesService, 'canAccessModule')
      .mockReturnValue(of(false));
    jest.spyOn(flagService, 'canAccessFeature').mockReturnValue(false);
  });

  it('should create the component', () => {
    expect(spectator.component).toBeTruthy();
  });
});
