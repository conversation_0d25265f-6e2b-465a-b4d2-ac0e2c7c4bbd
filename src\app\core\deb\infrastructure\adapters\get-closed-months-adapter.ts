import { inject, Injectable } from '@angular/core';
import { GetClosedMonthsPort } from '../../domains/ports';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { map } from 'rxjs';
import { DateAdapter } from '@gc/shared/utils';
import { ClosedMonth } from '@gc/core/deb/domains/models';

@Injectable()
export class GetClosedMonthsAdapter implements GetClosedMonthsPort {
  private readonly api = inject(DebApiService);

  forCompany(companyId: string, page = 1, pageSize = 100) {
    return this.api.getClosedDebCollection(companyId, page, pageSize).pipe(
      map((closedDebDeclaration) =>
        closedDebDeclaration.items.map(
          (closedMonth) =>
            <ClosedMonth>{
              declarationNumber: closedMonth.declarationNumber,
              declarationMonth: DateAdapter.dateFromStringAPI(
                closedMonth.month
              )!,
            }
        )
      )
    );
  }
}
