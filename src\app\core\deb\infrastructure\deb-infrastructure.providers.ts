/* eslint-disable max-lines-per-function */
import { Provider } from '@angular/core';
import {
  GetClosedMonthsPort,
  GetDestinationCountryCodesPort,
  GetEuNomenclaturesPort,
  GetParametersPort,
  GetStatisticalProceduresPort,
  LoadDetailsPort,
  LoadDraftDeclarationsPort,
  SaveClosurePort,
  SaveParametersPort,
  UncloseMonthPort,
} from '../domains/ports';
import {
  GetClosedMonthsAdapter,
  GetDestinationCountryCodesAdapter,
  GetEuNomenclaturesAdapter,
  GetParametersAdapter,
  StatisticalProceduresAdapter,
  LoadDetailsAdapter,
  LoadDraftDeclarationsAdapter,
  SaveClosureAdapter,
  SaveParametersAdapter,
  UncloseMonthAdapter,
} from './adapters';
import { DebApiService } from '@gc/core/deb/infrastructure/api';

export function provideDebInfrastructure(): Provider[] {
  return [
    DebApiService,
    {
      provide: GetClosedMonthsPort,
      useClass: GetClosedMonthsAdapter,
    },
    {
      provide: GetParametersPort,
      useClass: GetParametersAdapter,
    },
    {
      provide: LoadDraftDeclarationsPort,
      useClass: LoadDraftDeclarationsAdapter,
    },
    {
      provide: SaveClosurePort,
      useClass: SaveClosureAdapter,
    },
    {
      provide: SaveParametersPort,
      useClass: SaveParametersAdapter,
    },
    {
      provide: UncloseMonthPort,
      useClass: UncloseMonthAdapter,
    },
    {
      provide: LoadDetailsPort,
      useClass: LoadDetailsAdapter,
    },
    {
      provide: GetEuNomenclaturesPort,
      useClass: GetEuNomenclaturesAdapter,
    },
    {
      provide: GetStatisticalProceduresPort,
      useClass: StatisticalProceduresAdapter,
    },
    {
      provide: GetDestinationCountryCodesPort,
      useClass: GetDestinationCountryCodesAdapter,
    },
  ];
}
