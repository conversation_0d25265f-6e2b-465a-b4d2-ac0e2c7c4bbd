import { Injectable } from '@angular/core';
import { BaseStore, ResourceState } from '@gc/core/shared/store';
import { Representative } from '../../domains/models';

export enum RepresentativesStoreEnum {
  REPRESENTATIVES = 'REPRESENTATIVES',
  SELECTED_REPRESENTATIVE = 'SELECTED_REPRESENTATIVE',
}

export type RepresentativesState = {
  [RepresentativesStoreEnum.REPRESENTATIVES]: ResourceState<Representative[]>;
  [RepresentativesStoreEnum.SELECTED_REPRESENTATIVE]: ResourceState<Representative>;
};

@Injectable({
  providedIn: 'root',
})
export class RepresentativesStore extends BaseStore<
  typeof RepresentativesStoreEnum,
  RepresentativesState
> {
  constructor() {
    super(RepresentativesStoreEnum);
  }
}
