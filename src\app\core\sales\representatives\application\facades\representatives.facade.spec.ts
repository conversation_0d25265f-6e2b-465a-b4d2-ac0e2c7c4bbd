import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { RepresentativesFacade } from './representatives.facade';
import { RepresentativesStore, RepresentativesStoreEnum } from '../store';
import { of } from 'rxjs';
import { GuidHelper } from '@isagri-ng/core';
import { LoadRepresentativesUseCase } from '../use-cases';
import { Representative } from '../../domains/models';
import { ResourceState } from '@gc/core/shared/store';

describe('RepresentativesFacade', () => {
  let spectator: SpectatorService<RepresentativesFacade>;
  let loadRepresentativesUseCase: LoadRepresentativesUseCase;
  let representativesStore: RepresentativesStore;
  let getRepresentativesForCurrentCompanySpy: jest.SpyInstance;
  let selectedRepresentativeSpy: jest.SpyInstance;

  const mockRepresentative: Representative = {
    id: GuidHelper.newGuid(),
    fullname: '<PERSON>',
    code: 'JD123',
  };

  const createService = createServiceFactory({
    service: RepresentativesFacade,
    mocks: [RepresentativesStore, LoadRepresentativesUseCase],
  });

  beforeEach(() => {
    spectator = createService();
    representativesStore = spectator.inject(RepresentativesStore);
    loadRepresentativesUseCase = spectator.inject(LoadRepresentativesUseCase);

    getRepresentativesForCurrentCompanySpy = jest.spyOn(
      representativesStore,
      'get'
    );
    selectedRepresentativeSpy = jest.spyOn(representativesStore, 'get');
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('getRepresentativesForCurrentCompany', () => {
    it('should call store.get with REPRESENTATIVES enum', () => {
      spectator.service.getRepresentativesForCurrentCompany();
      expect(getRepresentativesForCurrentCompanySpy).toHaveBeenCalledWith(
        RepresentativesStoreEnum.REPRESENTATIVES
      );
    });

    it('should return the result from store.get', () => {
      const mockResourceState: ResourceState<Representative[]> = {
        data: [mockRepresentative],
        isLoading: false,
        status: 'Success',
        errors: undefined,
      };
      getRepresentativesForCurrentCompanySpy.mockReturnValue(mockResourceState);

      const result = spectator.service.getRepresentativesForCurrentCompany();

      expect(result).toBe(mockResourceState);
    });
  });

  describe('loadRepresentativesForCompany', () => {
    it('should call loadRepresentatives.for with companyId', () => {
      const companyId = 'test-company-id';
      const forSpy = jest
        .spyOn(loadRepresentativesUseCase, 'for')
        .mockReturnValue(of([mockRepresentative]));

      spectator.service.loadRepresentativesForCompany(companyId);

      expect(forSpy).toHaveBeenCalledWith(companyId);
    });

    it('should handle the loading state in the store', () => {
      const companyId = 'test-company-id';
      jest
        .spyOn(loadRepresentativesUseCase, 'for')
        .mockReturnValue(of([mockRepresentative]));

      spectator.service.loadRepresentativesForCompany(companyId);

      // Verify that the store is updated with loading state
      expect(representativesStore.startLoading).toHaveBeenCalledWith(
        RepresentativesStoreEnum.REPRESENTATIVES
      );
    });
  });

  describe('getSelectedRepresentative', () => {
    it('should call store.get with SELECTED_REPRESENTATIVE enum', () => {
      spectator.service.getSelectedRepresentative();
      expect(selectedRepresentativeSpy).toHaveBeenCalledWith(
        RepresentativesStoreEnum.SELECTED_REPRESENTATIVE
      );
    });

    it('should return the result from store.get', () => {
      const mockResourceState: ResourceState<Representative> = {
        data: mockRepresentative,
        isLoading: false,
        status: 'Success',
        errors: undefined,
      };
      selectedRepresentativeSpy.mockReturnValue(mockResourceState);

      const result = spectator.service.getSelectedRepresentative();

      expect(result).toBe(mockResourceState);
    });
  });
});
