<gc-no-navigation-container *transloco="let t">
  <div class="modal-overlay">
    <div class="container">
      <h2 class="label">
        {{ t('invalidConfig.page.default-company-and-date-select') }}
      </h2>
      <div class="form">
        <div class="form-fields">
          @if (!isSingleCompany) {
            <p>
              {{ t('invalidConfig.page.form.labels.company-select') }}
            </p>
            <gc-company-single-select
              [formControl]="companyIdControl"
              [inputId]="'companies-select'" />
          }
          <p>
            {{ t('invalidConfig.page.form.labels.date-select') }}
          </p>
          <mat-form-field subscriptSizing="dynamic" appearance="outline">
            <input
              matInput
              ingMatDatepickerDirective
              [matDatepicker]="picker"
              [formControl]="defaultDateControl" />
            <mat-datepicker-toggle matIconSuffix [for]="picker" />
            <mat-datepicker #picker />
          </mat-form-field>
        </div>

        <div class="button-container">
          <button
            [disabled]="
              (companyIdControl.touched || companyIdControl.dirty) &&
              companyIdControl.invalid &&
              (defaultDateControl.touched || defaultDateControl.dirty) &&
              defaultDateControl.invalid
            "
            mat-raised-button
            color="primary"
            (click)="confirmDefaultConfig()"
            cdkFocusInitial>
            {{ t('sharedAction.connect') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</gc-no-navigation-container>
