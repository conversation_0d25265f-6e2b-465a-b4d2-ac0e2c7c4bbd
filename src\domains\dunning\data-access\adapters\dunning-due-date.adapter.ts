import { DunningApi, DunningDueDateApi } from '@gc/shared/api/data-access';
import { DueDate } from '@gc/due-date/models';
import { DateAdapter } from '@gc/shared/utils';
import { DunningDueDate } from '@gc/dunning/models';

export class DunningDueDateAdapter {
  public static fromDueDate(dueDate: DueDate): DunningDueDate {
    const { id, balance, dueDateAmount, date } = dueDate;
    if (!date) {
      throw new Error('Missing "date" property.');
    }
    if (balance === undefined) {
      throw new Error('Missing "balance" property.');
    }
    if (dueDateAmount === undefined) {
      throw new Error('Missing "dueDateAmount" property.');
    }
    return {
      id,
      date,
      balance,
      amount: dueDateAmount,
    };
  }

  public static fromDunningDueDateApi(
    dunningDueDateApi: DunningDueDateApi
  ): DunningDueDate {
    const { id, balance, dueDateAmount } = dunningDueDateApi;
    const date = dunningDueDateApi.date
      ? DateAdapter.dateFromStringAPI(dunningDueDateApi.date)
      : null;
    if (!date) {
      throw new Error('[DunningDueDateAdapter] Missing "date" property');
    }
    if (balance === undefined) {
      throw new Error('[DunningDueDateAdapter] Missing "balance" property');
    }
    if (dueDateAmount === undefined) {
      throw new Error(
        '[DunningDueDateAdapter] Missing "dueDateAmount" property'
      );
    }
    return {
      id,
      date,
      balance,
      amount: dueDateAmount,
      // dueDates returned with dunning from dunningService are due.
      isDue: true,
    };
  }

  public static fromDunningsApi(dunningsApi: DunningApi[]): DunningDueDate[] {
    return dunningsApi.reduce(
      (dueDates: DunningDueDate[], dunningApi: DunningApi) => {
        dueDates.push(
          ...dunningApi.dueDates.map((dunningDueDateApi: DunningDueDateApi) =>
            DunningDueDateAdapter.fromDunningDueDateApi(dunningDueDateApi)
          )
        );
        return dueDates;
      },
      []
    );
  }
}
