import { waitForAsync } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { HeaderTitleService } from '@gc/core/navigation/feature';
import {
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
  REPORT_ID,
  ReportFamilies,
  StimulsoftSupplementaryProperties,
} from '@gc/shared/stimulsoft/models';
import { ActivatedRouteStub } from '@gc/shared/tests';
import {
  createComponentFactory,
  Spectator,
  SpyObject,
} from '@ngneat/spectator/jest';
import { MockComponents } from 'ng-mocks';
import { of } from 'rxjs';
import { StimulsoftViewerComponent } from './stimulsoft-viewer.component';

describe('StimulsoftViewerComponent', () => {
  let spectator: Spectator<StimulsoftViewerComponent>;
  let component: StimulsoftViewerComponent;

  let stimulsoftPropertiesService: StimulsoftPropertiesService;
  let activatedRouteStub: ActivatedRouteStub;

  const createComponent = createComponentFactory({
    component: StimulsoftViewerComponent,
    declarations: [MockComponents(StimulsoftViewerContainerComponent)],
    mocks: [StimulsoftPropertiesService, HeaderTitleService],
    providers: [{ provide: ActivatedRoute, useClass: ActivatedRouteStub }],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    stimulsoftPropertiesService = spectator.inject(StimulsoftPropertiesService);

    activatedRouteStub = spectator.inject(
      ActivatedRoute
    ) as unknown as SpyObject<ActivatedRouteStub>;
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });

  describe('ngOnInit', () => {
    describe('given a state where url contains a report family equal to DETVT, a report id equal to FRM00 and a treatmentId Params', () => {
      const treatmentId = '78878787';
      beforeEach(() => {
        activatedRouteStub.addQueryParam('treatmentId', treatmentId);
        activatedRouteStub.addQueryParam(
          'reportFamily',
          ReportFamilies.DETAILS
        );

        activatedRouteStub.addQueryParam('reportId', REPORT_ID.details);
      });

      describe('and createStimulsoftProperties return the stimulsoft properties', () => {
        const stimulsoftProperties: CoreStimulsoftProperties &
          CommonStimulsoftProperties & { treatmentId: string } = {
          authToken: 'aToken',
          culture: 'fr',
          reportFamily: ReportFamilies.DETAILS,
          reportId: REPORT_ID.details,
          treatmentId: '789878',
        };

        let createStimulsoftPropertiesSpy: jest.SpyInstance<any>;

        beforeEach(() => {
          createStimulsoftPropertiesSpy = jest
            .spyOn(stimulsoftPropertiesService, 'createStimulsoftProperties$')
            .mockReturnValue(of(stimulsoftProperties));
        });

        it('should call createStimulsoftProperties$ method of StimulsoftPropertiesService init stimulsoftProperties$', waitForAsync(() => {
          spectator.detectChanges();
          component.stimulsoftProperties$.subscribe((result) => {
            expect(result).toStrictEqual(stimulsoftProperties);
            expect(createStimulsoftPropertiesSpy).toHaveBeenCalledWith(
              REPORT_ID.details,
              ReportFamilies.DETAILS,
              { treatmentId }
            );
          });
        }));
      });
    });

    describe('given a state where url contains a reportFamily equal to TVAE, a reportId equal to TVAE, a companyId and a dateTo Params', () => {
      const companyId = 'a company id';
      const endDate = '2024-01-31';
      beforeEach(() => {
        activatedRouteStub.addQueryParam('companyId', companyId);
        activatedRouteStub.addQueryParam('dateTo', endDate);
        activatedRouteStub.addQueryParam(
          'reportFamily',
          ReportFamilies.RECEIPTS
        );

        activatedRouteStub.addQueryParam('reportId', REPORT_ID.receipts);
      });

      describe('and createStimulsoftProperties return the stimulsoft properties', () => {
        const stimulsoftProperties: CoreStimulsoftProperties &
          CommonStimulsoftProperties &
          StimulsoftSupplementaryProperties = {
          authToken: 'aToken',
          culture: 'fr',
          reportFamily: ReportFamilies.RECEIPTS,
          reportId: REPORT_ID.receipts,
          GcQueryCompanyId: companyId,
          GcQueryDateTo: endDate,
        };

        let createStimulsoftPropertiesSpy: jest.SpyInstance<any>;

        beforeEach(() => {
          createStimulsoftPropertiesSpy = jest
            .spyOn(stimulsoftPropertiesService, 'createStimulsoftProperties$')
            .mockReturnValue(of(stimulsoftProperties));
        });

        it('should call createStimulsoftProperties$ method of StimulsoftPropertiesService to init stimulsoftProperties$', waitForAsync(() => {
          spectator.detectChanges();
          component.stimulsoftProperties$.subscribe((result) => {
            expect(result).toStrictEqual(stimulsoftProperties);
            expect(createStimulsoftPropertiesSpy).toHaveBeenCalledWith(
              REPORT_ID.receipts,
              ReportFamilies.RECEIPTS,
              {
                GcQueryCompanyId: companyId,
                GcQueryDateTo: endDate,
              }
            );
          });
        }));
      });
    });
  });
});
