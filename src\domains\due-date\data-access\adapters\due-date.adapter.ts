import { DueDateApi } from '@gc/shared/api/data-access';
import { DueDate } from '@gc/due-date/models';
import { DateAdapter } from '@gc/shared/utils';
import { DueDateDocumentTypeAdapter } from './due-date-document-type.adapter';

export class DueDateAdapter {
  public static fromApi(dueDateApi: DueDateApi): DueDate {
    const {
      id,
      date: dateApi,
      balance,
      documentId,
      documentNumber,
      documentAmount,
      customerDocumentId,
      customerDocumentCode,
      rate,
      documentDate: documentDateApi,
      dueDateAmount,
      paymentTypeId,
      companyCode,
    } = dueDateApi;

    if (!id) {
      throw new Error('Missing id in DueDateApi');
    }

    if (!documentDateApi) {
      throw new Error('Missing documentDate in DueDateApi');
    }

    const documentDate: Date | null =
      DateAdapter.dateFromStringAPI(documentDateApi);

    if (!documentDate) {
      throw new Error('documentDate conversion returned null');
    }

    if (!dateApi) {
      throw new Error('Missing date in DueDateApi');
    }

    const date: Date | null = DateAdapter.dateFromStringAPI(dateApi);

    if (!date) {
      throw new Error('date conversion returned null');
    }

    if (!documentNumber) {
      throw new Error('documentNumber conversion returned null');
    }

    if (!customerDocumentCode) {
      throw new Error('customerDocumentCode conversion returned null');
    }

    if (!companyCode) {
      throw new Error('companyCode conversion returned null');
    }

    const documentType = DueDateDocumentTypeAdapter.fromApi(
      dueDateApi.documentType
    );

    return {
      id,
      date,
      balance,
      documentId,
      documentNumber,
      documentDate,
      documentType,
      documentAmount,
      customerDocumentId,
      customerDocumentCode,
      rate,
      dueDateAmount,
      paymentTypeId,
      companyCode,
    };
  }
}
