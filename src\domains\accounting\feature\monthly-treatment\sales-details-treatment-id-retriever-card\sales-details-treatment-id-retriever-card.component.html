@if (loadingFiltersStatus$ | ngrxPush; as loadingFiltersStatus) {
  <gc-monthly-treatment-card
    *transloco="let t; read: 'accounting'"
    [disabled]="
      invalid || !!isLoadingData || loadingFiltersStatus === 'IN_PROGRESS'
    "
    (handleVisualization)="handleVisualization()">
    <ng-container header>
      <span>
        {{ t('monthly-treatment-categories.labels.details') }}
      </span>
    </ng-container>
    <div class="container">
      <div>
        <mat-icon color="primary">filter_list</mat-icon>
      </div>
      @if (loadingFiltersStatus === 'IN_PROGRESS') {
        <gc-loader
          [label]="t('monthly-edition-tab.cards.filters-loader-label')" />
      } @else {
        <mat-checkbox [formControl]="detailsFilterFC" color="primary">
          {{ t('monthly-edition-tab.cards.filters-labels.details') }}
        </mat-checkbox>
      }
    </div>
  </gc-monthly-treatment-card>
}
