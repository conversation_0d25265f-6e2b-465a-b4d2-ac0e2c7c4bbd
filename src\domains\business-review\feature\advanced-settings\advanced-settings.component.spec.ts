import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { AdvancedSettingsComponent } from './advanced-settings.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { MockDirectives } from 'ng-mocks';
import { MatListOption, MatSelectionListChange } from '@angular/material/list';
import { GuidHelper } from '@isagri-ng/core';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { waitForAsync } from '@angular/core/testing';
import { VatService } from '@gc/vat/data-access';
import { LetDirective } from '@ngrx/component';
import { SnackbarService } from '@gc/shared/ui';
import { BusinessReviewFormService } from '../services/business-review-form.service';

describe('AdvancedSettingsComponent', () => {
  let spectator: Spectator<AdvancedSettingsComponent>;
  let businessReviewFormService: BusinessReviewFormService;

  const createComponent = createComponentFactory({
    component: AdvancedSettingsComponent,
    declarations: [MockDirectives(TranslocoDirective, LetDirective)],
    mocks: [FiscalYearService, WarehouseService, VatService, SnackbarService],
  });

  beforeEach(() => {
    spectator = createComponent();
    businessReviewFormService = spectator.inject(BusinessReviewFormService);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    describe('pendingValidationWarehouseIds$$', () => {
      it('should update its value when the formControl value change', waitForAsync(() => {
        const newValue = [
          GuidHelper.newGuid(),
          GuidHelper.newGuid(),
          GuidHelper.newGuid(),
        ];
        expect.assertions(1);
        businessReviewFormService.warehousesFC.setValue(newValue);
        spectator.component.pendingValidationWarehouseIds$$.subscribe(
          (value) => {
            expect(value).toEqual(newValue);
          }
        );
      }));
    });

    describe('pendingValidationVatRate$$', () => {
      it('should update its value when the formControl value change', waitForAsync(() => {
        const newValue = 51.86;
        expect.assertions(1);
        businessReviewFormService.vatRateFC.setValue(newValue);
        spectator.component.pendingValidationVatRate$$.subscribe((value) => {
          expect(value).toEqual(newValue);
        });
      }));
    });
  });

  describe('onMatListOptionChange', () => {
    const event: MatSelectionListChange = {
      options: [
        {
          value: '',
        } as MatListOption,
      ],
    } as MatSelectionListChange;

    describe('when event value is vat', () => {
      it('should set detailPanelTitleKey to the vat transloco key', () => {
        event.options[0].value = 'vat';
        spectator.component.onMatListOptionChange(event);

        expect(spectator.component.detailPanelTitleKey).toBe('panel-title.vat');
      });
    });

    describe('when event value is warehouses', () => {
      it('should set detailPanelTitleKey to the vat transloco key', () => {
        event.options[0].value = 'warehouses';
        spectator.component.onMatListOptionChange(event);

        expect(spectator.component.detailPanelTitleKey).toBe(
          'panel-title.warehouses'
        );
      });
    });

    describe('when event value is not valid', () => {
      it('should set detailPanelTitleKey to undefined', () => {
        event.options[0].value = 'nt_a_valid';
        spectator.component.onMatListOptionChange(event);
        expect(spectator.component.detailPanelTitleKey).toBeUndefined();
      });
    });
  });

  describe('onWarehousesSelectionChange method', () => {
    const selectedIds = [
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
    ];

    it('should update the pending validation list', waitForAsync(() => {
      spectator.component.onWarehouseSelectionChange(selectedIds);
      expect.assertions(1);
      spectator.component.pendingValidationWarehouseIds$$.subscribe((value) =>
        expect(value).toEqual(selectedIds)
      );
    }));
  });

  describe('onVatRateSelectionChange method', () => {
    const selectedVatRate = 94.12;

    it('should update the pending validation rate', waitForAsync(() => {
      spectator.component.onVatRateSelectionChange(94.12);
      expect.assertions(1);
      spectator.component.pendingValidationVatRate$$.subscribe((value) =>
        expect(value).toEqual(selectedVatRate)
      );
    }));
  });

  describe('onCancel Method', () => {
    const initialWarehouseIdsValue = [
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
    ];

    const initialVatRateValue = 61.23;

    const any_vatRate = 88.88;

    const any_ids = [
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
    ];

    beforeEach(() => {
      businessReviewFormService.warehousesFC.setValue(initialWarehouseIdsValue);
      businessReviewFormService.vatRateFC.setValue(initialVatRateValue);

      spectator.component.pendingValidationWarehouseIds$$.next(any_ids);
      spectator.component.pendingValidationVatRate$$.next(any_vatRate);
    });

    it('should reset the warehouses pending validation list value to the service value', waitForAsync(() => {
      spectator.component.onCancel();
      spectator.component.pendingValidationWarehouseIds$$.subscribe((value) => {
        expect(value).toEqual(initialWarehouseIdsValue);
      });
    }));

    it('should reset the vat rate pending validation value to the service value', waitForAsync(() => {
      spectator.component.onCancel();
      spectator.component.pendingValidationVatRate$$.subscribe((value) => {
        expect(value).toEqual(initialVatRateValue);
      });
    }));
  });

  describe('onValidate Method', () => {
    const any_ids = [
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
    ];

    const any_vatRate = 61.86;

    const newVatRateValue = 13.85;

    const newWarehouseIdsValue = [
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
      GuidHelper.newGuid(),
    ];

    describe('when the pending validation values are different from the service values', () => {
      beforeEach(() => {
        businessReviewFormService.warehousesFC.setValue(any_ids);
        businessReviewFormService.vatRateFC.setValue(any_vatRate);

        spectator.component.pendingValidationWarehouseIds$$.next(
          newWarehouseIdsValue
        );
        spectator.component.pendingValidationVatRate$$.next(newVatRateValue);
      });

      it('should update the service formControls values and status', () => {
        spectator.component.onValidate();
        expect(businessReviewFormService.warehousesFC.value).toEqual(
          newWarehouseIdsValue
        );
        expect(businessReviewFormService.warehousesFC.dirty).toBe(true);
        expect(businessReviewFormService.vatRateFC.value).toEqual(
          newVatRateValue
        );
      });
    });

    describe('when the pending validation values equals to the service values', () => {
      beforeEach(() => {
        businessReviewFormService.warehousesFC.setValue(newWarehouseIdsValue);
        businessReviewFormService.vatRateFC.setValue(newVatRateValue);

        spectator.component.pendingValidationWarehouseIds$$.next(
          newWarehouseIdsValue
        );
        spectator.component.pendingValidationVatRate$$.next(newVatRateValue);
      });

      it('should not update the service formControls status', () => {
        spectator.component.onValidate();
        expect(businessReviewFormService.warehousesFC.dirty).toBe(false);
      });
    });
  });
});
