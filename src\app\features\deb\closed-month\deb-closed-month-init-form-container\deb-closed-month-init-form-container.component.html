<ng-container *transloco="let t">
  <div class="container">
    <div mat-dialog-title class="title-container">
      {{ t('sharedDialog.titles.info') }}
    </div>
    <mat-dialog-content class="content-container">
      <div class="message-container">
        {{ t('deb.deb-closed-month-init-dialog.information') }}
      </div>
      <gc-deb-closed-month-init-form (closedMonth)="onClosedMonth($event)" />
    </mat-dialog-content>
  </div>
</ng-container>
