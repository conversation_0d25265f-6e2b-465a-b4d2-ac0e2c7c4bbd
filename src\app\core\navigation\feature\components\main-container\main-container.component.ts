import { Component, inject, input } from '@angular/core';
import { NgClass } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { Router } from '@angular/router';
import { IAuthentication } from '@isagri-ng/security';
import { TranslocoModule } from '@jsverse/transloco';
import { HeaderComponent } from '../header/header.component';
import { MenuComponent } from '../menu/menu.component';

@Component({
  selector: 'gc-main-container',
  templateUrl: './main-container.component.html',
  styleUrls: [
    './main-container.component.scss',
    './main-container-theme.component.scss',
  ],
  standalone: true,
  imports: [
    TranslocoModule,
    MatSidenavModule,
    MenuComponent,
    HeaderComponent,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    NgClass,
  ],
})
export class MainContainerComponent {
  private readonly _iAuthentication = inject(IAuthentication);
  private readonly _router = inject(Router);

  scrollable = input<boolean>(false);

  logout(): void {
    this._iAuthentication.logout();
  }
}
