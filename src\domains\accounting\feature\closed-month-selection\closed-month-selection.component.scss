:host {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .header {
        width: 100%;
        display: flex;
        gap: 5px;

        .select-company-container {
            width: 70%;
        }

        .select-year-container {
            width: 30%;

            mat-form-field {
                width: 100%;
            }
        }
    }

    .content {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 5px;
        overflow: hidden;

        .select-month-container {
            overflow-y: auto;
            flex: 1;

            mat-radio-group {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }
        }
    }
}
