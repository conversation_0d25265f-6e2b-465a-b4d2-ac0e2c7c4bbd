import { Component, inject } from '@angular/core';
import { NavigationBackContainerComponent } from '@gc/core/navigation/feature';
import {
  StimulsoftNavigationService,
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
} from '@gc/shared/stimulsoft/models';
import { TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { combineLatest, Observable, switchMap } from 'rxjs';
import { ReportSupplementaryStimulsoftProperties } from './models/report-supplementary-stimulsoft-properties.model';
import { ReportStimulsoftNavigationService } from './services/report-stimulsoft-navigation.service';

type StimulsoftViewerProperties = CoreStimulsoftProperties &
  CommonStimulsoftProperties &
  ReportSupplementaryStimulsoftProperties;

@Component({
  selector: 'gc-report',
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.scss'],
  standalone: true,
  imports: [
    TranslocoModule,
    NavigationBackContainerComponent,
    StimulsoftViewerContainerComponent,
    PushPipe,
  ],
})
export class ReportComponent {
  private readonly _stimulsoftPropertiesService = inject(
    StimulsoftPropertiesService
  );
  private readonly _stimulsoftNavigationService = inject(
    StimulsoftNavigationService
  );
  private readonly _reportStimulsoftNavigationService = inject(
    ReportStimulsoftNavigationService
  );

  stimulsoftProperties$: Observable<StimulsoftViewerProperties> = combineLatest(
    [
      this._stimulsoftNavigationService.getCommonStimulsoftPropertiesFromQueryParams$(),
      this._reportStimulsoftNavigationService.getReportStimulsoftPropertiesFromQueryParams$(),
    ]
  ).pipe(
    switchMap(
      ([commonStimulsoftProperties, reportStimulsoftProperties]: [
        CommonStimulsoftProperties,
        ReportSupplementaryStimulsoftProperties,
      ]) =>
        this._stimulsoftPropertiesService.createStimulsoftProperties$(
          commonStimulsoftProperties.reportId,
          commonStimulsoftProperties.reportFamily,
          reportStimulsoftProperties
        )
    )
  );
}
