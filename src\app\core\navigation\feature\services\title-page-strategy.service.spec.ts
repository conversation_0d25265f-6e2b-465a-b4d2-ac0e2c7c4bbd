import { waitForAsync } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { TitleKeyTab } from '@gc/core/navigation/models';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Observable, of } from 'rxjs';
import { TitlePageStrategyService } from './title-page-strategy.service';

describe('TitlePageStrategyService', () => {
  let spectator: SpectatorService<TitlePageStrategyService>;

  let mockRouterState: RouterStateSnapshot;

  let setTitleSpy: jest.SpyInstance<void>;
  let buildTitleSpy: jest.SpyInstance<string | undefined>;
  let selectTranslateSpy: jest.SpyInstance<Observable<string>>;

  let titleService: Title;
  let translocoService: TranslocoService;
  let titlePageStrategyService: TitlePageStrategyService;

  const createService = createServiceFactory({
    service: TitlePageStrategyService,
    mocks: [Title, TranslocoService],
  });

  beforeEach(() => {
    spectator = createService();
    titlePageStrategyService = spectator.service;

    titleService = spectator.inject(Title);
    setTitleSpy = jest.spyOn(titleService, 'setTitle');

    translocoService = spectator.inject(TranslocoService);

    mockRouterState = {
      url: '/mock-url',
      root: {} as ActivatedRouteSnapshot,
    };

    buildTitleSpy = jest.spyOn(titlePageStrategyService, 'buildTitle');
    selectTranslateSpy = jest.spyOn(
      translocoService,
      'selectTranslate'
    ) as jest.SpyInstance<Observable<string>>;
  });

  describe('updateTitle method', () => {
    describe('given a state with buildTitle which returns a suffixKeyTransloco', () => {
      describe('and selectTranslate returns the title translated', () => {
        const translocoKey = TitleKeyTab.DUNNING;
        const titleTrad = 'Relance';
        beforeEach(() => {
          buildTitleSpy.mockReturnValue(translocoKey);
          selectTranslateSpy.mockReturnValue(of(titleTrad));
        });
        describe('when method is called with a RouterStateSnapshot', () => {
          it('should call setTitleSpy method with the title translated', waitForAsync(() => {
            const expectedAssertions = 3;
            expect.assertions(expectedAssertions);

            titlePageStrategyService.updateTitle(mockRouterState);

            expect(buildTitleSpy).toHaveBeenCalledWith(mockRouterState);
            expect(selectTranslateSpy).toHaveBeenCalledWith(translocoKey);
            expect(setTitleSpy).toHaveBeenCalledWith(titleTrad);
          }));
        });
      });
    });

    describe('given a state where buildTitle returns a string which is not a transloco key', () => {
      const title = 'Ce titre ne necessite pas de traduction';
      beforeEach(() => {
        buildTitleSpy.mockReturnValue(title);
      });
      describe('when method is called with a RouterStateSnapshot', () => {
        it('should call setTitleSpy method with the title translated', waitForAsync(() => {
          const expectedAssertions = 3;
          expect.assertions(expectedAssertions);

          titlePageStrategyService.updateTitle(mockRouterState);

          expect(buildTitleSpy).toHaveBeenCalledWith(mockRouterState);
          expect(selectTranslateSpy).not.toHaveBeenCalled();
          expect(setTitleSpy).toHaveBeenCalledWith(title);
        }));
      });
    });

    describe('given a state with buildTitle which returns undefined', () => {
      beforeEach(() => {
        buildTitleSpy.mockReturnValue(undefined);
      });
      describe('when method is called with a RouterStateSnapshot', () => {
        it("shouldn't call setTitleSpy method", waitForAsync(() => {
          const expectedAssertions = 3;
          expect.assertions(expectedAssertions);

          titlePageStrategyService.updateTitle(mockRouterState);

          expect(buildTitleSpy).toHaveBeenCalledWith(mockRouterState);
          expect(selectTranslateSpy).not.toHaveBeenCalled();
          expect(setTitleSpy).not.toHaveBeenCalledWith();
        }));
      });
    });
  });
});
