import { companiesIdFixture } from '@gc/company/models';
import {
  StimulsoftNavigationService,
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
} from '@gc/shared/stimulsoft/models';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { PushPipe } from '@ngrx/component';
import { MockComponents, MockPipes } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { ReportSupplementaryStimulsoftProperties } from './models/report-supplementary-stimulsoft-properties.model';
import { ReportComponent } from './report.component';
import { ReportStimulsoftNavigationService } from './services/report-stimulsoft-navigation.service';

describe('ReportComponent', () => {
  let spectator: Spectator<ReportComponent>;

  let stimulsoftNavigationService: StimulsoftNavigationService;
  let reportStimulsoftNavigationService: ReportStimulsoftNavigationService;
  let stimulsoftPropertiesService: StimulsoftPropertiesService;

  const createComponent = createComponentFactory({
    component: ReportComponent,
    declarations: [
      MockComponents(StimulsoftViewerContainerComponent),
      MockPipes(PushPipe),
    ],
    mocks: [
      StimulsoftNavigationService,
      ReportStimulsoftNavigationService,
      StimulsoftPropertiesService,
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    stimulsoftNavigationService = spectator.inject(StimulsoftNavigationService);
    reportStimulsoftNavigationService = spectator.inject(
      ReportStimulsoftNavigationService
    );
    stimulsoftPropertiesService = spectator.inject(StimulsoftPropertiesService);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
    expect(spectator.component.stimulsoftProperties$).toBeDefined();
  });

  describe('stimulsoftProperties$', () => {
    const commonStimulsoftProperties: CommonStimulsoftProperties = {
      reportId: 'REPORT_ID',
      reportFamily: 'REPORT_FAMILY',
    };

    const companiesIds = companiesIdFixture();
    const reportSupplementaryStimulsoftProperties: ReportSupplementaryStimulsoftProperties =
      {
        companiesIds: companiesIds,
      };

    const fullStimulsoftProperties: CoreStimulsoftProperties &
      CommonStimulsoftProperties &
      ReportSupplementaryStimulsoftProperties = {
      authToken: 'Bearer 123245654987',
      culture: 'fr-FR',
      reportId: commonStimulsoftProperties.reportId,
      reportFamily: commonStimulsoftProperties.reportFamily,
      companiesIds: reportSupplementaryStimulsoftProperties.companiesIds,
    };

    it('should emit the stimulsoft properties once all involved services emits', () => {
      jest
        .spyOn(stimulsoftPropertiesService, 'createStimulsoftProperties$')
        .mockReturnValue(of(fullStimulsoftProperties));

      jest
        .spyOn(
          stimulsoftNavigationService,
          'getCommonStimulsoftPropertiesFromQueryParams$'
        )
        .mockReturnValue(
          of(commonStimulsoftProperties)
        ) as unknown as jest.SpyInstance<
        Observable<CommonStimulsoftProperties>
      >;

      jest.spyOn(
        reportStimulsoftNavigationService,
        'getReportStimulsoftPropertiesFromQueryParams$'
      );

      spectator.detectChanges();

      spectator.component.stimulsoftProperties$.subscribe((value) => {
        expect(value).toStrictEqual(fullStimulsoftProperties);
      });
    });
  });
});
