import { Dunning<PERSON>ilter<PERSON><PERSON> } from '@gc/shared/api/data-access';
import { DateAdapter, PaginationAdapter } from '@gc/shared/utils';
import { DunningListState } from '../stores/dunning-list/models/dunning-list-state.model';
import { DunningFilter } from '@gc/dunning/models';

export class DunningFilterAdapter {
  public static toApi(dunningFilter: DunningFilter): DunningFilterApi {
    const {
      firstElementNumber,
      elementsPerPage,
      customerIds,
      businessTypeIds,
      companyIds: enterpriseIds,
      invoicesDueUpTo,
      totalAmount,
    } = dunningFilter;
    const invoicesDueUpToString = DateAdapter.dateToStringAPI(invoicesDueUpTo);
    if (!invoicesDueUpToString) {
      throw new Error('Date écheance obligatoire ou invalide !');
    }
    return {
      firstElementNumber,
      elementsPerPage,
      customerIds,
      businessTypeIds,
      enterpriseIds,
      invoicesDueUpTo: invoicesDueUpToString,
      totalAmount,
    };
  }

  public static fromDunningState(
    dunningState: DunningListState
  ): DunningFilter {
    const { currentPage, elementsPerPage, filters, defaultDueDateFrom } =
      dunningState;

    return {
      firstElementNumber: PaginationAdapter.calculateFirstElementNumber(
        currentPage,
        elementsPerPage
      ),
      elementsPerPage,
      invoicesDueUpTo: filters?.dueDateFrom ?? defaultDueDateFrom,
      companyIds: filters?.companiesId,
    };
  }
}
