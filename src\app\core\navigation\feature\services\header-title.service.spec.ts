import { waitForAsync } from '@angular/core/testing';
import { TranslocoService } from '@jsverse/transloco';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { HeaderTitleService } from './header-title.service';
import { Observable, of } from 'rxjs';

describe('HeaderTitleService', () => {
  let translocoService: TranslocoService;
  let headerTitleService: HeaderTitleService;
  let spectator: SpectatorService<HeaderTitleService>;
  let selectTranslateSpy: jest.SpyInstance<Observable<string>>;

  const createService = createServiceFactory({
    service: HeaderTitleService,
    mocks: [TranslocoService],
  });

  beforeEach(() => {
    spectator = createService();
    headerTitleService = spectator.service;
    translocoService = spectator.inject(TranslocoService);
    selectTranslateSpy = jest.spyOn(
      translocoService,
      'selectTranslate'
    ) as jest.SpyInstance<Observable<string>>;
  });

  it('should be created', () => {
    expect(headerTitleService).toBeTruthy();
  });

  describe('given a state with a titleKey empty', () => {
    const title = '';
    beforeEach(() => {
      selectTranslateSpy.mockReturnValue(of(title));
    });

    it('should be a empty string', waitForAsync(() => {
      const expectedTitle = title;
      headerTitleService.title$.subscribe((_title) => {
        expect(_title).toStrictEqual(expectedTitle);
      });
    }));
  });

  describe('given a state with a titleKey not empty', () => {
    const titleKey = 'header.titles.dunning';
    const title = 'Relance';
    beforeEach(() => {
      selectTranslateSpy.mockReturnValue(of(title));
    });

    it('should be a empty string', waitForAsync(() => {
      const expectedTitle = title;
      headerTitleService.updateCurrentTitle(titleKey);
      headerTitleService.title$.subscribe((_title) => {
        expect(_title).toStrictEqual(expectedTitle);
      });
    }));
  });
});
