import { waitForAsync } from '@angular/core/testing';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentService,
  MonthlyTreatmentStoreModule,
} from '@gc/accounting/data-access';
import { AUTO_SIZES_DIALOG_CONFIG, DialogService } from '@gc/shared/ui';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { Store } from '@ngrx/store';
import { MockModule, MockPipe } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { DefaultEnclosureMonthSelectionDialogComponent } from '../default-enclosure-month-selection-dialog/default-enclosure-month-selection-dialog.component';
import { EncloseMonthButtonComponent } from './enclose-month-button.component';
import { IDocumentViewerManager } from '@isagri-ng/document-viewer';
import { DocumentViewerManagerStub } from '@gc/core/documents-viewer/utils';
import { MonthlyAccountingReportDocumentsIds } from '@gc/accounting/models';

describe('EncloseMonthButtonComponent', () => {
  let component: EncloseMonthButtonComponent;
  let spectator: Spectator<EncloseMonthButtonComponent>;

  let translocoService: TranslocoService;
  let dialogService: DialogService;
  let store: Store;

  let dispatchSpy: jest.SpyInstance<void>;
  let selectSpy: jest.SpyInstance;

  let handleEmptyEditionSpy: jest.SpyInstance<void>;
  let handleEnclosingSuccessSpy: jest.SpyInstance<void>;
  let handleDefaultMonthSelectionSpy: jest.SpyInstance<void>;

  const createComponent = createComponentFactory({
    component: EncloseMonthButtonComponent,
    declarations: [
      MockPipe(TranslocoDatePipe),
      MockModule(MonthlyTreatmentStoreModule),
    ],
    mocks: [DialogService, Store, TranslocoService, MonthlyTreatmentService],
    detectChanges: false,
    providers: [
      {
        provide: IDocumentViewerManager,
        useClass: DocumentViewerManagerStub,
      },
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    store = spectator.inject(Store);
    translocoService = spectator.inject(TranslocoService);
    dialogService = spectator.inject(DialogService);
    dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();
    selectSpy = jest.spyOn(store, 'select');
    selectSpy.mockReturnValue(of(void 0));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    beforeEach(() => {
      handleEmptyEditionSpy = jest
        .spyOn(component, 'handleEmptyEdition')
        .mockImplementation();

      handleEnclosingSuccessSpy = jest
        .spyOn(component, 'handleEnclosingSuccess')
        .mockImplementation();

      handleDefaultMonthSelectionSpy = jest
        .spyOn(component, 'handleDefaultMonthSelection')
        .mockImplementation();
    });
    it('should call handleEmptyEdition and handleEnclosingSuccess methods', () => {
      component.ngOnInit();
      expect(handleEmptyEditionSpy).toHaveBeenCalled();
      expect(handleEnclosingSuccessSpy).toHaveBeenCalled();
      expect(handleDefaultMonthSelectionSpy).toHaveBeenCalled();
    });
  });

  describe('encloseMonth', () => {
    it('should call MonthlyTreatmentActions.encloseMonth dispatch', () => {
      component.encloseMonth();

      expect(dispatchSpy).toHaveBeenCalledWith(
        monthlyTreatmentActions.loadHasEmptyEdition()
      );
    });
  });

  describe('handleEmptyEdition', () => {
    describe('given a state where emptyEditions is true', () => {
      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(true));
      });

      describe('given confirmation pop up is confirmed', () => {
        beforeEach(() => {
          jest
            .spyOn<any, any>(component, '_displayEmptyEditionsPopUp')
            .mockReturnValue(of(true));
        });

        it('should dispatch encloseMonth and resetEmptyEditions action', () => {
          dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

          component.handleEmptyEdition();

          expect(dispatchSpy).toHaveBeenCalledWith(
            monthlyTreatmentActions.encloseMonth()
          );
          expect(dispatchSpy).toHaveBeenCalledWith(
            monthlyTreatmentActions.resetEmptyEditions()
          );
        });
      });

      describe('given confirmation pop up is canceled', () => {
        beforeEach(() => {
          jest
            .spyOn<any, any>(component, '_displayEmptyEditionsPopUp')
            .mockReturnValue(of(false));
        });

        it('should dispatch resetEmptyEditions action', waitForAsync(() => {
          dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

          component.handleEmptyEdition();

          expect(dispatchSpy).not.toHaveBeenCalledWith(
            monthlyTreatmentActions.encloseMonth()
          );
          expect(dispatchSpy).toHaveBeenCalledWith(
            monthlyTreatmentActions.resetEmptyEditions()
          );
        }));
      });
    });
  });

  describe('handleEnclosingSuccess', () => {
    describe('given a state where enclosingStatus is DONE', () => {
      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of('DONE'));
      });

      describe('and selectTranslate return the enclosing success message', () => {
        const messageConfirm = 'message confirmation';
        beforeEach(() => {
          jest
            .spyOn(translocoService, 'selectTranslate')
            .mockReturnValue(of(messageConfirm));
        });
        describe('and dialog confirm return false', () => {
          let confirmSpy: jest.SpyInstance<Observable<boolean>>;
          beforeEach(() => {
            confirmSpy = jest
              .spyOn(dialogService, 'confirm')
              .mockReturnValue(of(false));
          });

          it('should display confirm dialog, dispatch resetEnclosingStatus action and not call openEditions method', () => {
            dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();
            const openEditionsSpy = jest
              .spyOn(component as any, '_openEditions')
              .mockImplementation();

            component.handleEnclosingSuccess();

            expect(confirmSpy).toHaveBeenCalledWith(
              messageConfirm,
              'action-button-label.consult',
              'action-button-label.ok',
              'titles.info'
            );
            expect(dispatchSpy).toHaveBeenCalledWith(
              monthlyTreatmentActions.resetEnclosingStatus()
            );
            expect(openEditionsSpy).not.toHaveBeenCalled();
          });
        });

        describe('and dialog confirm return true', () => {
          let confirmSpy: jest.SpyInstance<Observable<boolean>>;

          beforeEach(() => {
            confirmSpy = jest
              .spyOn(dialogService, 'confirm')
              .mockReturnValue(of(true));
          });

          it('should display confirm dialog, dispatch resetEnclosingStatus action and call openEditions method', () => {
            dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();
            const openEditionsSpy = jest
              .spyOn(component as any, '_openEditions')
              .mockImplementation();

            component.handleEnclosingSuccess();

            expect(confirmSpy).toHaveBeenCalledWith(
              messageConfirm,
              'action-button-label.consult',
              'action-button-label.ok',
              'titles.info'
            );
            expect(dispatchSpy).toHaveBeenCalledWith(
              monthlyTreatmentActions.resetEnclosingStatus()
            );
            expect(openEditionsSpy).toHaveBeenCalled();
          });
        });
      });
    });
  });

  describe('handleDefaultMonthSelection', () => {
    describe('given a state without enclosureMonth', () => {
      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(false));
      });

      it('should display an open dialog and dispatch encloseDefaultMonth action', waitForAsync(() => {
        const selectedMonth = new Date('2024-08-21');

        const openDialogSpy = jest
          .spyOn(dialogService, 'open')
          .mockReturnValue(of(selectedMonth));

        dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

        component.handleDefaultMonthSelection();

        expect(openDialogSpy).toHaveBeenCalledWith(
          DefaultEnclosureMonthSelectionDialogComponent,
          void 0,
          AUTO_SIZES_DIALOG_CONFIG
        );
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.encloseDefaultMonth({
            defaultMonth: selectedMonth,
          })
        );
      }));
    });
    describe('given a state with a enclosureMonth', () => {
      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(true));
      });

      it("shouldn't display an open dialog and dispatch encloseDefaultMonth action", waitForAsync(() => {
        const selectedMonth = new Date('2024-08-21');

        const openDialogSpy = jest
          .spyOn(dialogService, 'open')
          .mockReturnValue(of(selectedMonth));

        dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

        component.handleDefaultMonthSelection();

        expect(openDialogSpy).not.toHaveBeenCalled();
        expect(dispatchSpy).not.toHaveBeenCalledWith(
          monthlyTreatmentActions.encloseDefaultMonth({
            defaultMonth: selectedMonth,
          })
        );
      }));
    });
  });

  describe('onDefaultEncloseClicked', () => {
    describe('given a state with selectedMonth selected', () => {
      it('should display an open dialog and dispatch encloseDefaultMonth action', waitForAsync(() => {
        const selectedMonth = new Date('2024-08-21');

        const openDialogSpy = jest
          .spyOn(dialogService, 'open')
          .mockReturnValue(of(selectedMonth));

        dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

        component.onDefaultEncloseClicked();

        expect(openDialogSpy).toHaveBeenCalledWith(
          DefaultEnclosureMonthSelectionDialogComponent,
          void 0,
          AUTO_SIZES_DIALOG_CONFIG
        );
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.encloseDefaultMonth({
            defaultMonth: selectedMonth,
          })
        );
      }));
    });
    describe('given a state without a selectedMonth selected', () => {
      it("shouldn't display an open dialog and dispatch encloseDefaultMonth action", waitForAsync(() => {
        const openDialogSpy = jest
          .spyOn(dialogService, 'open')
          .mockReturnValue(of(undefined));

        dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

        component.onDefaultEncloseClicked();

        expect(openDialogSpy).toHaveBeenCalled();
        expect(dispatchSpy).not.toHaveBeenCalled();
      }));
    });
  });

  describe('_displayEmptyEditionsPopUp', () => {
    it('should call dialogService confirm with warning message', waitForAsync(() => {
      const confirmSpy = jest
        .spyOn(dialogService, 'confirm')
        .mockReturnValue(of(true));

      jest
        .spyOn(translocoService, 'selectTranslate')
        .mockReturnValue(
          of('accounting.monthly-edition-tab.editions.empty-editions')
        );

      component['_displayEmptyEditionsPopUp'](true).subscribe(() => {
        expect(confirmSpy).toHaveBeenCalledWith(
          'accounting.monthly-edition-tab.editions.empty-editions'
        );
      });
    }));

    it('should call dialogService confirm with confirmation message', waitForAsync(() => {
      const confirmSpy = jest
        .spyOn(dialogService, 'confirm')
        .mockReturnValue(of(false));

      jest
        .spyOn(translocoService, 'selectTranslate')
        .mockReturnValue(of('accounting.monthly-edition-tab.editions.confirm'));

      component['_displayEmptyEditionsPopUp'](false).subscribe(() => {
        expect(confirmSpy).toHaveBeenCalledWith(
          'accounting.monthly-edition-tab.editions.confirm'
        );
      });
    }));
  });

  describe('_openEditions', () => {
    let documentViewerOpenSpy: jest.SpyInstance<void>;
    beforeEach(() => {
      const documentViewerManager = spectator.inject(IDocumentViewerManager);
      documentViewerOpenSpy = jest.spyOn(documentViewerManager, 'open');
    });
    describe('given a state where selectCompanyIdAndLastEnclosedMonth return a company and the last enclosed month', () => {
      const lastEnclosedMonth = new Date('2024-01-01');
      const companyId = 'aCompanyId';

      beforeEach(() => {
        selectSpy.mockReturnValueOnce(of({ companyId, lastEnclosedMonth }));
      });

      describe('and a state where getExistingReportsIds of MonthlyTreatmentService return the corresponding report ids', () => {
        const reportDocumentsIds: MonthlyAccountingReportDocumentsIds = {
          saleDetailsId: 'bb263db1-f2c5-4132-8f78-7b47447e1807;01',
          categorySalesId: '48d3d80-4554-4e5d-b7a7-64ee5bzd5c06;01',
        };

        beforeEach(() => {
          const monthlyTreatmentService = spectator.inject(
            MonthlyTreatmentService
          );
          jest
            .spyOn(monthlyTreatmentService, 'getExistingReportsIds')
            .mockReturnValue(of(reportDocumentsIds));
        });

        it('should call open method of DocumentViewerManager twice with the report ids', waitForAsync(() => {
          (component as any)._openEditions();
          expect(documentViewerOpenSpy).toHaveBeenCalledWith(
            reportDocumentsIds.categorySalesId
          );
          expect(documentViewerOpenSpy).toHaveBeenCalledWith(
            reportDocumentsIds.saleDetailsId
          );
        }));
      });
    });
  });
});
