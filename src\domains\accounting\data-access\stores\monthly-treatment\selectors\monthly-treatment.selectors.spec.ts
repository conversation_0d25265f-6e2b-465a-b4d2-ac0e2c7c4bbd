import {
  MonthlyTreatmentRange,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { MonthlyTreatmentState } from '../models/monthly-treatment-state.model';
import { initialState } from '../reducers/monthly-treatment.reducer';

import {
  selectDefaultFiltersLoadingStatus,
  selectEmptyEditions,
  selectEmptyEditionsLoadingStatus,
  selectEnclosingDefaultStatus,
  selectEnclosingStatus,
  selectEnclosureMonth,
  selectEnclosureMonthLoadingStatus,
  selectHasEnclosureMonth,
  selectMonthlyTreatmentCompanyId,
  selectMonthlyTreatmentDateRange,
  selectMonthlyTreatmentDateRangeAndCompanyId,
  selectSalesByProductCurrentFilter,
  selectSalesDetailsCurrentFilter,
  selectCompanyIdAndLastEnclosedMonth,
} from './monthly-treatment.selectors';

describe('MonthlyTreatmentSelectors', () => {
  [
    {
      currentState: {
        ...initialState,
        companyId: 'a company id',
      } as MonthlyTreatmentState,
      selector: selectMonthlyTreatmentCompanyId,
      expectedResult: 'a company id',
      selectTitle: 'selectMonthlyTreatmentCompanyId',
      givenTitle: 'given a current state with an companyId set',
      shouldTitle: 'should return the companyId value',
    },
    {
      currentState: {
        ...initialState,
        range: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
      } as MonthlyTreatmentState,
      selector: selectMonthlyTreatmentDateRange,
      expectedResult: {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31'),
      } as MonthlyTreatmentRange,
      selectTitle: 'selectMonthlyTreatmentDateRange',
      givenTitle: 'given a current state with a range set',
      shouldTitle: 'should return the start and end values of range',
    },
    {
      currentState: {
        ...initialState,
        range: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        companyId: 'a companyId',
      } as MonthlyTreatmentState,
      selector: selectMonthlyTreatmentDateRangeAndCompanyId,
      expectedResult: {
        range: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
        companyId: 'a companyId',
      } as { range: MonthlyTreatmentRange; companyId: string },
      selectTitle: 'selectMonthlyTreatmentDateRangeAndCompanyId',
      givenTitle: 'given a current state with a range and a companyId set',
      shouldTitle: 'should return the range and companyId',
    },
    {
      currentState: {
        ...initialState,
        salesByProductCurrentFilter: [ProductCharacteristicEnum.CUSTOM_0],
      } as MonthlyTreatmentState,
      selector: selectSalesByProductCurrentFilter,
      expectedResult: [ProductCharacteristicEnum.CUSTOM_0],
      selectTitle: 'salesByProductCurrentFilter',
      givenTitle:
        'given a current state with a salesByProductCurrentFilter set',
      shouldTitle: 'should return the salesByProductCurrentFilter value',
    },
    {
      currentState: {
        ...initialState,
        salesDetailsCurrentFilter: SalesDetailsOrderByEnum.BUSINESS_TYPE,
      } as MonthlyTreatmentState,
      selector: selectSalesDetailsCurrentFilter,
      expectedResult: true,
      selectTitle: 'salesDetailsCurrentFilter',
      givenTitle: 'given a current state with a salesDetailsCurrentFilter set',
      shouldTitle: 'should return the salesDetailsCurrentFilter value',
    },
    {
      currentState: {
        ...initialState,
        defaultFiltersLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      selector: selectDefaultFiltersLoadingStatus,
      expectedResult: 'IN_PROGRESS',
      selectTitle: 'selectDefaultFiltersLoadingStatus',
      givenTitle:
        'given a current state with a defaultFiltersLoadingStatus set to "IN_PROGRESS"',
      shouldTitle: 'should return "IN_PROGRESS"',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentState,
      selector: selectEnclosureMonth,
      expectedResult: new Date('2024-01-01'),
      selectTitle: 'selectEnclosureMonth',
      givenTitle: 'given a current state with a enclosureMonth set',
      shouldTitle: 'should return the enclosureMonth',
    },
    {
      currentState: {
        ...initialState,
        emptyEditions: true,
      } as MonthlyTreatmentState,
      selector: selectEmptyEditions,
      expectedResult: true,
      selectTitle: 'selectEmptyEditions',
      givenTitle: 'given a current state with an emptyEdition set',
      shouldTitle: 'should return the emptyEdition',
    },
    {
      currentState: {
        ...initialState,
        emptyEditionsLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      selector: selectEmptyEditionsLoadingStatus,
      expectedResult: 'IN_PROGRESS',
      selectTitle: 'selectEmptyEditionsLoadingStatus',
      givenTitle:
        'given a current state with an emptyEditionsLoadingStatus set to IN_PROGRESS',
      shouldTitle: 'should return IN_PROGRESS',
    },
    {
      currentState: {
        ...initialState,
        enclosingStatus: 'DONE',
      } as MonthlyTreatmentState,
      selector: selectEnclosingStatus,
      expectedResult: 'DONE',
      selectTitle: 'selectEnclosingStatus',
      givenTitle: 'given a current state with an enclosingStatus set',
      shouldTitle: 'should return the enclosingStatus',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'LOADED',
      } as MonthlyTreatmentState,
      selector: selectEnclosureMonthLoadingStatus,
      expectedResult: 'LOADED',
      selectTitle: 'selectEnclosingStatus',
      givenTitle:
        'given a current state with an enclosureMonthLoadingStatus set to LOADED',
      shouldTitle: 'should return the enclosureMonthLoadingStatus to LOADED',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'NOT_LOADED',
      } as MonthlyTreatmentState,
      selector: selectHasEnclosureMonth,
      expectedResult: null,
      selectTitle: 'selectEnclosingStatus',
      givenTitle:
        'given a current state with an enclosureMonthLoadingStatus set to NOT_LOADED',
      shouldTitle:
        'should return null when the enclosureMonthLoadingStatus is NOT_LOADED',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'LOADED',
        hasAlreadyEnclosedMonths: true,
        enclosureMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentState,
      selector: selectHasEnclosureMonth,
      expectedResult: true,
      selectTitle: 'selectEnclosingStatus',
      givenTitle:
        'given a current state with an enclosureMonthLoadingStatus set to LOADED, and an enclosureMonth set',
      shouldTitle:
        'should return true when the enclosureMonthLoadingStatus is LOADED and hasAlreadyEnclosedMonths is true',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'LOADED',
        hasAlreadyEnclosedMonths: false,
        enclosureMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentState,
      selector: selectHasEnclosureMonth,
      expectedResult: false,
      selectTitle: 'selectEnclosingStatus',
      givenTitle:
        'given a current state with an enclosureMonthLoadingStatus set to LOADED, and an enclosureMonth set',
      shouldTitle:
        'should return false when the enclosureMonthLoadingStatus is LOADED and hasAlreadyEnclosedMonths is false',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'LOADED',
        enclosureMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentState,
      selector: selectHasEnclosureMonth,
      expectedResult: undefined,
      selectTitle: 'selectEnclosingStatus',
      givenTitle:
        'given a current state with an enclosureMonthLoadingStatus set to LOADED, and an enclosureMonth set',
      shouldTitle:
        "should return undefined when the enclosureMonthLoadingStatus is don't LOADED",
    },
    {
      currentState: {
        ...initialState,
        enclosingDefaultStatus: 'DONE',
      } as MonthlyTreatmentState,
      selector: selectEnclosingDefaultStatus,
      expectedResult: 'DONE',
      selectTitle: 'selectEnclosingDefaultStatus',
      givenTitle: 'given a state with enclosingDefaultStatus set to DONE',
      shouldTitle: 'should return the enclosingDefaultStatus to DONE',
    },
    {
      currentState: {
        ...initialState,
        companyId: 'aCompanyId',
        lastEnclosedMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentState,
      selector: selectCompanyIdAndLastEnclosedMonth,
      expectedResult: {
        companyId: 'aCompanyId',
        lastEnclosedMonth: new Date('2024-01-01'),
      },
      selectTitle: 'selectCompanyIdAndLastEnclosedMonth',
      givenTitle:
        'given a state with companyId set to "aCompanyId" and lastEnclosedMonth set to new Date("2024-01-01")',
      shouldTitle: 'should return the companyId and the lastEnclosedMonth',
    },
  ].forEach(
    ({
      currentState,
      selector,
      expectedResult,
      selectTitle,
      givenTitle,
      shouldTitle,
    }) => {
      describe(`${selectTitle} selector`, () => {
        describe(`${givenTitle}`, () => {
          it(`${shouldTitle}`, () => {
            const result = selector.projector(currentState);

            expect(result).toStrictEqual(expectedResult);
          });
        });
      });
    }
  );
});
