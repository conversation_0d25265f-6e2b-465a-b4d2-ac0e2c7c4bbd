import { Directive, HostListener, OnInit, inject } from '@angular/core';
import {
  LOCKABLE_COMPONENT_TOKEN,
  LockableComponent,
} from '@gc/core/navigation/models';

@Directive({
  selector: '[gcBlockReload]',
  exportAs: 'gcBlockReload',
  standalone: true,
})
export class BlockReloadDirective implements OnInit {
  private readonly _lockableComponent = inject<LockableComponent>(
    LOCKABLE_COMPONENT_TOKEN
  );

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    if (!this._lockableComponent.canDeactivate()) event.preventDefault();
  }

  ngOnInit(): void {
    if (!this._lockableComponent)
      throw new Error('You need to provide a LockableComponent');
  }
}
