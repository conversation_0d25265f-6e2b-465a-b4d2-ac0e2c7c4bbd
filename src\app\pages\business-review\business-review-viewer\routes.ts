import { Routes } from '@angular/router';
import { Title<PERSON><PERSON>Header, TitleKeyTab } from '@gc/core/navigation/models';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { BusinessReviewViewerComponent } from './business-review-viewer.component';

export const ROUTES: Routes = [
  {
    path: '',
    component: BusinessReviewViewerComponent,
    title: TitleKeyTab.BUSINESS_REVIEW_VIEWER,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.BUSINESS_REVIEW_VIEWER)],
  },
];
