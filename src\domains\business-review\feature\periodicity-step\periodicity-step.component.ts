import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { CalendarYear, PeriodicityKind } from '@gc/business-review/models';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { LoaderComponent } from '@gc/shared/ui';
import { TranslocoModule } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { LetDirective, PushPipe } from '@ngrx/component';
import { Observable } from 'rxjs';
import { BusinessReviewFormService } from '../services/business-review-form.service';

@Component({
  selector: 'gc-periodicity-step',
  standalone: true,
  imports: [
    TranslocoModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatFormFieldModule,
    MatSelectModule,
    LetDirective,
    TranslocoDatePipe,
    LoaderComponent,
    PushPipe,
  ],
  templateUrl: './periodicity-step.component.html',
  styleUrl: './periodicity-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeriodicityStepComponent {
  private readonly _service = inject(BusinessReviewFormService);

  PeriodicityKind = PeriodicityKind;

  periodicityKindFC = this._service.periodicityKindFC;

  calendarYearsOptions: CalendarYear[] = this._service.calendarYearsOptions;

  fiscalYearsOptions$: Observable<FiscalYearDateRange[]> =
    this._service.fiscalYearsOptions$;

  periodicityFG = this._service.periodicityFG;

  calendarYearFC = this._service.calendarYearFC;

  fiscalYearFC = this._service.fiscalYearFC;

  selectedDateRange$: Observable<{
    startDate: Date;
    endDate: Date;
  }> = this._service.selectedDateRange$;

  isLoading$ = this._service.isLoadingFiscalYears$$;

  get periodicityKind(): string {
    return this._service.periodicityKind;
  }
}
