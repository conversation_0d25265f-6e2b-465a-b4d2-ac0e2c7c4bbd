<div class="main-container" *transloco="let t; read: 'accounting'">
  <ng-container
    *ngrxLet="{
      availableEditions: availableEditions$,
      selectedEditions: selectedEditions$,
      isOldestEnclosedMonthSelected: isOldestEnclosedMonthSelected$,
      loadingStatus: availableMonthlyEditionsLoadingStatus$,
    } as vm">
    @if (vm.loadingStatus === 'IN_PROGRESS') {
      <gc-loader
        [label]="
          t(
            'monthly-edition-history-tab.available-editions-loading-in-progress'
          )
        " />
    } @else {
      @if (
        vm &&
        vm.isOldestEnclosedMonthSelected &&
        !(vm.availableEditions | hasSomeAvailableEditions)
      ) {
        <p class="oldest-enclosed-month-selected-message-container">
          {{ t('monthly-edition-tab.label-select-oldest-month') }}
        </p>
      } @else {
        @if (vm && vm.availableEditions && vm.selectedEditions) {
          <div class="editions-selection-container">
            @for (
              availableEdition of vm.availableEditions;
              track availableEdition
            ) {
              <mat-checkbox
                [color]="'primary'"
                [disabled]="!availableEdition.isAvailable"
                [checked]="vm.selectedEditions.includes(availableEdition.id)"
                (change)="toggleEdition(availableEdition.id)">
                {{ t(availableEditionsKeys[availableEdition.id]) }}
                @if (!availableEdition.isAvailable) {
                  ({{ t('monthly-edition-tab.unavailable') }})
                }
              </mat-checkbox>
            }
          </div>
        }
      }
    }
  </ng-container>
</div>
