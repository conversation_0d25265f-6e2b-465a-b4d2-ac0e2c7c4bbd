@if (loadingFiltersStatus$ | ngrxPush; as loadingFiltersStatus) {
  <gc-monthly-treatment-card
    *transloco="let t"
    [disabled]="
      invalid ||
      !productCharacteristicFG?.valid ||
      !!isLoadingData ||
      loadingFiltersStatus === 'IN_PROGRESS'
    "
    (handleVisualization)="
      consult.emit(productCharacteristicFG?.controls?.characteristics?.value)
    ">
    <ng-container header>
      <span>
        {{ t('accounting.monthly-treatment-categories.labels.sales') }}
      </span>
    </ng-container>
    <div class="container">
      <div class="filter-icon">
        <mat-icon color="primary">filter_list</mat-icon>
        <div>
          {{ t('accounting.monthly-edition-tab.cards.filters-labels.sales') }}
        </div>
      </div>
      @if (loadingFiltersStatus === 'IN_PROGRESS') {
        <gc-loader
          [label]="
            t('accounting.monthly-edition-tab.cards.filters-loader-label')
          " />
      } @else {
        @if (productCharacteristicFG) {
          <div class="filters" [formGroup]="productCharacteristicFG">
            <div formArrayName="characteristics">
              @for (
                characFC of productCharacteristicFG.controls.characteristics
                  .controls;
                track characFC
              ) {
                <gc-characteristic-select
                  [isSubscriptSizingFixed]="productCharacteristicFG.invalid"
                  [required]="characFC.hasValidator(requiredValidator)"
                  [label]="
                    t(
                      'accounting.monthly-edition-tab.form.label.select-characteristic'
                    )
                  "
                  [formControl]="characFC"
                  [filters]="selectFilters" />
              }
            </div>
          </div>
        }
      }
    </div>
  </gc-monthly-treatment-card>
}
