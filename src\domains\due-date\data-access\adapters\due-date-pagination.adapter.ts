import { DueDateApi, DueDateResponseApi } from '@gc/shared/api/data-access';
import { DueDatePagination } from '@gc/due-date/models';
import { DueDateAdapter } from './due-date.adapter';

export class DueDatePaginationAdapter {
  public static fromApi(
    dueDateResponseApi: DueDateResponseApi
  ): DueDatePagination {
    const { firstElementNumber, elementsPerPage, totalElements, dueDates } =
      dueDateResponseApi;
    return {
      firstElementNumber,
      elementsPerPage,
      totalElements,
      dueDates: dueDates
        ? dueDates.map((dueDateApi: DueDateApi) =>
            DueDateAdapter.fromApi(dueDateApi)
          )
        : [],
    };
  }
}
