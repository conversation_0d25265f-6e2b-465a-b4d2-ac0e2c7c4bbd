import { inject, Injectable } from '@angular/core';
import { GetStatisticalProceduresPort } from '@gc/core/deb/domains/ports';
import { map } from 'rxjs';
import { DebApiService } from '../api';

@Injectable()
export class StatisticalProceduresAdapter
  implements GetStatisticalProceduresPort
{
  private readonly api = inject(DebApiService);

  for(companyId: string) {
    return this.api
      .getStatisticalProcedures(companyId)
      .pipe(
        map((response) =>
          response.map((statisticalProcedure) => statisticalProcedure.code)
        )
      );
  }
}
