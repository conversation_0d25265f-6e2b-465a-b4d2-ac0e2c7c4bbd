<ng-container *transloco="let t">
  <gc-navigation-back-container [title]="t('header.titles.dunning')">
    <ng-container *transloco="let t; read: 'dunning'">
      <gc-master-detail-panels-layout
        *ngrxLet="currentDunningLetter$ as currentDunningLetter"
        [masterPanelTitle]="t('dunning-parameters-page.titles.master-panel')"
        [detailPanelTitle]="
          currentDunningLetter
            ? t('dunning-parameters-page.titles.detail-panel-with-level', {
                level: currentDunningLetter.dunningLevel | dunningLevelFromEnum,
              })
            : t('dunning-parameters-page.titles.detail-panel')
        ">
        <div masterPanel class="panel">
          <gc-dunning-letter-edit-filter />
        </div>
        <div detailPanel class="panel">
          <gc-dunning-letter-edit />
        </div>
      </gc-master-detail-panels-layout>
    </ng-container>
  </gc-navigation-back-container>
</ng-container>
