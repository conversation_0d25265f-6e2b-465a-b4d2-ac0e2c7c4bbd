import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';
import { LoginComponent } from './login.component';

export const ROUTES: Routes = [
  {
    path: '',
    component: LoginComponent,
    title: TitleKeyTab.LOGIN,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.LOGIN)],
  },
];
