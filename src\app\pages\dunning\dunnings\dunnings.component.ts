import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { Router, RouterLink } from '@angular/router';
import {
  dunningActions,
  dunningListActions,
  DunningListStoreModule,
  DunningStoreModule,
  selectSaveResult,
  selectTotalSelectedDunnings,
} from '@gc/dunning/data-access';
import { DunningSaveDueDateFail, DunningsSaveResult } from '@gc/dunning/models';

import { DialogService } from '@gc/shared/ui';
import {
  TranslocoService,
  TranslocoModule,
  TRANSLOCO_SCOPE,
} from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import {
  defer,
  filter,
  finalize,
  iif,
  Observable,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { map } from 'rxjs/operators';
import {
  DunningFiltersComponent,
  DunningListComponent,
} from '@gc/dunning/feature';
import { DunningMoreActionsButtonComponent } from '@gc/dunning/ui';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { URL_PATHS } from '@gc/core/navigation/models';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

type saveDunningConfirmation = boolean;
type saveDunningInformation = boolean;

@Component({
  selector: 'gc-dunnings',
  templateUrl: './dunnings.component.html',
  styleUrls: ['./dunnings.component.scss'],
  standalone: true,
  imports: [
    TranslocoModule,
    MainContainerComponent,
    DunningFiltersComponent,
    MatButtonModule,
    MatIconModule,
    DunningMoreActionsButtonComponent,
    MatSidenavModule,
    DunningListComponent,
    DunningListStoreModule,
    DunningStoreModule,
    RouterLink,
  ],
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'dunning', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
  ],
})
export class DunningsComponent implements OnInit {
  private readonly _dialogService = inject(DialogService);
  private readonly _router = inject(Router);
  private readonly _translocoService = inject(TranslocoService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  urlsPaths = URL_PATHS;
  totalSelectedDunnings$!: Observable<number>;
  isDialogOpened = false;

  dueDateFormControl = new FormControl<Date | undefined>(undefined, [
    Validators.required,
  ]);

  ngOnInit(): void {
    this.totalSelectedDunnings$ = this._store.select(
      selectTotalSelectedDunnings
    );

    // TODO : https://pirats.atlassian.net/browse/PR-1067
    // eslint-disable-next-line @ngrx/avoid-dispatching-multiple-actions-sequentially
    this._store.dispatch(dunningListActions.initDunningPagination());
    // eslint-disable-next-line @ngrx/avoid-dispatching-multiple-actions-sequentially
    this._store.dispatch(dunningActions.fetchDunningLevelConfig());
    this.listenDunningSaveAndOpenLetter();
  }

  onFiltersApplied(drawer: MatDrawer): void {
    if (drawer.opened) {
      drawer.close();
    }
  }

  navigateTo(path: string): void {
    this._router.navigate([path]);
  }

  openSaveDunningDialog(): void {
    const afterCloseDialogUnsubscribe = 1;

    this.totalSelectedDunnings$
      .pipe(
        filter(() => !this.isDialogOpened),
        tap(() => (this.isDialogOpened = true)),

        switchMap((totalSelectedDunnings: number) =>
          iif(
            () => totalSelectedDunnings > 0,
            this.openConfirmSaveDunningDialog$(totalSelectedDunnings),
            this.openInfoSaveDunningDialog$()
          )
        ),
        tap((saveConfirmed: boolean) => {
          if (saveConfirmed) {
            this._store.dispatch(dunningListActions.saveSelectedDunnings());
          }
        }),
        take(afterCloseDialogUnsubscribe),
        finalize(() => (this.isDialogOpened = false))
      )
      .subscribe();
  }

  openConfirmSaveDunningDialog$(
    totalSelectedDunnings: number
  ): Observable<saveDunningConfirmation> {
    return this._translocoService
      .selectTranslate(
        'dunning-page.save.confirm.message',
        { nbClients: totalSelectedDunnings },
        'dunning'
      )
      .pipe(
        switchMap((messageConfirm: string) =>
          this._dialogService.confirm(messageConfirm)
        )
      );
  }

  openInfoSaveDunningDialog$(): Observable<saveDunningInformation> {
    return this._translocoService
      .selectTranslate('dunning-page.save.info.message', {}, 'dunning')
      .pipe(
        switchMap((messageInfo: string) =>
          this._dialogService.info(messageInfo)
        ),
        map(() => false)
      );
  }

  listenDunningSaveAndOpenLetter(): void {
    this._store
      .select(selectSaveResult)
      .pipe(
        switchMap((saveResult: DunningsSaveResult | undefined) =>
          iif(
            () => Boolean(saveResult?.dueDateFails?.length),
            defer(() => this.openDueDateFailDialog$(saveResult)),
            of(saveResult)
          )
        ),
        filter((saveResult: DunningsSaveResult | undefined) =>
          Boolean(saveResult?.dueDateSuccess?.length)
        ),
        switchMap(() => this._router.navigate([URL_PATHS.dunningsViewer])),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  openDueDateFailDialog$(
    saveResult: DunningsSaveResult | undefined
  ): Observable<DunningsSaveResult | undefined> {
    const messagesError: Array<string> = [];

    if (!saveResult?.dueDateFails?.length) {
      throw new Error('dueDateFails from saveResult is undefined');
    }

    messagesError.push(
      ...(saveResult?.dueDateFails
        .filter(
          (dueDateFail: DunningSaveDueDateFail) =>
            dueDateFail.rejectionReason !== undefined
        )
        .map(
          (dueDateFail: DunningSaveDueDateFail) => dueDateFail.rejectionReason
        ) as Array<string>)
    );

    return this._dialogService.error(messagesError).pipe(map(() => saveResult));
  }
}
