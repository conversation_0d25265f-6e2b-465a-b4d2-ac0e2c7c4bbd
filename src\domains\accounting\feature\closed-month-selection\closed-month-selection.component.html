<ng-container *transloco="let t">
  <div class="header">
    @if (selectCompanyFC) {
      <div class="select-company-container">
        <gc-company-single-select
          [formControl]="selectCompanyFC"
          [label]="t('accounting.companies-select.label')"
          [invalid]="
            selectCompanyFC.invalid &&
            (selectCompanyFC.dirty || selectCompanyFC.touched)
          "
          [errorText]="t('sharedForm.error.required')" />
      </div>
    }

    <div
      class="select-year-container"
      *ngrxLet="{
        enclosedYears: enclosedYears$,
        selectedEnclosedYear: selectedEnclosedYear$,
      } as vm">
      @if (vm && vm.enclosedYears?.length) {
        <mat-form-field appearance="outline" subscriptSizing="dynamic">
          <mat-select
            [value]="vm.selectedEnclosedYear"
            (selectionChange)="selectedYearChange($event.value)">
            @for (
              enclosedYear of vm.enclosedYears | sortNumbersDsc;
              track enclosedYear
            ) {
              <mat-option [value]="enclosedYear">{{ enclosedYear }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
      }
    </div>
  </div>
  <div class="content">
    <div class="title">
      {{
        t('accounting.monthly-edition-history-tab.enclosing-month-list-title')
      }}
    </div>
    <div
      class="select-month-container"
      *ngrxLet="{
        enclosedMonths: enclosedMonths$,
        selectedEnclosedMonth: selectedEnclosedMonth$,
      } as vm">
      @if (vm && !vm.enclosedMonths?.length) {
        {{ t('accounting.monthly-edition-history-tab.no-enclosing-month') }}
      }
      @if (vm && vm.enclosedMonths?.length && vm.selectedEnclosedMonth) {
        <mat-radio-group
          [value]="vm.selectedEnclosedMonth"
          (change)="selectedMonthChange($event.value)"
          [color]="'primary'">
          @for (
            enclosedMonth of vm.enclosedMonths | sortDatesDsc;
            track enclosedMonth
          ) {
            <mat-radio-button [value]="enclosedMonth">{{
              enclosedMonth
                | translocoDate: { year: 'numeric', month: 'long' }
                | capitalize
            }}</mat-radio-button>
          }
        </mat-radio-group>
      }
    </div>
  </div>
</ng-container>
