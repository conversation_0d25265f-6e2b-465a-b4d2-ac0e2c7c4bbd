import { Dunning<PERSON><PERSON>, DunningDueDateApi } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';
import { DunningInvoice } from '@gc/dunning/models';
import { DueDateHelper } from '../utils/due-date.helper';

export class DunningInvoiceAdapter {
  public static fromDunningsApi(dunningsApi: DunningApi[]): DunningInvoice[] {
    return dunningsApi.reduce(
      (invoices: DunningInvoice[], dunningApi: DunningApi) => {
        invoices.push(
          ...DunningInvoiceAdapter.fromDueDatesApi(dunningApi.dueDates)
        );
        return invoices;
      },
      []
    );
  }

  public static fromDueDatesApi(
    dunningDueDatesApi: DunningDueDateApi[]
  ): DunningInvoice[] {
    return dunningDueDatesApi.reduce(
      (invoices: DunningInvoice[], dunningDueDateApi: DunningDueDateApi) => {
        const invoiceAlreadyExistInAccumulator = invoices.find(
          (invoice: DunningInvoice) =>
            invoice.id === dunningDueDateApi.invoiceId
        );
        if (!invoiceAlreadyExistInAccumulator) {
          const date = DateAdapter.dateFromStringAPI(
            dunningDueDateApi.invoiceDate
          );
          if (!date) {
            throw new Error('La date de la facture est manquante !');
          }

          const invoiceDueDatesApi: DunningDueDateApi[] =
            dunningDueDatesApi.filter(
              (dueDateApi: DunningDueDateApi) =>
                dunningDueDateApi.invoiceId === dueDateApi.invoiceId
            );
          const invoiceDueDatesIds = invoiceDueDatesApi.map(
            (dueDateApi: DunningDueDateApi) => dueDateApi.id
          );

          const invoiceLevel = DueDateHelper.getMaxLevel(invoiceDueDatesApi);

          invoices.push({
            id: dunningDueDateApi.invoiceId,
            number: dunningDueDateApi.invoiceNumber,
            amount: dunningDueDateApi.invoiceAmount,
            date,
            dueDatesIds: invoiceDueDatesIds,
            totalAmountDue: invoiceDueDatesApi.reduce(
              (totalAmountDue: number, dueDate: DunningDueDateApi) => {
                return totalAmountDue + dueDate.balance;
              },
              0
            ),
            level: invoiceLevel,
            selectedLevel: undefined,
          });
        }

        return invoices;
      },
      []
    );
  }
}
