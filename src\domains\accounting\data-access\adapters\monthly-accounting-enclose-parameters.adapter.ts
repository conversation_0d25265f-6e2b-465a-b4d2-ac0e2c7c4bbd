import { MonthlyAccountingEncloseParameters } from '@gc/accounting/models';
import { MonthlyAccountingReportsClosureParameterApi } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';

export class MonthlyAccountingEncloseParametersAdapter {
  public static toApi(
    parameters: MonthlyAccountingEncloseParameters
  ): MonthlyAccountingReportsClosureParameterApi {
    return {
      companyId: parameters.companyId,
      month: DateAdapter.dateToStringAPIWithoutDay(parameters.month) as string,
    };
  }
}
