import { Component, inject } from '@angular/core';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { DeviceDetectorService } from 'ngx-device-detector';

@Component({
  selector: 'gc-unavailable-device',
  standalone: true,
  templateUrl: './unavailable-device.component.html',
  styleUrl: './unavailable-device.component.scss',
  imports: [MainContainerComponent, TranslocoModule],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'navigation/unavailable-device',
      multi: true,
    },
  ],
})
export class UnavailableDeviceComponent {
  readonly deviceService = inject(DeviceDetectorService);
}
