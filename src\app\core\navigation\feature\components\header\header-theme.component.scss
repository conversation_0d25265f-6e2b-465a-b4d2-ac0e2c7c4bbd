@use 'sass:map';
@use '@angular/material' as mat;
@use 'gc-material-theme' as gc-material-theme;

$color-config: mat.m2-get-color-config(gc-material-theme.$theme);
$primary-palette: map.get($color-config, 'primary');

mat-toolbar {
    background-color: mat.m2-get-color-from-palette(
        $primary-palette,
        'default'
    );

    .container {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left-container,
        .right-container {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .left-container {
            gap: 5px;
        }

        .right-container {
            justify-content: flex-end;
        }
    }
}
