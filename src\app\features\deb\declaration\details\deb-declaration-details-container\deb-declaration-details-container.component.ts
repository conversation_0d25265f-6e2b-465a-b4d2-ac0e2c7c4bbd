import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Signal,
} from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { provideDebServices } from '@gc/core/deb';
import { DebFacade } from '@gc/core/deb/application/facades';
import { DebDetails, DraftDeclaration } from '@gc/core/deb/domains/models';
import { provideDebInfrastructure } from '@gc/core/deb/infrastructure';
import { ResourceState } from '@gc/core/shared/store';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebDeclarationDetailsComponent } from '../deb-declaration-details/deb-declaration-details.component';

@Component({
  selector: 'gc-deb-declaration-details-container',
  standalone: true,
  imports: [
    MatIconModule,
    TranslocoDirective,
    MatDialogModule,
    MatButton,
    DebDeclarationDetailsComponent,
  ],
  providers: [provideDebServices(), provideDebInfrastructure()],
  templateUrl: './deb-declaration-details-container.component.html',
  styleUrl: './deb-declaration-details-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationDetailsContainerComponent {
  private readonly debFacade = inject(DebFacade);
  readonly debSummaryInfo = inject<DraftDeclaration>(MAT_DIALOG_DATA);

  details: Signal<ResourceState<DebDetails[]>>;

  constructor() {
    this.details = this.debFacade.getDetails();
  }
}
