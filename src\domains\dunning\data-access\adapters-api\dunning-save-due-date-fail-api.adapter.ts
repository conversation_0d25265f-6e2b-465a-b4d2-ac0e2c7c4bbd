import { DueDateSaveFailApi } from '@gc/shared/api/data-access';
import { DunningSaveDueDateFail } from '@gc/dunning/models';

export class DunningSaveDueDateFailApiAdapter {
  static fromApi(
    dueDateSaveSuccessApi: DueDateSaveFailApi[]
  ): DunningSaveDueDateFail[] {
    const result: DunningSaveDueDateFail[] = [];
    dueDateSaveSuccessApi.forEach((s) => {
      if (!s.dueDateId) {
        throw new Error(
          '[DunningSaveDueDateFailApiAdapter] dueDateId should not be null'
        );
      }
      if (!s.rejectionReason) {
        throw new Error(
          '[DunningSaveDueDateFailApiAdapter] rejectionReason should not be null'
        );
      }
      result.push({
        dueDateId: s.dueDateId,
        rejectionReason: s.rejectionReason,
      });
    });
    return result;
  }
}
