<a name="readme-top"></a>

-   [Qualité du code](#qualité-du-code)
    -   [Linters](#linters)
        -   [Qu'est ce qu'un linter ?](#quest-ce-quun-linter-)
        -   [Quels linters sont utilisés ?](#quels-linters-sont-utilisés-)
            -   [Eslint](#eslint)
            -   [Stylelint](#stylelint)
        -   [Comment paramétrer les règles des linters ?](#comment-paramétrer-les-règles-des-linters-)
            -   [EsLint](#eslint-1)
            -   [StyleLint](#stylelint-1)
        -   [Comment voir les erreurs des Linters ?](#comment-voir-les-erreurs-des-linters-)
            -   [EsLint](#eslint-2)
                -   [Executer EsLint dans un terminal](#executer-eslint-dans-un-terminal)
            -   [StyleLint](#stylelint-2)
                -   [Executer StyleLint dans un terminal](#executer-stylelint-dans-un-terminal)
        -   [Que faire en cas d'erreur remontée dans la console par un linter ?](#que-faire-en-cas-derreur-remontée-dans-la-console-par-un-linter-)
            -   [Désactivation d'une règle](#désactivation-dune-règle)
    -   [Formattage du code automatique](#formattage-du-code-automatique)
        -   [Pourquoi formatter le code ?](#pourquoi-formatter-le-code-)
        -   [Formattage du code html et ts avec Prettier](#formattage-du-code-html-et-ts-avec-prettier)
        -   [Formattage du code scss avec Stylelint](#formattage-du-code-scss-avec-stylelint)
            -   [Note importante : éviter les conflits avec Eslint](#note-importante--éviter-les-conflits-avec-eslint)
            -   [Comment executer le formattage du code ?](#comment-executer-le-formattage-du-code-)
            -   [Executer le formattage de code dans un terminal](#executer-le-formattage-de-code-dans-un-terminal)
    -   [Tests](#tests)
        -   [Tests unitaires](#tests-unitaires)
            -   [Comment lancer les tests](#comment-lancer-les-tests)
    -   [Normes des nommages](#normes-des-nommages)
-   [Gestion des imports : Sheriff](#gestion-des-imports--sheriff)
    -   [Règles d'imports](#règles-dimports)
    -   [Vérification des imports](#vérification-des-imports)
    -   [Limitations de Sheriff](#limitations-de-sheriff)

# Qualité du code

## Linters

### Qu'est ce qu'un linter ?

Le linter est un outil d'analyse de code qui permet de forcer le développeur à écrire du code de qualité.
Il permet de suivre les bonnes pratiques de codage du language (dans notre cas Javascript, Typescript, Html et Css) et des conventions d'équipe (nommage des fichiers...).

Ces bonnes pratiques sont définies sous forme de règles de codage à respecter

> (exemples : pas de typage any, pas de doublons dans les imports ...).

### Quels linters sont utilisés ?

Les linters utilisés sont :

-   EsLint
-   Stylelint

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Eslint

EsLint est utilisé pour linter les fichiers Javascript, TypeScript et Html incluant les bonnes pratiques liées à l'utilisation d'Angular, de RxJs et de Jest.

> :bulb: plus d'infos sur <https://eslint.org/docs/latest/use/getting-started>

Eslint est intégré par défaut par Angular lorsqu'on génère une nouvelle application. Le linter est configuré par Angular dans le fichier `angular.json`.

> :bulb: Il n'y a donc pas besoin de définir de parser ou de plugins dans `.eslintrc.json`.

> :bulb: Plus d'information sur la configuration de Eslint par Angular : <https://github.com/angular-eslint/angular-eslint#quick-start>.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Stylelint

Stylelint est utilisé pour linter les fichiers scss.

> :bulb: plus d'infos sur <https://stylelint.io/user-guide/get-started>

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Comment paramétrer les règles des linters ?

#### EsLint

Les règles sont définies dans le fichier `.eslintrc.json`.

Plusieurs plugins Eslint existent (sous la forme de dépendances npm) et proposent des règles de bonnes pratiques de codage :

-   eslint - permet de définir des règles de bonnes pratiques de codage propre à Javascript
-   @typescript-eslint/eslint-plugin - permet de définir des règles de bonnes pratiques de codage propre à Typescript
-   @angular-eslint/eslint-plugin - permet de définir des règles de bonnes pratiques de codage propre à Angular (dans les .ts)
-   @angular-eslint/eslint-plugin-template - permet de définir des règles de bonnes pratiques de codage propre à Angular (dans les .html)

Ces plugins fournissent des règles par défaut que l'on peut intégrer directement dans `.eslintrc.json > "extends": []` :

-   eslint:recommended
-   plugin:@typescript-eslint/recommended
-   plugin:@angular-eslint/recommended
-   plugin:@angular-eslint/template/recommended

Il est aussi possible d'ajouter des règles ou de surcharger les règles définies par défaut dans `.eslintrc.json > "rules": []`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### StyleLint

Les règles sont définies dans le fichier `.stylelintrc.json`.

Comme pour EsLint, des plugins permettent de configurer des règles par défaut créée par la communauté dans `.stylelintrc.json > "extends": []`.
Notre configuration utilise `stylelint-config-standard-scss` qui étend `stylelint-config-standard` et `stylelint-config-recommended-scss`.

> :bulb: plus d'infos sur <https://www.npmjs.com/package/stylelint-config-standard-scss>

Il est aussi possible d'ajouter des règles ou de surcharger les règles définies par défaut dans `.stylelintrc.json > "rules": []`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Comment voir les erreurs des Linters ?

#### EsLint

Il existe 2 possibilités pour voir les erreurs de EsLint :

1. Au niveau d'un fichier grâce à l'extension Eslint de VSCode
    > :bulb: cf section extensions de VSCode
2. En exécutant Eslint sur tout le repo depuis le terminal

<p align="right">(<a href="#readme-top">back to top</a>)</p>

##### Executer EsLint dans un terminal

La commande est définie dans le package.json et peut être lancée avec :

`npm run lint`

Cette commande va lancer Eslint qui va analyser le code de tout le repo (excepté les dossiers/fichiers indiqués dans `.eslintignore`) et faire ressortir des erreurs en fonction des règles que l'on a défini.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### StyleLint

Il existe 2 possibilités pour voir les erreurs de StyleLint :

1. Au niveau d'un fichier grâce à l'extension officielle Stylelint de VSCode (il peut être nécessaire de supprimer la partie `stylelint.config` du fichier `.vscode/settings.json` contenant les configurations de Visual Studio Code).

> :bulb: cf section extensions de VSCode

2. En exécutant StyleLint sur tout le repo depuis le terminal

<p align="right">(<a href="#readme-top">back to top</a>)</p>

##### Executer StyleLint dans un terminal

La commande est définie dans le package.json et peut être lancée avec :

`npm run lint`

(cette commande va elle même lancer la commande `npx stylelint "**/*.scss"`).

Cette commande va lancer Stylelint qui va analyser le code de tous les fichiers .scss dans tout le repo et faire ressortir des erreurs en fonction des règles définies.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Que faire en cas d'erreur remontée dans la console par un linter ?

Si une erreur est remontée dans la console elle doit être corrigée.

Si le développeur considère qu'il ne peut pas la corriger, il est possible de désactiver la règle associée, ou de redéfinir la règle associée.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Désactivation d'une règle

> :warning: Il faut éviter le plus possible la désactivation de règle. Etre vigilant lors des relectures de code et ne pas hésiter à challenger cette désactivation.

La philosophie pour désactiver une règle de lint (eslint ou stylelint) est la suivante (dans l'ordre de préférence suivant) :

1. Identifier le type de fichier concerné (exemple: .spec.ts, .component.ts...). Désactiver la règle seulement pour ce type de fichiers.
   :bulb: La desactivation ou la modification des règles StyleLint se parametre dans `.stylelintrc.json > "rules": []`
   :bulb: La desactivation ou la modification des règles Eslint se parametre dans `.eslintrc.json > "rules": []`
2. Se la désactivation concerne tous les fichiers, désactiver la règle de manière globale.
3. **à eviter le plus possible** si cela concerne un cas très particulier, il est aussi possible de désactiver la règle au niveau de la ligne concernée dans le fichiers directement `/* eslint-disable -nom-de-la-règle- */`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Formattage du code automatique

### Pourquoi formatter le code ?

L'utilisation d'outils pour formatter le code automatiquement à plusieurs avantages :

-   Il permet d'avoir un code plus ahéré et donc plus lisible
-   Il permet de définir des règles de formattage communes entre tous les développeurs. Les relecture de MR/PR ne sont plus "polluées" par du formattage.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Formattage du code html et ts avec Prettier

Nous utilisons Prettier pour formatter automatiquement nos fichiers html et ts.

Prettier est un package npm importé dans le package.json du projet.

Il est possible d'overrider les règles de formattage par défaut de prettier dans le fichier `.prettierrc.json`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

### Formattage du code scss avec Stylelint

Nous utilisons le paramètre `--fix` pour formatter automatiquement nos fichiers scss.

:bulb: à noter que certaines règles scss ne peuvent pas être corrigées automatiquement et devront être corrigées "à la main" par le developpeur.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Note importante : éviter les conflits avec Eslint

Eslint possède lui aussi des règles de formattage de code.

Cependant, Prettier étant plus performant que Eslint pour le formattage, il est préférable de désactiver ces règles dans le fichier `.eslintrc.json` pour laisser la gestion du formattage à Prettier.

> :bulb: Cette désactivation se fait via le plugin eslint `eslint-config-prettier` (qui est un package npm présent dans le package.json). Il faut ensuite ajouter "prettier" dans `.eslintrc.json > "extends": []`
> :bulb: Pour plus d'information, aller sur <https://prettier.io/docs/en/integrating-with-linters.html>

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Comment executer le formattage du code ?

Il existe 2 possibilités formatter le code :

1. Au niveau d'un fichier, lors de la sauvegarde de celui-ci, grâce à l'extension Prettier de VSCode (formatOnSave) et l'extenstion Stylelint pour les fichiers de style.
    > :bulb: cf section extensions de VSCode
2. En exécutant Prettier sur tout le repo depuis le terminal

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Executer le formattage de code dans un terminal

La commande est définie dans le package.json et peut être lancée avec :

`npm run format`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Tests

### Tests unitaires

Les tests unitaires du projet sont exécutés avec jest.

Les avantages de jest :

-   d'importantes capacités de mock
-   une bibliothèque d'assertions intégrée
-   un outil de code coverage intégré
-   JSDOM, un librairie émulant le navigateur
-   un mode watch, pour ne lancer que les tests des fichiers modifiés

<p align="right">(<a href="#readme-top">back to top</a>)</p>

#### Comment lancer les tests

Plusieurs commandes existe pour lancer les tests unitaires et sont définies dans le package.json.

-   lancement des tests : `npm run test`
-   lancement des tests en mode watch : `npm run test:watch`
-   lancement des tests avec un rapport de coverage: `npm run test:cov`

<p align="right">(<a href="#readme-top">back to top</a>)</p>

## Normes des nommages

**Propriétés et méthodes privées** : le nom doit commencer par "\_".

**Enums** : le nom des enums en camelCase mais les valeurs en ALL_CAPS.

**Observables** : le nom doit terminer par $.

**BehaviorSubject et Subjec**t : le nom doit terminer par $$.

**Les constantes** : Attention, il y a une nuance. Les constantes au sein de méthodes et les constantes qui contiennent des méthodes resteront en camelCase MAIS les constantes globales à un fichier ou importées doivent être en ALL_CAPS. La distinction se fait sur la portée de la constante. Local => camelCase / Global => ALL_CAPS.

**Les emitters** : le nom des méthodes appelées suite à un emit doit porter le nom de l'emitter avec comme préfixe "on". Exemple : (openReport)="onOpenReport($event)".

**Les classes css** : elles doivent être en kebab-case

<p align="right">(<a href="#readme-top">back to top</a>)</p>

# Gestion des imports : Sheriff

Afin d'avoir du code maintenable, et donc de respecter les principes et bonnes pratiques de codages (SOLID, LIFT ..), nous suivons les guidelines introduites par NX. Notamment le découpage de notre code en 4 catégories différentes : data-access, feature, ui et util.

Pour rester simple, nous n'utilisons pas de librairie au sens npm ou angular mais tout simplement des dossiers.

Afin de valider que la structure de dossier de la web app soit constamment cohérent, nous utilisons l'utilitaire Sheriff : <https://softarc-consulting.github.io/sheriff/>

:bulb: Pour plus d'informations sur la philosophie NX, voir la documentation de NX sur les dépendances entre catégorie : <https://nx.dev/concepts/decisions/project-dependency-rules>

## Règles d'imports

L'objectif de ce découpage en catégorie et de s'imposer des contraintes en termes de dépendance. Par exemple, un component situé dans un dossier ui ne doit pas importer de service d'accès à la donnée situé dans data-access.

💡Pour accéder à la liste complète des règles, se référer au fichier **sheriff.config.ts**

## Vérification des imports

Commande manuelle : **npm run sheriff:verify**

Afin de limiter les risques d'ajout de nouvelles erreur Sheriff au projet, les configurations suivantes ont été mises en place :

-   **Vérification avant commit** : ajout de la commande _npm run sheriff:verify_ au hook de commit husky 👉 si erreur Sheriff, alors le commit n'est pas effectué
-   **Vérification dans VSCode** : utilisation du plugin eslint (dbaeumer.vscode-eslint) avec une configuration supplémentaire dans .vscode/settings.json 👉 si erreur Sheriff, la ligne en erreur est soulignée en rouge.
    \
     ⚠️ Sur VSCode c'est le plugin Eslint qui permet de checker le respect des règles Sheriff, l'exécution de eslint se faisant seulement sur le fichier ouvert, on se permet d'utiliser le plugin eslint de Sheriff (_plugin:@softarc/sheriff/default_) dans ce cas. Pour plus d'infos, cf section [Limitations de Sheriff](#limitations-de-sheriff)

-   **Vérification dans la CI** : ajout de la commande _npm run sheriff:verify_ dans azure-pipelines-ci.yml 👉 si erreur Sheriff, le merge sur develop et bloqué

## Limitations de Sheriff

Sheriff peut être utilisé en **ligne de commande** _npm run sheriff:verify_ ou en **plugin eslint**.

La 2ème option pose cependant des problèmes de performance d'exécution du linter : on passe de 1 min à 8 min ☠️.

Le problème est connue par l'autheur de la lib : <https://github.com/softarc-consulting/sheriff/issues/99>
