import { NavigationStart, Router, RouterOutlet } from '@angular/router';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { Spectator } from '@ngneat/spectator';
import { createComponentFactory } from '@ngneat/spectator/jest';
import { MockComponents, MockDirective } from 'ng-mocks';
import { of } from 'rxjs';

import { ProductParameterComponent } from './product-parameter.component';

const routerWithCodeDesignationNavigation = {
  url: '/code-designation',
  events: of(
    new NavigationStart(
      0,
      'http://localhost:4200/product/paramater/code-designation'
    )
  ),
};

const routerWithCodeStructureNavigation = {
  url: '/code-structure',
  events: of(
    new NavigationStart(
      0,
      'http://localhost:4200/product/paramater/code-structure'
    )
  ),
};
describe('ProductParameterComponent with code structure route', () => {
  let spectator: Spectator<ProductParameterComponent>;
  let component: ProductParameterComponent;

  const createComponent = createComponentFactory({
    component: ProductParameterComponent,
    declarations: [
      MockComponents(MainContainerComponent),
      MockDirective(RouterOutlet),
    ],
    providers: [
      { provide: Router, useValue: routerWithCodeStructureNavigation },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call initializeTabsWithCurrentUrl', () => {
      const initializeTabsWithCurrentUrlSpy = jest.spyOn(
        component,
        'initializeTabsWithCurrentUrl'
      );
      component.ngOnInit();

      expect(initializeTabsWithCurrentUrlSpy).toHaveBeenCalled();
    });
  });

  describe('initializeTabsWithCurrentUrl', () => {
    describe('given a state where the active url contain code-structure', () => {
      it('should set activeLinkIndex to 1', () => {
        component.initializeTabsWithCurrentUrl();

        expect(component.activeLinkIndex).toEqual(0);
      });
    });
  });
});

describe('ProductParameterComponent with code designation route', () => {
  let spectator: Spectator<ProductParameterComponent>;
  let component: ProductParameterComponent;

  const createComponent = createComponentFactory({
    component: ProductParameterComponent,
    declarations: [
      MockComponents(MainContainerComponent),
      MockDirective(RouterOutlet),
    ],
    providers: [
      { provide: Router, useValue: routerWithCodeDesignationNavigation },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call initializeTabsWithCurrentUrl', () => {
      const initializeTabsWithCurrentUrlSpy = jest.spyOn(
        component,
        'initializeTabsWithCurrentUrl'
      );
      component.ngOnInit();

      expect(initializeTabsWithCurrentUrlSpy).toHaveBeenCalled();
    });
  });

  describe('given a state where the active url contain code-designation', () => {
    it('should set activeLinkIndex to 1', () => {
      component.initializeTabsWithCurrentUrl();

      expect(component.activeLinkIndex).toEqual(1);
    });
  });
});
