<div class="container" data-testid="monthly_visualization" *transloco="let t">
  <ng-container
    *ngrxLet="{
      isNewestEnclosedMonthSelected: isNewestEnclosedMonthSelected$,
      uncloseStatus: uncloseStatus$,
      selectedEnclosedMonth: selectedEnclosedMonth$,
      hasEnclosedMonths: hasEnclosedMonths$,
    } as vm">
    @if (vm.uncloseStatus !== 'IN_PROGRESS') {
      <gc-master-detail-panels-layout>
        <div detailPanelHeader class="detail-header">
          {{ t('accounting.monthly-edition-history-tab.details-title') }}

          @if (vm.selectedEnclosedMonth) {
            -
            {{
              vm.selectedEnclosedMonth
                | translocoDate: { year: 'numeric', month: 'long' }
                | capitalize
            }}
          }
        </div>

        <gc-closed-month-selection masterPanel />
        <div detailPanel>
          @if (vm.hasEnclosedMonths === true) {
            <gc-available-editions-selection />

            <mat-divider />

            <div class="actions-container">
              @if (vm.isNewestEnclosedMonthSelected) {
                <button
                  mat-raised-button
                  color="primary"
                  (click)="uncloseSelectedMonth()">
                  {{ t('accounting.monthly-edition-history-tab.unclose') }}
                </button>
              }

              <button
                mat-raised-button
                color="primary"
                [disabled]="hasNotEditionToConsult | ngrxPush"
                (click)="openEditions()">
                {{ t('accounting.monthly-edition-history-tab.visualize') }}
              </button>
            </div>
          } @else if (vm.hasEnclosedMonths === false) {
            {{
              t('accounting.monthly-edition-history-tab.no-available-editions')
            }}
          }
        </div>
      </gc-master-detail-panels-layout>
    } @else {
      <gc-loader
        [label]="
          t('accounting.monthly-edition-history-tab.unclose-in-progress')
        " />
    }
  </ng-container>
</div>
