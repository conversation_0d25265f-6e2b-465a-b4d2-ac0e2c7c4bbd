@mixin mobile {
  @media screen and (width <= 424px) {
    @content;
  }
}

@mixin tablet {
  /* stylelint-disable-next-line media-feature-range-notation */
  @media screen and (min-width: 425px) and (max-width: 1023px) {
    @content;
  }
}

@mixin laptop {
  /* stylelint-disable-next-line media-feature-range-notation */
  @media screen and (min-width: 1024px) and (max-width: 1439px) {
    @content;
  }
}

@mixin desktop {
  /* stylelint-disable-next-line media-feature-range-notation */
  @media screen and (min-width: 1440px) {
    @content;
  }
}

@mixin tablet-and-up {
  @media screen and (width >= 425px) {
    @content;
  }
}
