<ng-container>
  <div
    mat-dialog-title
    class="title-container"
    *transloco="
      let t;
      read: 'sales.commissions-tab.payout-table.commission-form-edit.dialog.header'
    ">
    <mat-icon [color]="'primary'"> mode_edit </mat-icon>
    <span>
      {{ t('title') }}
    </span>
  </div>
  <div class="description-container">
    <div
      class="description-data"
      *transloco="
        let t;
        read: 'sales.commissions-tab.payout-table.commission-form-edit.dialog.information'
      ">
      <p>
        {{ t('representative') }}
        <span class="value">{{ selectedRepresentative().data?.fullname }}</span>
      </p>
      <p>
        {{ t('document-id') }}
        <span class="value">{{ commission.documentId }}</span>
      </p>
      <p>
        {{ t('document-date') }}
        <span class="value">{{ commission.documentDate }}</span>
      </p>
      <p>
        {{ t('amount-already-paid') }}
        <span class="value">{{
          commission.amount - commission.amountToBePaid
        }}</span>
      </p>
    </div>
  </div>
  <mat-dialog-content
    class="form-container"
    *transloco="
      let t;
      read: 'sales.commissions-tab.payout-table.commission-form-edit.dialog.form'
    ">
    @if (commission) {
      <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
        <div class="form-inputs-container">
          <gc-currency-input
            [label]="t('commission-amount')"
            [currency]="(currency$ | ngrxPush) ?? {}"
            formControlName="commissionAmount"></gc-currency-input>
          <gc-currency-input
            [label]="t('amount-to-be-paid')"
            [currency]="(currency$ | ngrxPush) ?? {}"
            formControlName="amountToBePaid"></gc-currency-input>
        </div>
        @if (formGroup.invalid && (formGroup.dirty || formGroup.touched)) {
          <p class="error">
            <mat-error>{{ t('error-message') }}</mat-error>
          </p>
        }
      </form>
    } @else if (!commission) {
      <p class="user-feedback-container">{{ t('no-data') }}</p>
    } @else {
      <div class="user-feedback-container">
        <gc-loader [label]="t('is-loading')" />
      </div>
    }
  </mat-dialog-content>
  <div
    class="action-button-container"
    *transloco="
      let t;
      read: 'sales.commissions-tab.payout-table.commission-form-edit.dialog'
    ">
    <button
      mat-stroked-button
      color="primary"
      type="button"
      cdkFocusInitial
      (click)="onCancel()">
      {{ t('cancel') }}
    </button>
    <button
      mat-raised-button
      color="primary"
      type="submit"
      [disabled]="formGroup.invalid"
      (click)="onSubmit()">
      {{ t('validate') }}
    </button>
  </div>
</ng-container>
