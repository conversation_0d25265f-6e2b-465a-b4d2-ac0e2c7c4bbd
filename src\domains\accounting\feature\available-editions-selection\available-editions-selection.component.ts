import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  inject,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  monthlyTreatmentHistoryActions,
  MonthlyTreatmentHistoryStoreModule,
  MonthlyTreatmentStoreModule,
  selectAvailableMonthlyEditions,
  selectAvailableMonthlyEditionsLoadingStatus,
  selectIsOldestEnclosedMonthSelected,
  selectSelectedEnclosedMonth,
  selectSelectedMonthlyEditions,
} from '@gc/accounting/data-access';
import { MonthlyTreatmentEditionsEnum } from '@gc/accounting/models';
import { LoadingStatus } from '@gc/shared/models';
import {
  LoaderComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import { delayInProgress } from '@gc/shared/utils';
import { TranslocoDirective } from '@jsverse/transloco';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { filter, Observable, tap } from 'rxjs';
import { HasSomeAvailableEditionsPipe } from './has-some-available-editions.pipe';
import { MatButton } from '@angular/material/button';

const AVAILABLE_EDITIONS_KEYS = {
  [MonthlyTreatmentEditionsEnum.SALES]:
    'monthly-treatment-categories.labels.sales',
  [MonthlyTreatmentEditionsEnum.DETAILS]:
    'monthly-treatment-categories.labels.details',
  [MonthlyTreatmentEditionsEnum.RECEIPTS]:
    'monthly-treatment-categories.labels.receipts',
  [MonthlyTreatmentEditionsEnum.DEBT]:
    'monthly-treatment-categories.labels.debt',
  [MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY]:
    'monthly-treatment-categories.labels.account-entry',
};

@Component({
  selector: 'gc-available-editions-selection',
  standalone: true,
  imports: [
    LetDirective,
    MatCheckboxModule,
    TranslocoDirective,
    MonthlyTreatmentHistoryStoreModule,
    MonthlyTreatmentStoreModule,
    LoaderComponent,
    HasSomeAvailableEditionsPipe,
    MatButton,
  ],
  templateUrl: './available-editions-selection.component.html',
  styleUrls: [
    './available-editions-selection.component.scss',
    './available-editions-selection-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvailableEditionsSelectionComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  availableMonthlyEditionsLoadingStatus$!: Observable<LoadingStatus>;
  readonly MonthlyTreatmentEditionsEnum = MonthlyTreatmentEditionsEnum;

  availableEditions$ = this._store.select(selectAvailableMonthlyEditions);
  selectedEditions$ = this._store.select(selectSelectedMonthlyEditions);

  isOldestEnclosedMonthSelected$ = this._store.select(
    selectIsOldestEnclosedMonthSelected
  );

  readonly availableEditionsKeys = AVAILABLE_EDITIONS_KEYS;

  ngOnInit(): void {
    this.handleAvailableEditionsRetrieval();

    this.availableMonthlyEditionsLoadingStatus$ = this._store
      .select(selectAvailableMonthlyEditionsLoadingStatus)
      .pipe(delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING));
  }

  handleAvailableEditionsRetrieval(): void {
    this._store
      .select(selectSelectedEnclosedMonth)
      .pipe(
        filter(Boolean),
        tap(() =>
          this._store.dispatch(
            monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions()
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  toggleEdition(edition: MonthlyTreatmentEditionsEnum): void {
    this._store.dispatch(
      monthlyTreatmentHistoryActions.toggleMonthlyTreatmentEditionSelection({
        edition,
      })
    );
  }
}
