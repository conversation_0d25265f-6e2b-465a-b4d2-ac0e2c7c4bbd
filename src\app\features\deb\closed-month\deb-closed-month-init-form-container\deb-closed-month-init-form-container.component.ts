import { ChangeDetectionStrategy, Component } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebClosedMonthInitFormComponent } from '@gc/features/deb/closed-month/deb-closed-month-init-form';
import { ClosedMonth } from '@gc/core/deb/domains/models';

@Component({
  selector: 'gc-deb-closed-month-init-container',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    DebClosedMonthInitFormComponent,
    TranslocoDirective,
    DebClosedMonthInitFormComponent,
  ],
  templateUrl: 'deb-closed-month-init-form-container.component.html',
  styleUrls: ['./deb-closed-month-init-form-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebClosedMonthInitFormContainerComponent {
  constructor(
    public dialogRef: MatDialogRef<DebClosedMonthInitFormContainerComponent>
  ) {}

  onClosedMonth(closedMonth: ClosedMonth | null) {
    if (!closedMonth) {
      this.dialogRef.close();
    }

    this.dialogRef.close(closedMonth);
  }
}
