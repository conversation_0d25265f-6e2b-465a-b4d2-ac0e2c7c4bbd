import { DebDeclarationDocumentsOverviewComponent } from './deb-declaration-documents-overview.component';
import { DialogService } from '@gc/shared/ui';
import { Currency } from '@gc/shared/models';
import { DraftDeclaration, Filters } from '@gc/core/deb/domains/models';
import { of } from 'rxjs';
import { signal } from '@angular/core';
import { DebFacade } from '@gc/core/deb/application/facades';
import { ResourceState } from '@gc/core/shared/store';
import { GuidHelper } from '@isagri-ng/core';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MatTable } from '@angular/material/table';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockDirectives } from 'ng-mocks';

describe('DebDeclarationDocumentsOverviewComponent', () => {
  let spectator: Spectator<DebDeclarationDocumentsOverviewComponent>;
  let component: DebDeclarationDocumentsOverviewComponent;
  let dialogService: DialogService;
  let debFacade: DebFacade;
  let translocoService: TranslocoService;

  const mockCurrency: Currency = {
    code: 'EUR',
    symbol: '€',
    usualPrecision: 2,
  };

  const mockFilters: Filters = {
    declarationMonth: new Date('2023-01'),
    companyId: 'company-123',
    includeDeliveryNotes: true,
    includeInvoices: true,
  };

  const createComponent = createComponentFactory({
    component: DebDeclarationDocumentsOverviewComponent,
    declarations: [MockDirectives(TranslocoDirective)],
    providers: [
      {
        provide: 'TRANSLOCO_TRANSPILER',
        useValue: { transpile: (value: string) => value },
      },
    ],
    mocks: [DialogService, DebFacade, MatTable, TranslocoService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();

    component = spectator.component;
    dialogService = spectator.inject(DialogService);
    debFacade = spectator.inject(DebFacade);
    translocoService = spectator.inject(TranslocoService);

    // Set up required inputs
    Object.defineProperty(component, 'companyId', {
      value: () => 'company-123',
    });
    Object.defineProperty(component, 'filters', {
      value: () => mockFilters,
    });
    Object.defineProperty(component, 'currency', {
      value: () => mockCurrency,
    });
    Object.defineProperty(component, 'areFiltersApplied', {
      value: () => true,
    });
    Object.defineProperty(component, 'inputDraftDeclarationsState', {
      value: () =>
        ({
          isLoading: false,
          data: [],
          status: 'Success',
          errors: undefined,
        }) as ResourceState<DraftDeclaration[]>,
    });

    jest.spyOn(GuidHelper, 'newGuid').mockReturnValue('new-guid-123');

    (debFacade.getDetails as jest.Mock).mockReturnValue(
      signal({ loading: false, data: [] })
    );
    (debFacade.loadDetailsFor as jest.Mock).mockImplementation(() => {});

    component.table = spectator.inject(MatTable);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should properly handle input signals', () => {
    spectator.detectChanges();

    expect(component.companyId()).toBe('company-123');
    expect(component.filters()).toEqual({
      declarationMonth: new Date('2023-01'),
      companyId: 'company-123',
      includeDeliveryNotes: true,
      includeInvoices: true,
    } as Filters);
    expect(component.currency()).toEqual({
      code: 'EUR',
      symbol: '€',
      usualPrecision: 2,
    } as Currency);
    expect(component.inputDraftDeclarationsState()).toEqual({
      isLoading: false,
      data: [],
      status: 'Success',
      errors: undefined,
    } as ResourceState<DraftDeclaration[]>);

    expect(component.draftDeclarations()).toEqual([]);
  });

  describe('openUpdateDialogAndUpdateToList', () => {
    it('should update the draftDeclarations when dialog returns a declaration', () => {
      const existingDeclaration: DraftDeclaration = {
        id: 'existing-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };

      const updatedDeclaration: DraftDeclaration = {
        ...existingDeclaration,
        invoicedAmount: 200,
      };

      component.draftDeclarations.set([existingDeclaration]);

      (dialogService.open as jest.Mock).mockReturnValue(of(updatedDeclaration));

      const updateSpy = jest.spyOn(component.draftDeclarations, 'update');

      component.openUpdateDialogAndUpdateToList(existingDeclaration);

      expect(dialogService.open).toHaveBeenCalled();
      expect(updateSpy).toHaveBeenCalled();
      expect(component.table.renderRows).toHaveBeenCalled();
    });

    it('should not update the draftDeclarations when dialog returns undefined', () => {
      const existingDeclaration: DraftDeclaration = {
        id: 'existing-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };

      component.draftDeclarations.set([existingDeclaration]);

      (dialogService.open as jest.Mock).mockReturnValue(of(undefined));

      const updateSpy = jest.spyOn(component.draftDeclarations, 'update');

      component.openUpdateDialogAndUpdateToList(existingDeclaration);

      expect(dialogService.open).toHaveBeenCalled();
      expect(updateSpy).not.toHaveBeenCalled();
      expect(component.table.renderRows).not.toHaveBeenCalled();
    });
  });

  describe('openAddDialogAndAddToList', () => {
    it('should add a new declaration to draftDeclarations when dialog returns a declaration', () => {
      const initialDeclarations: DraftDeclaration[] = [];
      component.draftDeclarations.set(initialDeclarations);

      const newDeclaration: DraftDeclaration = {
        id: 'temp-id',
        euNomenclature: 'EU456',
        destinationCountryCode: 'DE',
        invoicedAmount: 300,
        statisticalProcedureCode: 'SPC2',
        vatNumber: 'VAT456',
        state: 'initial',
        status: 'enabled',
      };

      (dialogService.open as jest.Mock).mockReturnValue(of(newDeclaration));

      component.onAddDebLineButtonClick();

      expect(dialogService.open).toHaveBeenCalled();

      expect(component.table.renderRows).toHaveBeenCalled();

      const declarations = component.draftDeclarations();
      expect(declarations.length).toBe(1);
      expect(declarations[0].euNomenclature).toBe('EU456');
      expect(declarations[0].destinationCountryCode).toBe('DE');
      expect(declarations[0].invoicedAmount).toBe(300);
      expect(declarations[0].statisticalProcedureCode).toBe('SPC2');
      expect(declarations[0].vatNumber).toBe('VAT456');
      expect(declarations[0].state).toBe('added');
      expect(declarations[0].status).toBe('enabled');
      expect(declarations[0].id).toBe('new-guid-123');
    });

    it('should not add a new declaration when dialog returns undefined', () => {
      const initialDeclarations: DraftDeclaration[] = [];
      component.draftDeclarations.set(initialDeclarations);

      (dialogService.open as jest.Mock).mockReturnValue(of(undefined));

      component.onAddDebLineButtonClick();

      expect(dialogService.open).toHaveBeenCalled();

      expect(component.table.renderRows).not.toHaveBeenCalled();
      expect(component.draftDeclarations().length).toBe(0);
    });
  });

  describe('toggleDeclarationState', () => {
    it('should toggle the status from enabled to disabled', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };
      component.draftDeclarations.set([declaration]);

      component.toggleDeclarationState('test-id');

      const updatedDeclarations = component.draftDeclarations();
      expect(updatedDeclarations.length).toBe(1);
      expect(updatedDeclarations[0].status).toBe('disabled');
    });

    it('should toggle the status from disabled to enabled', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'disabled',
      };
      component.draftDeclarations.set([declaration]);

      component.toggleDeclarationState('test-id');

      const updatedDeclarations = component.draftDeclarations();
      expect(updatedDeclarations.length).toBe(1);
      expect(updatedDeclarations[0].status).toBe('enabled');
    });

    it('should not change anything if the declaration is not found', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };
      component.draftDeclarations.set([declaration]);

      component.toggleDeclarationState('non-existent-id');

      const updatedDeclarations = component.draftDeclarations();
      expect(updatedDeclarations.length).toBe(1);
      expect(updatedDeclarations[0].status).toBe('enabled');
    });
  });

  describe('openDetailsDialog', () => {
    it('should call openLineDetailsDialog for initial declarations', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };
      const spy = jest.spyOn(component, 'openLineDetailsDialog');

      component.openDetailsDialog(declaration);

      expect(spy).toHaveBeenCalledWith(declaration);
    });

    it('should call openAddedOrUpdatedLineDetailsDialog for added or updated declarations', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'added',
        status: 'enabled',
      };
      const spy = jest.spyOn(component, 'openAddedOrUpdatedLineDetailsDialog');

      component.openDetailsDialog(declaration);

      expect(spy).toHaveBeenCalledWith(declaration);
    });
  });

  describe('openAddedOrUpdatedLineDetailsDialog', () => {
    it('should open dialog with the declaration', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'added',
        status: 'enabled',
      };

      component.openAddedOrUpdatedLineDetailsDialog(declaration);

      expect(dialogService.open).toHaveBeenCalledWith(
        expect.anything(),
        declaration,
        expect.anything()
      );
    });
  });

  describe('openLineDetailsDialog', () => {
    it('should open dialog and load details for the declaration', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };

      component.openLineDetailsDialog(declaration);

      expect(dialogService.open).toHaveBeenCalled();
      expect(debFacade.loadDetailsFor).toHaveBeenCalledWith({
        companyId: 'company-123',
        deliveryNotesIncluded: true,
        destinationCountryCode: 'FR',
        invoicesIncluded: true,
        euNomenclature: 'EU123',
        month: mockFilters.declarationMonth,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
      });
    });

    it('should not load details if filters are null', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 100,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
        status: 'enabled',
      };

      Object.defineProperty(component, 'filters', {
        value: () => null,
      });

      component.openLineDetailsDialog(declaration);

      expect(dialogService.open).toHaveBeenCalled();
      expect(debFacade.loadDetailsFor).not.toHaveBeenCalled();
    });
  });

  describe('checkMissingFields', () => {
    it('should return empty array when all fields are present', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        state: 'initial',
        status: 'enabled',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 1000,
        statisticalProcedureCode: 'CODE1',
        vatNumber: 'VAT123',
      };

      const result = component['checkMissingFields'](declaration);

      expect(result).toEqual([]);
    });

    it('should return array with missing field names when fields are missing', () => {
      const declaration: DraftDeclaration = {
        id: 'test-id',
        state: 'initial',
        status: 'enabled',
        euNomenclature: '123',
        destinationCountryCode: null,
        invoicedAmount: 150,
        statisticalProcedureCode: '',
        vatNumber: 'VAT123',
      };

      (translocoService.translate as jest.Mock).mockImplementation((key) => {
        const translations = {
          'deb.deb-declaration-table-component.table.destination-country-code':
            'Destination Country',
          'deb.deb-declaration-table-component.table.statistical-procedure-code':
            'Statistical Procedure',
        };
        return translations[key as keyof typeof translations] || key;
      });

      const result = component['checkMissingFields'](declaration);

      expect(result).toEqual(['Destination Country', 'Statistical Procedure']);
    });
  });
});
