import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MatDialogRef } from '@angular/material/dialog';
import { DebClosedMonthInitFormContainerComponent } from './deb-closed-month-init-form-container.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockDirectives } from 'ng-mocks';
import { DebClosedMonthInitFormComponent } from '../deb-closed-month-init-form/deb-closed-month-init-form.component';
import { ClosedMonth } from '@gc/core/deb/domains/models';

describe('DebClosedMonthInitFormContainerComponent', () => {
  let spectator: Spectator<DebClosedMonthInitFormContainerComponent>;
  let component: DebClosedMonthInitFormContainerComponent;
  let dialogRef: MatDialogRef<DebClosedMonthInitFormContainerComponent>;

  const createComponent = createComponentFactory({
    component: DebClosedMonthInitFormContainerComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      DebClosedMonthInitFormComponent,
    ],
    mocks: [MatDialogRef, TranslocoService],
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
    dialogRef = spectator.inject(MatDialogRef);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('onClosedMonth', () => {
    it('should close dialog with null when closedMonth is null', () => {
      const closeSpy = jest.spyOn(dialogRef, 'close');

      component.onClosedMonth(null);

      expect(closeSpy).toHaveBeenCalledWith();
    });

    it('should close dialog with closedMonth when closedMonth is provided', () => {
      const closeSpy = jest.spyOn(dialogRef, 'close');
      const testClosedMonth: ClosedMonth = {
        declarationNumber: 123,
        declarationMonth: new Date('2023-01-01'),
      };

      component.onClosedMonth(testClosedMonth);

      expect(closeSpy).toHaveBeenCalledWith(testClosedMonth);
    });
  });
});
