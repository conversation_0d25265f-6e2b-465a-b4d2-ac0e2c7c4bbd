import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';

@Component({
  selector: 'gc-monthly-treatment-card',
  templateUrl: './monthly-treatment-card.component.html',
  standalone: true,
  styleUrls: [
    './monthly-treatment-card.component.scss',
    './monthly-treatment-card-theme.component.scss',
  ],
  imports: [
    ReactiveFormsModule,
    MatCheckboxModule,
    PushPipe,
    TranslocoModule,
    NgClass,
    MatButtonModule,
    MatIconModule,
  ],
})
export class MonthlyTreatmentCardComponent {
  @Input() disabled!: boolean;
  @Output() handleVisualization = new EventEmitter<void>();
}
