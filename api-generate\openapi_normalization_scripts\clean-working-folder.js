const fs = require('fs');
const path = require('path');

const originalOpenapiFilesFolderPath = path.join(
    __dirname,
    '../openapi_files_last_version/'
);

const normalizedOpenapiFilesFolderPath = path.join(
    __dirname,
    '../openapi_files_last_version_normalized/'
);

function deleteFilesAndSubfolders(dir) {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    files.forEach((file) => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            // Recursively delete contents of subdirectory
            deleteFilesAndSubfolders(filePath);

            // Remove the empty subdirectory
            fs.rmdirSync(filePath);
            console.log(`Deleted folder: ${filePath}`);
        } else if (path.extname(file) !== '.md') {
            // Skip .md files, delete everything else
            fs.unlinkSync(filePath);
            console.log(`Deleted file: ${filePath}`);
        } else {
            console.log(`Skipped .md file: ${filePath}`);
        }
    });
}

deleteFilesAndSubfolders(originalOpenapiFilesFolderPath);
deleteFilesAndSubfolders(normalizedOpenapiFilesFolderPath);
