import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatTabsModule } from '@angular/material/tabs';
import {
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet,
} from '@angular/router';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentStoreModule,
} from '@gc/accounting/data-access';
import { CompanyService } from '@gc/company/data-access';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { Paths } from '@gc/core/navigation/models';
import { UserStoreService } from '@gc/user/data-access';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import { take, tap } from 'rxjs';

@Component({
  selector: 'gc-monthly-treatment',
  templateUrl: './monthly-treatment.component.html',
  standalone: true,
  styleUrls: ['./monthly-treatment.component.scss'],
  imports: [
    MainContainerComponent,
    TranslocoModule,
    MatTabsModule,
    RouterLinkActive,
    RouterLink,
    RouterOutlet,
    MonthlyTreatmentStoreModule,
  ],
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'accounting', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/dialog', multi: true },
  ],
})
export class MonthlyTreatmentComponent implements OnInit {
  private readonly _companyService = inject(CompanyService);
  private readonly _router = inject(Router);
  private readonly _userStoreService = inject(UserStoreService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  activeLinkIndex = -1;
  navLinks = [
    {
      label: 'page.navigation-tab.visualization',
      link: Paths.ACCOUNTING_VISUALIZATION,
    },
    {
      label: 'page.navigation-tab.history',
      link: Paths.ACCOUNTING_HISTORY,
    },
  ];

  ngOnInit(): void {
    this.initializeTabsWithCurrentUrl();
    this._handleSelectedCompanyId();
  }

  initializeTabsWithCurrentUrl(): void {
    this._router.events.pipe(take(1)).subscribe(() => {
      const currentUrl = this._router.url.slice(1);
      this.activeLinkIndex = this.navLinks.findIndex(
        (tab) => tab.link === currentUrl
      );
    });
  }

  private _handleSelectedCompanyId(): void {
    this._companyService.companyIdIfSingleCompany$
      .pipe(
        tap((companyId?: string) => {
          const isSingleCompany = !!companyId;
          const defaultCompanyId =
            this._userStoreService.getUserDefaultCompanyId();

          if (isSingleCompany) {
            this._store.dispatch(
              monthlyTreatmentActions.changeCompanyId({
                companyId,
              })
            );
          } else if (defaultCompanyId) {
            this._store.dispatch(
              monthlyTreatmentActions.changeCompanyId({
                companyId: defaultCompanyId,
              })
            );
          }
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }
}
