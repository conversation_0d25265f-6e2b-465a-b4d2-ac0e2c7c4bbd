import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';

import { AccountEntryListTreatmentIdRetrieverCardComponent } from './account-entry-list-treatment-id-retriever-card.component';

describe('AccountEntryListTreatmentIdRetrieverCardComponent', () => {
  let spectator: Spectator<AccountEntryListTreatmentIdRetrieverCardComponent>;
  let component: AccountEntryListTreatmentIdRetrieverCardComponent;

  const createComponent = createComponentFactory({
    component: AccountEntryListTreatmentIdRetrieverCardComponent,
    mocks: [TranslocoService, Store],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
