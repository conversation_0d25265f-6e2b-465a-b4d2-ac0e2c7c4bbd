<div class="main-container" *transloco="let t">
  <mat-drawer-container hasBackdrop="true">
    <mat-drawer #drawer mode="over">
      <gc-menu />
    </mat-drawer>
    <mat-drawer-content>
      <gc-header>
        <button openMenuButton (click)="drawer.toggle()" mat-icon-button>
          <mat-icon>menu</mat-icon>
        </button>

        <button
          openProfileButton
          mat-icon-button
          [matMenuTriggerFor]="profileMenu">
          <mat-icon>person</mat-icon>
        </button>
      </gc-header>
      <div
        class="navigation-container"
        [ngClass]="{ 'overflow-y-auto': scrollable() }">
        <ng-content />
      </div>
    </mat-drawer-content>
  </mat-drawer-container>

  <mat-menu #profileMenu="matMenu">
    <button mat-menu-item (click)="logout()">
      {{ t('header.profile.logout') }}
    </button>
  </mat-menu>
</div>
