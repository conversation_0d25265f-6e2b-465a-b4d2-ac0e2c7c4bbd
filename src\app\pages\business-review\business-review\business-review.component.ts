import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  inject,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import {
  AdvancedSettingsComponent,
  BusinessReviewFormService,
  MainSettingsStepperComponent,
} from '@gc/business-review/feature';
import { UpperCasePipe } from '@angular/common';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { Params, Router } from '@angular/router';
import { QueryParamsKeys, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { BusinessReviewParameters } from '@gc/business-review/models';
import { REPORT_ID, ReportFamilies } from '@gc/shared/stimulsoft/models';
import { URL_PATHS } from '@gc/core/navigation/models';
import { LetDirective } from '@ngrx/component';
import { LoaderComponent, SnackbarService } from '@gc/shared/ui';
import { BehaviorSubject, finalize, map, Observable, take } from 'rxjs';
import { DateAdapter } from '@gc/shared/utils';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LegacyApiService } from '@gc/shared/api/data-access';

type BusinessReviewParametersNotAssociatedWithTreatmentId = Omit<
  BusinessReviewParameters,
  'warehouseIds' | 'isFiscalYear' | 'selectedDates'
>;
@Component({
  selector: 'gc-business-review',
  standalone: true,
  imports: [
    TranslocoModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatButtonModule,
    MainContainerComponent,
    MainSettingsStepperComponent,
    AdvancedSettingsComponent,
    LoaderComponent,
    UpperCasePipe,
    LetDirective,
  ],
  templateUrl: './business-review.component.html',
  styleUrl: './business-review.component.scss',
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'business-review', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BusinessReviewComponent implements OnInit {
  private readonly _service = inject(BusinessReviewFormService);
  private readonly _router = inject(Router);
  private readonly _snackBarService = inject(SnackbarService);
  private readonly _legacyApiService = inject(LegacyApiService);
  private readonly _destroyRef = inject(DestroyRef);

  isLastStep = false;

  businessReviewFG = this._service.businessReviewFG;
  companiesFC = this._service.companiesFC;

  isLoading$ = this._service.isLoading$;
  isPreparingStimulosoftParams$ = new BehaviorSubject<boolean>(false);

  ngOnInit(): void {
    this._service.init();

    this._displaySnackbarWhenFormReinitialized();
  }

  onOpenReport(): void {
    if (this.businessReviewFG.valid) {
      this.isPreparingStimulosoftParams$.next(true);
      const parameters: BusinessReviewParameters =
        this._service.getBusinessReviewParameters();
      this._createTreatmentId(parameters)
        .pipe(
          take(1),
          map((treatmentId) =>
            this._createStimulsoftQueryParams(parameters, treatmentId)
          ),
          finalize(() => this.isPreparingStimulosoftParams$.next(false))
        )
        .subscribe((queryParams) =>
          this._router.navigate([URL_PATHS.businessReviewViewer], {
            queryParams,
          })
        );
    }
  }

  private _createTreatmentId({
    isFiscalYear,
    companyIds,
    selectedDates,
    warehouseIds,
  }: BusinessReviewParameters): Observable<string> {
    return this._legacyApiService
      .businessReviewSaveSalesChannelParameters({
        companyIds,
        dateFromN: DateAdapter.dateToStringAPI(selectedDates.startDate),
        dateToN: DateAdapter.dateToStringAPI(selectedDates.endDate),
        isFiscalYear,
        warehouseIds,
      })
      .pipe(map(({ treatmentId }) => treatmentId!));
  }

  private _createStimulsoftQueryParams(
    parameters: BusinessReviewParametersNotAssociatedWithTreatmentId,
    treatmentId: string
  ): Params {
    return {
      [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.businessReview,
      [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.BUSINESS_REVIEW,
      [QueryParamsKeys.COMPANIES_IDS]: parameters.companyIds,
      // JSON.stringify is used to ensure the order of the dates is preserved in url
      [QueryParamsKeys.DATES]: JSON.stringify(parameters.dates),
      [QueryParamsKeys.VAT_RATE]: parameters.vatRate,
      [StimulsoftQueryParamsKeys.TREATMENT_ID]: treatmentId,
    };
  }

  private _displaySnackbarWhenFormReinitialized(): void {
    this._service.formReinitialized$
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe(() =>
        this._snackBarService.success({
          key: 'businessReview.page.reinitparameters',
        })
      );
  }
}
