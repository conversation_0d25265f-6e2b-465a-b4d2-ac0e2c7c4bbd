import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import {
  CompanyMultiSelectComponent,
  CompanyMultiSelectDefaultSelectionMode,
} from '@gc/company/feature';
import { BusinessReviewFormService } from '../services/business-review-form.service';

@Component({
  selector: 'gc-companies-step',
  standalone: true,
  imports: [
    TranslocoModule,
    ReactiveFormsModule,
    MatButtonModule,
    CompanyMultiSelectComponent,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'business-review',
      multi: true,
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'company',
      multi: true,
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/form',
      multi: true,
    },
  ],
  templateUrl: './companies-step.component.html',
  styleUrl: './companies-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CompaniesStepComponent {
  private readonly _service = inject(BusinessReviewFormService);

  companiesFC = this._service.companiesFC;

  companySelectDefaultSelectionMode =
    CompanyMultiSelectDefaultSelectionMode.ALL;
}
