import { Router } from '@angular/router';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { TranslocoDirective } from '@jsverse/transloco';
import { MockDirective } from 'ng-mocks';
import { Guid<PERSON>elper } from '@isagri-ng/core';
import { URL_PATHS } from '@gc/core/navigation/models';
import { Observable, of } from 'rxjs';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { Warehouse } from '@gc/warehouse/models';
import { VatService } from '@gc/vat/data-access';
import { SnackbarService } from '@gc/shared/ui';
import { BusinessReviewComponent } from './business-review.component';
import { BusinessReviewFormService } from '@gc/business-review/feature';
import { waitForAsync } from '@angular/core/testing';
import { LegacyApiService } from '@gc/shared/api/data-access';

describe('BusinessReviewComponent', () => {
  let spectator: Spectator<BusinessReviewComponent>;
  let component: BusinessReviewComponent;
  let businessReviewFormService: BusinessReviewFormService;
  let vatService: VatService;
  let warehouseService: WarehouseService;
  let router: Router;

  let initSpy: jest.SpyInstance<void>;
  let displaySnackbarWhenFormReinitializedSpy: jest.SpyInstance<void>;

  const createComponent = createComponentFactory({
    component: BusinessReviewComponent,
    declarations: [MockDirective(TranslocoDirective)],
    detectChanges: false,
    mocks: [
      FiscalYearService,
      WarehouseService,
      VatService,
      SnackbarService,
      LegacyApiService,
      Router,
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
    router = spectator.inject(Router);
    businessReviewFormService = spectator.inject(BusinessReviewFormService);
    warehouseService = spectator.inject(WarehouseService);
    vatService = spectator.inject(VatService);

    jest
      .spyOn(warehouseService, 'getWarehouses')
      .mockReturnValue(of([] as Warehouse[]));

    jest
      .spyOn(vatService, 'getDeduplicatedRatesValuesByCompanies')
      .mockReturnValue(of([]));
    // The init method is called in the ngOnInit method
    initSpy = jest.spyOn(businessReviewFormService, 'init');

    displaySnackbarWhenFormReinitializedSpy = jest.spyOn(
      component as any,
      '_displaySnackbarWhenFormReinitialized'
    );

    spectator.component.ngOnInit();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('ngOnInit method', () => {
    it('should call the service init method', () => {
      expect(initSpy).toHaveBeenCalled();
      expect(displaySnackbarWhenFormReinitializedSpy).toHaveBeenCalled();
    });
  });

  describe('onOpenReport method', () => {
    let createTreatmentIdSpy: jest.SpyInstance<Observable<string>>;
    const fakeTreatmentId = '1345678945689';
    beforeEach(() => {
      createTreatmentIdSpy = jest.spyOn(component as any, '_createTreatmentId');
    });

    describe('given a state where businessReviewFG is valid', () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      beforeEach(() => {
        spectator.component.businessReviewFG.setValue({
          companies: ['company1', 'company2'],
          periodicity: {
            periodicityKind: 'calendarYear',
            calendarYearFC: {
              startDate,
              endDate,
              viewValue: '2024',
            },
            fiscalYearFC: null,
          },
          warehouses: [GuidHelper.newGuid(), GuidHelper.newGuid()],
          vatRate: 20,
        });
      });

      describe('and _createTreatmentId return an Observable of treatmentId', () => {
        beforeEach(() => {
          createTreatmentIdSpy.mockReturnValue(of(fakeTreatmentId));
        });
        it('should call the service getBusinessReviewParameters method', () => {
          const serviceSpy = jest.spyOn(
            spectator.component['_service'],
            'getBusinessReviewParameters'
          );
          spectator.component.onOpenReport();
          expect(serviceSpy).toHaveBeenCalled();
        });

        it('should call the router navigate method with the expected queryParams and call _createTreatmentId method with warehouse ids', waitForAsync(() => {
          const selectedCompanies = [
            GuidHelper.newGuid(),
            GuidHelper.newGuid(),
          ];
          const selectedDates = [
            '2024-01-01',
            '2024-12-31',
            '2023-01-01',
            '2023-12-31',
          ];

          const selectedWarehousesIds = [
            GuidHelper.newGuid(),
            GuidHelper.newGuid(),
          ];

          const selectedVatRate = 2.12;

          const businessReviewParamters = {
            companyIds: selectedCompanies,
            dates: selectedDates,
            warehouseIds: selectedWarehousesIds,
            vatRate: selectedVatRate,
            isFiscalYear: false,
            selectedDates: {
              startDate,
              endDate,
            },
          };

          jest
            .spyOn(
              spectator.component['_service'],
              'getBusinessReviewParameters'
            )
            .mockReturnValue(businessReviewParamters);

          const navigateSpy = jest.spyOn(router, 'navigate');

          spectator.component.onOpenReport();
          expect(createTreatmentIdSpy).toHaveBeenCalledWith(
            businessReviewParamters
          );
          expect(navigateSpy).toHaveBeenCalledWith(
            [URL_PATHS.businessReviewViewer],
            {
              queryParams: {
                reportId: 'BILCO',
                reportFamily: 'BILCO',
                companiesIds: selectedCompanies,
                dates: JSON.stringify(selectedDates),
                vatRate: selectedVatRate,
                treatmentId: fakeTreatmentId,
              },
            }
          );
        }));
      });
    });

    describe('Given a state where businessReviewFG is invalid', () => {
      beforeEach(() => {
        spectator.component.businessReviewFG.setValue({
          companies: [],
          periodicity: {
            periodicityKind: 'calendarYear',
            calendarYearFC: {
              startDate: new Date('2024-01-01'),
              endDate: new Date('2024-12-31'),
              viewValue: '2024',
            },
            fiscalYearFC: null,
          },
          warehouses: [GuidHelper.newGuid(), GuidHelper.newGuid()],
          vatRate: null,
        });
      });

      it('should not call the service getBusinessReviewParameters method', () => {
        const serviceSpy = jest.spyOn(
          spectator.component['_service'],
          'getBusinessReviewParameters'
        );
        spectator.component.onOpenReport();
        expect(serviceSpy).not.toHaveBeenCalled();
      });

      it('should not call the router navigate method', () => {
        const navigateSpy = jest.spyOn(router, 'navigate');
        spectator.component.onOpenReport();
        expect(navigateSpy).not.toHaveBeenCalled();
      });
    });
  });

  describe('displaySnackbarWhenFormReinitialized method', () => {
    let successSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      const snackbarService = spectator.inject(SnackbarService);
      successSpy = jest.spyOn(snackbarService, 'success');
    });
    describe('given a state where formReinitialized$ of BusinessReviewFormService emit', () => {
      beforeEach(() => {
        jest
          .spyOn(businessReviewFormService, 'formReinitialized$', 'get')
          .mockReturnValue(of(void 0));
      });

      it('should call success method of SnackbarService', () => {
        (component as any)._displaySnackbarWhenFormReinitialized();
        expect(successSpy).toHaveBeenCalledWith({
          key: 'businessReview.page.reinitparameters',
        });
      });
    });
  });
});
