import { Dunning<PERSON><PERSON>, DunningResponseApi } from '@gc/shared/api/data-access';
import {
  DunningPagination,
  DunningInvoice,
  DunningDueDate,
} from '@gc/dunning/models';
import { DunningApiAdapter } from '../adapters-api/dunning-api.adapter';
import { DunningInvoiceAdapter } from './dunning-invoice.adapter';
import { DunningDueDateAdapter } from './dunning-due-date.adapter';

export class DunningPaginationAdapter {
  public static fromApi(
    dunningResponseApi: DunningResponseApi
  ): DunningPagination {
    const {
      firstElementNumber,
      elementsPerPage,
      totalElements,
      dunnings: dunningsApi,
      totalAmountDue,
    } = dunningResponseApi;

    const allPageInvoices: DunningInvoice[] =
      DunningInvoiceAdapter.fromDunningsApi(dunningsApi);

    const allPageDueDates: DunningDueDate[] =
      DunningDueDateAdapter.fromDunningsApi(dunningsApi);

    return {
      firstElementNumber,
      elementsPerPage,
      totalElements,
      dunnings: dunningsApi.map((dunningApi: DunningApi) =>
        DunningApiAdapter.fromApi(dunningApi)
      ),
      dunningsInvoices: allPageInvoices,
      dunningsInvoicesDueDates: allPageDueDates,
      totalAmountDue: totalAmountDue,
    };
  }
}
