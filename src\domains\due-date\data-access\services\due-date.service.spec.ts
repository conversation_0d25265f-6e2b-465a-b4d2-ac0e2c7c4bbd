import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { DueDateService } from './due-date.service';
import {
  LegacyApiService,
  DueDateResponseApi,
} from '@gc/shared/api/data-access';
import { DueDatePaginationAdapter } from '../adapters/due-date-pagination.adapter';
import { of, Observable } from 'rxjs';
import {
  DueDatePagination,
  DueDateFilter,
  DueDate,
  anyDueDate,
} from '@gc/due-date/models';
import { waitForAsync } from '@angular/core/testing';
import { DueDateFilterApi } from '../models/api/due-date-filter-api.model';
import { DueDateFilterAdapter } from '../adapters/due-date-filter.adapter';

describe('DunningDueDateService', () => {
  let spectator: SpectatorService<DueDateService>;
  let dueDateService: DueDateService;

  const createService = createServiceFactory({
    service: DueDateService,
    mocks: [LegacyApiService],
  });

  beforeEach(() => {
    spectator = createService();
    dueDateService = spectator.service;
  });

  describe('getDueDates method', () => {
    let dueDatesGetDueDatesSpy: jest.SpyInstance<
      Observable<DueDateResponseApi>
    >;
    let dueDatePaginationAdapterFromApiSpy: jest.SpyInstance<DueDatePagination>;

    let dueDateFilterAdapterToApiSpy: jest.SpyInstance<DueDateFilterApi>;

    beforeEach(() => {
      const legacyApiService = spectator.inject(LegacyApiService);
      dueDatesGetDueDatesSpy = jest.spyOn(
        legacyApiService,
        'dueDatesGetDueDates'
      ) as unknown as jest.SpyInstance<Observable<DueDateResponseApi>>;

      dueDatePaginationAdapterFromApiSpy = jest.spyOn(
        DueDatePaginationAdapter,
        'fromApi'
      ) as unknown as jest.SpyInstance<DueDatePagination>;

      dueDateFilterAdapterToApiSpy = jest.spyOn(
        DueDateFilterAdapter,
        'toApi'
      ) as unknown as jest.SpyInstance<DueDateFilterApi>;
    });

    describe('given a state where dueDatesGetDueDates method of LegacyApiService returns a valid DueDateResponseApi object', () => {
      const fakeDueDateResponseApi: DueDateResponseApi = {
        firstElementNumber: 1,
        elementsPerPage: 1,
        totalElements: 15,
        dueDates: [],
      };

      beforeEach(() => {
        dueDatesGetDueDatesSpy.mockReturnValueOnce(of(fakeDueDateResponseApi));
      });

      describe('and toApi method of DueDateFilterAdapter returns any DueDateFilterApi object', () => {
        const invoiceId = '8888-9999-6666-7777';
        const dueDateFilterApi: DueDateFilterApi = {
          documentId: invoiceId,
        };

        beforeEach(() => {
          dueDateFilterAdapterToApiSpy.mockReturnValueOnce(dueDateFilterApi);
        });

        describe('and fromApi method of DueDatePaginationAdapter returns a valid DueDatePagination object', () => {
          const fakeDueDatePagination: DueDatePagination = {
            firstElementNumber: 1,
            elementsPerPage: 1,
            totalElements: 15,
            dueDates: [],
          };

          beforeEach(() => {
            dueDatePaginationAdapterFromApiSpy.mockReturnValueOnce(
              fakeDueDatePagination
            );
          });

          describe('when method is called with a valid Filter object', () => {
            const fakeDueDateFilter: DueDateFilter = {
              invoiceId,
            };

            it('then should call fromApi of DueDatePaginationAdapter, toApi of DueDateFilterAdapter , dueDatesGetDueDates methods', waitForAsync(() => {
              const expectedAssertions = 3;

              expect.assertions(expectedAssertions);

              dueDateService.getDueDates(fakeDueDateFilter).subscribe(() => {
                expect(dueDatesGetDueDatesSpy).toHaveBeenCalledWith(
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  invoiceId,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined
                );

                expect(dueDateFilterAdapterToApiSpy).toHaveBeenCalledWith(
                  fakeDueDateFilter
                );

                expect(dueDatePaginationAdapterFromApiSpy).toHaveBeenCalledWith(
                  fakeDueDateResponseApi
                );
              });
            }));

            it('then should return DueDatePagination object', waitForAsync(() => {
              const expectedAssertions = 1;
              expect.assertions(expectedAssertions);

              dueDateService
                .getDueDates(fakeDueDateFilter)
                .subscribe((dueDatePagination: DueDatePagination) => {
                  expect(dueDatePagination).toEqual(fakeDueDatePagination);
                });
            }));
          });
        });
      });
    });
  });

  describe('getAllDueDatesByInvoiceId method', () => {
    let getDueDatesSpy: jest.SpyInstance<Observable<DueDatePagination>>;

    beforeEach(() => {
      getDueDatesSpy = jest.spyOn(dueDateService, 'getDueDates');
    });

    describe('given a state where getDueDates method return a Observable<DueDatePagination>', () => {
      const dueDate1: DueDate = {
        ...anyDueDate,
      };
      const dueDate2: DueDate = {
        ...anyDueDate,
        id: '7896-9874-9879-9635',
      };
      const fakeDueDatePagination: DueDatePagination = {
        firstElementNumber: 1,
        elementsPerPage: 1,
        totalElements: 15,
        dueDates: [dueDate1, dueDate2],
      };

      beforeEach(() => {
        getDueDatesSpy.mockReturnValueOnce(of(fakeDueDatePagination));
      });

      describe('when method is called with an invoiceId', () => {
        const invoiceId = '4444-5555-6666-7777';

        it('then should call getDueDates method', waitForAsync(() => {
          const expectedAssertions = 1;
          expect.assertions(expectedAssertions);

          dueDateService.getAllDueDatesByInvoiceId(invoiceId).subscribe(() => {
            expect(getDueDatesSpy).toHaveBeenCalledWith({
              invoiceId,
            });
          });
        }));

        it('then should return a list of DueDate', waitForAsync(() => {
          const expectedAssertions = 1;
          const expectedDueDates: DueDate[] = [dueDate1, dueDate2];
          expect.assertions(expectedAssertions);

          dueDateService
            .getAllDueDatesByInvoiceId(invoiceId)
            .subscribe((dueDates: DueDate[]) => {
              expect(dueDates).toStrictEqual(expectedDueDates);
            });
        }));
      });
    });
  });
});
