import { waitForAsync } from '@angular/core/testing';
import { NavigationBackContainerComponent } from '@gc/core/navigation/feature';
import {
  DunningParametersState,
  DunningParametersStoreModule,
  DunningStoreModule,
  dunningParametersInitialState,
  selectCurrentDunningLetter,
} from '@gc/dunning/data-access';
import { DunningLetter, dunningLettersFixture } from '@gc/dunning/models';
import {
  DialogService,
  MasterDetailPanelsLayoutComponent,
} from '@gc/shared/ui';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { MockComponents, MockDirectives, MockModule } from 'ng-mocks';
import { DunningParametersComponent } from './dunning-parameters.component';
import { DunningLetterEditComponent } from '@gc/dunning/feature';

describe('DunningParametersComponent', () => {
  let spectator: Spectator<DunningParametersComponent>;
  let store: MockStore;

  const dunningParametersState: DunningParametersState =
    dunningParametersInitialState;

  const initialState = {
    dunningParameters: dunningParametersState,
  };

  const createComponent = createComponentFactory({
    component: DunningParametersComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(
        NavigationBackContainerComponent,
        MasterDetailPanelsLayoutComponent,
        DunningLetterEditComponent
      ),
      MockModule(DunningParametersStoreModule),
      MockModule(DunningStoreModule),
    ],
    providers: [provideMockStore({ initialState })],
    mocks: [DialogService, TranslocoService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    store = spectator.inject(MockStore);
    spectator.detectChanges();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('currentDunningLetter$', () => {
    describe('Given an initialState', () => {
      it('should emit undefined', waitForAsync(() => {
        spectator.component.currentDunningLetter$.subscribe((value) => {
          expect(value).toEqual(undefined);
        });
      }));
    });

    describe('Given the selectCurrentDunningLetter selector returns e duning letter', () => {
      const mockDunningLetters: DunningLetter[] = dunningLettersFixture();
      const [, , expectedCurrentDunningLetter] = mockDunningLetters;

      beforeEach(() => {
        store.overrideSelector(
          selectCurrentDunningLetter,
          expectedCurrentDunningLetter
        );
        store.refreshState();
      });

      it('should emit the dunning letter', waitForAsync(() => {
        spectator.component.currentDunningLetter$.subscribe((value) => {
          expect(value).toEqual(expectedCurrentDunningLetter);
        });
      }));
    });
  });

  describe('currentLetterHasUnsavedChanges$', () => {
    describe('Given an initialState', () => {
      beforeEach(() => {
        initialState.dunningParameters = dunningParametersInitialState;
        store.refreshState();
      });
      it('should emit undefined and set value of showBrowserNotificationIfUnsavedChanges to undefined', waitForAsync(() => {
        spectator.component.currentLetterHasUnsavedChanges$.subscribe(
          (value) => {
            expect(value).toEqual(undefined);
            expect(
              spectator.component.showBrowserNotificationIfUnsavedChanges
            ).toEqual(undefined);
          }
        );
      }));
    });

    describe('Given a state where currentLetterHasUnsavedChanges is set to true', () => {
      beforeEach(() => {
        initialState.dunningParameters = {
          ...dunningParametersInitialState,
          currentLetterHasUnsavedChanges: true,
        };
        store.refreshState();
      });
      it('should emit tue and set value of showBrowserNotificationIfUnsavedChanges to true', waitForAsync(() => {
        spectator.component.currentLetterHasUnsavedChanges$.subscribe(
          (value) => {
            expect(value).toEqual(true);
            expect(
              spectator.component.showBrowserNotificationIfUnsavedChanges
            ).toEqual(true);
          }
        );
      }));
    });

    describe('Given a state where currentLetterHasUnsavedChanges is set to false', () => {
      beforeEach(() => {
        initialState.dunningParameters = {
          ...dunningParametersInitialState,
          currentLetterHasUnsavedChanges: false,
        };
        store.refreshState();
      });
      it('should emit tue and set value of showBrowserNotificationIfUnsavedChanges to false', waitForAsync(() => {
        spectator.component.currentLetterHasUnsavedChanges$.subscribe(
          (value) => {
            expect(value).toEqual(false);
            expect(
              spectator.component.showBrowserNotificationIfUnsavedChanges
            ).toEqual(false);
          }
        );
      }));
    });
  });

  describe('onBeforeUnload', () => {
    describe('Given showBrowserNotificationIfUnsavedChanges is set to true', () => {
      beforeEach(() => {
        spectator.component.showBrowserNotificationIfUnsavedChanges = true;
      });
      it('should set event.returnValue to true', () => {
        const event = {} as unknown as BeforeUnloadEvent;
        Object.defineProperty(event, 'returnValue', { writable: true });
        spectator.component.onBeforeUnload(event);
        expect(event.returnValue).toEqual(true);
      });
    });
    describe('Given showBrowserNotificationIfUnsavedChanges is set to false', () => {
      beforeEach(() => {
        spectator.component.showBrowserNotificationIfUnsavedChanges = false;
      });
      it('should set event.returnValue to undefined', () => {
        const event = {} as unknown as BeforeUnloadEvent;
        Object.defineProperty(event, 'returnValue', { writable: true });
        spectator.component.onBeforeUnload(event);
        expect(event.returnValue).toEqual(undefined);
      });
    });
  });
});
