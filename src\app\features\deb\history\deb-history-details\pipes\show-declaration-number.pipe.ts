import { Pipe, PipeTransform } from '@angular/core';
import { ClosedMonth } from '@gc/core/deb/domains/models';

@Pipe({
  name: 'showDeclarationNumber',
  standalone: true,
})
export class ShowDeclarationNumberPipe implements PipeTransform {
  transform(lineDetails: ClosedMonth, dataSource: ClosedMonth[]): string {
    if (lineDetails.declarationNumber === 0) {
      return '-';
    }

    const sortedDataSource = [...dataSource].sort(
      (a, b) =>
        new Date(a.declarationMonth).getTime() -
        new Date(b.declarationMonth).getTime()
    );

    const currentIndex = sortedDataSource.findIndex(
      (item) => item.declarationMonth === lineDetails.declarationMonth
    );

    const previousItem =
      currentIndex > 0 ? sortedDataSource[currentIndex - 1] : null;
    const nextItem =
      currentIndex < sortedDataSource.length - 1
        ? sortedDataSource[currentIndex + 1]
        : null;

    if (!nextItem) {
      return lineDetails.declarationNumber.toString();
    }

    const isDifferentFromPrevious =
      previousItem &&
      previousItem.declarationNumber !== lineDetails.declarationNumber;

    const isDifferentFromNext =
      nextItem && nextItem.declarationNumber !== lineDetails.declarationNumber;

    if (isDifferentFromPrevious && !isDifferentFromNext) {
      return '-';
    }

    return isDifferentFromPrevious || isDifferentFromNext
      ? lineDetails.declarationNumber.toString()
      : '-';
  }
}
