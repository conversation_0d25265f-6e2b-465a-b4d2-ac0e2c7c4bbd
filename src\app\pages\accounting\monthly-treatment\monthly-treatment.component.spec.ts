import { NavigationStart, Router, RouterOutlet } from '@angular/router';
import { Spectator } from '@ngneat/spectator';
import { createComponentFactory } from '@ngneat/spectator/jest';
import { MockComponents, MockDirective, MockModule } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { MonthlyTreatmentComponent } from './monthly-treatment.component';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { Store } from '@ngrx/store';
import { UserStoreService } from '@gc/user/data-access';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentStoreModule,
} from '@gc/accounting/data-access';
import { waitForAsync } from '@angular/core/testing';
import { CompanyService } from '@gc/company/data-access';

const routerWithMonthlyVisualization = {
  url: '/visualization',
  events: of(
    new NavigationStart(
      0,
      'http://localhost:4200/product/accounting/monthly_treatment/visualization'
    )
  ),
};

describe('MonthlyTreatmentComponent', () => {
  let spectator: Spectator<MonthlyTreatmentComponent>;
  let component: MonthlyTreatmentComponent;

  const createComponent = createComponentFactory({
    component: MonthlyTreatmentComponent,
    declarations: [
      MockComponents(MainContainerComponent),
      MockDirective(RouterOutlet),
      MockModule(MonthlyTreatmentStoreModule),
    ],
    mocks: [CompanyService, UserStoreService, Store],
    providers: [{ provide: Router, useValue: routerWithMonthlyVisualization }],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call initializeTabsWithCurrentUrl and handleSelectedCompanyId', () => {
      const initializeTabsWithCurrentUrlSpy = jest.spyOn(
        component,
        'initializeTabsWithCurrentUrl'
      );
      const handleSelectedCompanyIdSpy = jest
        .spyOn(component as any, '_handleSelectedCompanyId')
        .mockImplementation(() => {});

      component.ngOnInit();

      expect(initializeTabsWithCurrentUrlSpy).toHaveBeenCalled();
      expect(handleSelectedCompanyIdSpy).toHaveBeenCalled();
    });
  });

  describe('initializeTabsWithCurrentUrl', () => {
    describe('given a state where the active url contain visualization', () => {
      it('should set activeLinkIndex to 0', () => {
        component.initializeTabsWithCurrentUrl();

        expect(component.activeLinkIndex).toEqual(0);
      });
    });
  });

  describe('_handleSelectedCompanyId', () => {
    let getUserDefaultCompanyIdSpy: jest.SpyInstance<string | undefined>;
    let getCompanyIdIfSingleCompanySpy: jest.SpyInstance<
      Observable<string | undefined>
    >;
    let dispatchSpy: jest.SpyInstance<void>;

    const defaultCompanyId = 'defaultCompanyId';
    beforeEach(() => {
      const companyService = spectator.inject(CompanyService);
      const store = spectator.inject(Store);
      const userStoreService = spectator.inject(UserStoreService);

      dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

      getUserDefaultCompanyIdSpy = jest
        .spyOn(userStoreService, 'getUserDefaultCompanyId')
        .mockReturnValue(defaultCompanyId);

      getCompanyIdIfSingleCompanySpy = jest.spyOn(
        companyService,
        'companyIdIfSingleCompany$',
        'get'
      );
    });

    describe('given a state where companyIdIfSingleCompany$ getter return a company and getUserDefaultCompanyId return a default company', () => {
      const companyId = 'companyId';
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should dispatch changeCompanyId action with companyId as payload and not call initSelectCompanyFormControl', waitForAsync(() => {
        (component as any)._handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.changeCompanyId({ companyId })
        );
      }));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return an undefined company and getUserDefaultCompanyId return a default company', () => {
      const companyId = undefined;
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should dispatch changeCompanyId action with defaultCompanyId as payload and do call initSelectCompanyFormControl', waitForAsync(() => {
        (component as any)._handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.changeCompanyId({
            companyId: defaultCompanyId,
          })
        );
      }));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return an undefined company and getUserDefaultCompanyId return an undefined default company', () => {
      const companyId = undefined;
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
        getUserDefaultCompanyIdSpy.mockReturnValue(undefined);
      });
      it('should not dispatch changeCompanyId action and do call initSelectCompanyFormControl', waitForAsync(() => {
        (component as any)._handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(dispatchSpy).not.toHaveBeenCalled();
      }));
    });
  });
});
