import { CommonModule } from '@angular/common';
import { NgModule, inject } from '@angular/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { FEATURE_KEY_MONTHLY_TREATMENT_HISTORY } from './models/monthly-treatment-history-key.constant';
import { MonthlyTreatmentHistoryEffects } from './effects/monthly-treatment-history.effects';
import { monthlyTreatmentHistoryReducer } from './reducers/monthly-treatment-history.reducer';
import { MonthlyTreatmentStoreModule } from '../monthly-treatment/monthly-treatment-store.module';

@NgModule({
  imports: [
    CommonModule,
    StoreModule.forFeature(
      FEATURE_KEY_MONTHLY_TREATMENT_HISTORY,
      monthlyTreatmentHistoryReducer
    ),
    EffectsModule.forFeature([MonthlyTreatmentHistoryEffects]),
  ],
})
export class MonthlyTreatmentHistoryStoreModule {
  constructor() {
    const monthlyTreatmentStoreModule = inject(MonthlyTreatmentStoreModule, {
      optional: true,
    });

    if (!monthlyTreatmentStoreModule) {
      throw new Error(
        'MonthlyTreatmentHistoryStoreModule uses MonthlyTreatmentStoreModule, you need to make sure MonthlyTreatmentStoreModule module is imported'
      );
    }
  }
}
