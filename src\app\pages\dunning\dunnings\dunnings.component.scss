.container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .container-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .dunning-filters {
        display: flex;
        justify-content: center;
    }

    .action-buttons-container {
        display: flex;
        justify-content: flex-end;
        gap: 5px;

        // adjustment to align actions-buttons with filter inputs without restructuring
        // filter inputs and filter results are in a component and action buttons are in another component
        padding: 16px 0;
    }

    .list-actions-container {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 10px;
        gap: 10px;
    }

    mat-drawer-container {
        flex: 1;
        overflow: hidden;

        mat-drawer {
            padding: 20px;

            gc-dunning-filters {
                height: 100%;
            }
        }

        mat-drawer-content {
            display: flex;
            justify-content: center;
            height: 100%;

            .gc-dunning-list-container {
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
        }
    }
}
