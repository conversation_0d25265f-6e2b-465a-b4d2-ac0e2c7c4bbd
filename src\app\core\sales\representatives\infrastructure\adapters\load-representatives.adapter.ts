import { inject, Injectable } from '@angular/core';
import { map } from 'rxjs';
import { Representative } from '../../domains/models';
import { LoadRepresentativesPort } from '../../domains/ports';
import { RepresentativesApiService } from '../api';

@Injectable()
export class LoadRepresentativesAdapter implements LoadRepresentativesPort {
  private readonly api = inject(RepresentativesApiService);

  for(companyId: string) {
    return this.api
      .loadRepresentatives(companyId)
      .pipe(
        map((representatives) =>
          representatives.map(
            (representative) => <Representative>representative
          )
        )
      );
  }
}
