import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { DebFacade } from '@gc/core/deb/application/facades';
import { DebDetails, DebParameters } from '@gc/core/deb/domains/models';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { DebParametersEditComponent } from './deb-parameters-edit.component';
import { signal } from '@angular/core';
import { ResourceState } from '@gc/core/shared/store';

describe('DebParametersEditComponent', () => {
  let spectator: Spectator<DebParametersEditComponent>;
  let component: DebParametersEditComponent;
  let facade: DebFacade;
  const testParameters: DebParameters = {
    declarantName: 'Test Name',
    authorizationNumber: '1234',
    declarationNumber: 5,
    declarationType: 'light',
  };
  const mockDebDetails: DebDetails[] = [
    {
      documentType: 'Invoice' as any,
      documentNumber: 'INV001',
      customerCode: 'CUST001',
      deliveryDate: '2023-01-01',
      productCode: 'PROD001',
      productLabel: 'Test Product',
      lineAmount: 100,
    },
  ];
  const mockResourceState: ResourceState<DebDetails[]> = {
    data: mockDebDetails,
    isLoading: false,
    status: 'Success',
    errors: undefined,
  };
  const mockDebFacade = {
    provide: DebFacade,
    useValue: {
      getDetails: jest.fn().mockReturnValue(signal(mockResourceState)),
      hasMissingParameters: jest.fn().mockReturnValue(false),
    },
  };
  const createComponent = createComponentFactory({
    component: DebParametersEditComponent,
    imports: [
      ReactiveFormsModule,
      MatButtonModule,
      MatDialogModule,
      MatFormFieldModule,
      MatInputModule,
      MatRadioModule,
      NoopAnimationsModule,
    ],
    declarations: [MockDirective(TranslocoDirective)],
    providers: [
      { provide: MAT_DIALOG_DATA, useValue: testParameters },
      { provide: MatDialogRef, useValue: { close: jest.fn() } },
      mockDebFacade,
    ],
    componentProviders: [mockDebFacade],
    mocks: [TranslocoService, DebFacade],
  });
  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
    facade = spectator.inject(DebFacade);
  });

  describe('Component initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });
    it('should initialize form with provided parameters', () => {
      expect(component.parametersFG.value).toEqual({
        declarantName: testParameters.declarantName,
        authorizationNumber: testParameters.authorizationNumber,
        declarationNumber: testParameters.declarationNumber,
        declarationType: testParameters.declarationType,
      });
    });
    it('should expose constant values as readonly properties', () => {
      expect(component.declarantNameMaxLength).toBe(14);
      expect(component.authorizationNumberMaxLength).toBe(4);
      expect(component.declarationNumberMinValue).toBe(1);
    });
  });

  describe('Form validation', () => {
    it('should have a valid form when all fields are valid', () => {
      component.parametersFG.setValue({
        declarantName: 'Valid Name',
        authorizationNumber: '1234',
        declarationNumber: 5,
        declarationType: 'light',
      });
      expect(component.parametersFG.valid).toBeTruthy();
    });
  });

  describe('declarantName field', () => {
    it('should be required', () => {
      const declarantNameControl = component.parametersFG.get('declarantName');
      declarantNameControl?.setValue('');
      expect(declarantNameControl?.valid).toBeFalsy();
      expect(declarantNameControl?.hasError('required')).toBeTruthy();
    });
    it('should not exceed max length', () => {
      const declarantNameControl = component.parametersFG.get('declarantName');
      declarantNameControl?.setValue('ThisIsAVeryLongNameThatExceedsMaxLength');
      expect(declarantNameControl?.valid).toBeFalsy();
      expect(declarantNameControl?.hasError('maxlength')).toBeTruthy();
    });
    it('should be valid with proper value', () => {
      const declarantNameControl = component.parametersFG.get('declarantName');
      declarantNameControl?.setValue('Valid Name');
      expect(declarantNameControl?.valid).toBeTruthy();
    });
  });

  describe('authorizationNumber field', () => {
    it('should be required', () => {
      const authNumberControl = component.parametersFG.get(
        'authorizationNumber'
      );
      authNumberControl?.setValue('');
      expect(authNumberControl?.valid).toBeFalsy();
      expect(authNumberControl?.hasError('required')).toBeTruthy();
    });
    it('should not exceed max length', () => {
      const authNumberControl = component.parametersFG.get(
        'authorizationNumber'
      );
      authNumberControl?.setValue('12345');
      expect(authNumberControl?.valid).toBeFalsy();
      expect(authNumberControl?.hasError('maxlength')).toBeTruthy();
    });
    it('should be valid with proper value', () => {
      const authNumberControl = component.parametersFG.get(
        'authorizationNumber'
      );
      authNumberControl?.setValue('1234');
      expect(authNumberControl?.valid).toBeTruthy();
    });
  });

  describe('declarationNumber field', () => {
    it('should be required', () => {
      const declarationNumberControl =
        component.parametersFG.get('declarationNumber');
      declarationNumberControl?.setValue(undefined);
      expect(declarationNumberControl?.valid).toBeFalsy();
      expect(declarationNumberControl?.hasError('required')).toBeTruthy();
    });
    it('should be a valid number (pattern validation)', () => {
      const declarationNumberControl =
        component.parametersFG.get('declarationNumber');
      declarationNumberControl?.setValue(NaN);
      expect(declarationNumberControl?.valid).toBeFalsy();
      expect(declarationNumberControl?.hasError('pattern')).toBeTruthy();
    });
    it('should be greater than minimum value', () => {
      const declarationNumberControl =
        component.parametersFG.get('declarationNumber');
      declarationNumberControl?.setValue(0);
      expect(declarationNumberControl?.valid).toBeFalsy();
      expect(declarationNumberControl?.hasError('min')).toBeTruthy();
    });
    it('should be valid with proper value', () => {
      const declarationNumberControl =
        component.parametersFG.get('declarationNumber');
      declarationNumberControl?.setValue(5);
      expect(declarationNumberControl?.valid).toBeTruthy();
    });
  });

  describe('declarationType field', () => {
    it('should be required', () => {
      const declarationTypeControl =
        component.parametersFG.get('declarationType');
      declarationTypeControl?.setValue(undefined);
      expect(declarationTypeControl?.valid).toBeFalsy();
      expect(declarationTypeControl?.hasError('required')).toBeTruthy();
    });
    it('should be valid with proper value', () => {
      const declarationTypeControl =
        component.parametersFG.get('declarationType');
      declarationTypeControl?.setValue('light');
      expect(declarationTypeControl?.valid).toBeTruthy();
    });
  });
});
