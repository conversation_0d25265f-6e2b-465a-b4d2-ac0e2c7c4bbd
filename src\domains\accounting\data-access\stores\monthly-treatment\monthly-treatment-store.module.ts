import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { MonthlyTreatmentEffects } from './effects/monthly-treatment.effects';
import { FEATURE_KEY_MONTHLY_TREATMENT } from './models/monthly-treatment-key.constant';
import { monthlyTreatmentReducer } from './reducers/monthly-treatment.reducer';

@NgModule({
  imports: [
    CommonModule,
    StoreModule.forFeature(
      FEATURE_KEY_MONTHLY_TREATMENT,
      monthlyTreatmentReducer
    ),
    EffectsModule.forFeature([MonthlyTreatmentEffects]),
  ],
})
export class MonthlyTreatmentStoreModule {}
