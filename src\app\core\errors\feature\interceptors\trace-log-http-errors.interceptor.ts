import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ITraceService } from '@isagri-ng/core/diagnostics';
import { catchError, Observable, throwError } from 'rxjs';

@Injectable()
export class TraceLogHttpErrorsInterceptor implements HttpInterceptor {
  private readonly _traceService = inject(ITraceService);

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: unknown) => {
        if (!(error instanceof HttpErrorResponse)) {
          return throwError(() => error);
        }

        this._traceService.logErrorException(error);
        return throwError(() => error);
      })
    );
  }
}
