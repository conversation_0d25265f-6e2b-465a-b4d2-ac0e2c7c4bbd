import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { CompaniesStepComponent } from './companies-step.component';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { VatService } from '@gc/vat/data-access';
import { SnackbarService } from '@gc/shared/ui';

describe('CompaniesStepComponent', () => {
  let spectator: Spectator<CompaniesStepComponent>;
  const createComponent = createComponentFactory({
    component: CompaniesStepComponent,
    declarations: [MockDirective(TranslocoDirective)],
    mocks: [FiscalYearService, WarehouseService, VatService, SnackbarService],
  });

  it('should create', () => {
    spectator = createComponent();

    expect(spectator.component).toBeTruthy();
  });
});
