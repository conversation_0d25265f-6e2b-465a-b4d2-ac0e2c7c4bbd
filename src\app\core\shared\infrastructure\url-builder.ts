export class UrlBuilder {
  private readonly queryParams: Record<string, string>;
  private url: string;

  private constructor(baseUrl: string) {
    this.url = baseUrl;
    this.queryParams = {};
  }

  static create(baseUrl: string): UrlBuilder {
    return new UrlBuilder(baseUrl);
  }

  withRouteParam(param: string): this {
    this.url = `${this.url}/${param}`;
    return this;
  }

  withQueryParam(key: string, value: unknown): this {
    const stringValue = String(value);
    if (value !== 0 && value !== false && (!value || !stringValue)) {
      return this;
    }

    this.queryParams[key] = String(value);
    return this;
  }

  build(): string {
    const queryString = Object.entries(this.queryParams)
      .map(([key, value], index) => {
        const prefix = index === 0 ? '?' : '&';
        return `${prefix}${key}=${encodeURIComponent(value)}`;
      })
      .join('');

    return `${this.url}${queryString}`;
  }
}
