import { DunningLevelProposalApi } from '@gc/shared/api/data-access';
import { DunningNextLevelProposal } from '@gc/dunning/models';
import { DunningLevelAdapter } from './dunning-level.adapter';

export class DunningNextLevelProposalAdapter {
  static fromApi(
    dunningLevelProposalApi: DunningLevelProposalApi
  ): DunningNextLevelProposal {
    return {
      level: DunningLevelAdapter.fromApi(
        dunningLevelProposalApi?.dunningLevelId
      ),
      nextLevel: DunningLevelAdapter.fromApi(
        dunningLevelProposalApi.nextDunningLevelIdProposal
      ),
    };
  }
}
