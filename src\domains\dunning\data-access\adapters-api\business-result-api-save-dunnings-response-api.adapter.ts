import { SaveDunningsResponseBusinessResultAPIApi } from '@gc/shared/api/data-access';
import { DunningsSaveBusinessResult } from '@gc/dunning/models';
import { SaveDunningsResponseApiAdapter } from './save-dunnings-response-api.adapter';
import { BusinessResultErrorMessageApiAdapter } from './business-result-error-message-api.adapter';
import { BusinessResultWarningMessageApiAdapter } from './business-result-warning-message-api.adapter';

export class SaveDunningsResponseBusinessResultAPIApiAdapter {
  static fromApi(
    reponse: SaveDunningsResponseBusinessResultAPIApi
  ): DunningsSaveBusinessResult {
    if (!reponse) {
      throw new Error(
        '[SaveDunningsResponseBusinessResultAPIApiAdapter] reponse should not be null'
      );
    }
    if (!reponse.warnings) {
      throw new Error(
        '[SaveDunningsResponseBusinessResultAPIApiAdapter] warnings should not be null'
      );
    }
    if (!reponse.errors) {
      throw new Error(
        '[SaveDunningsResponseBusinessResultAPIApiAdapter] errors should not be null'
      );
    }
    return {
      result: SaveDunningsResponseApiAdapter.fromApi(reponse.result),
      warnings: BusinessResultWarningMessageApiAdapter.fromApi(
        reponse.warnings
      ),
      errors: BusinessResultErrorMessageApiAdapter.fromApi(reponse.errors),
    };
  }
}
