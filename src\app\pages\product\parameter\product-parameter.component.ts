import { Component, OnInit, inject } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import {
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet,
} from '@angular/router';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { Paths } from '@gc/core/navigation/models';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { take } from 'rxjs';

@Component({
  selector: 'gc-product-parameter',
  templateUrl: './product-parameter.component.html',
  styleUrls: ['./product-parameter.component.scss'],
  standalone: true,
  imports: [
    MainContainerComponent,
    TranslocoModule,
    MatTabsModule,
    RouterLinkActive,
    RouterLink,
    RouterOutlet,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'product-parameter',
      multi: true,
    },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/form', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
  ],
})
export class ProductParameterComponent implements OnInit {
  private readonly _router = inject(Router);

  activeLinkIndex = -1;
  navLinks = [
    {
      label: 'page.navigation-tab.code-structure',
      link: Paths.CODE_STRUCTURE,
    },
    {
      label: 'page.navigation-tab.code-designation',
      link: Paths.CODE_DESIGNATION,
    },
  ];

  ngOnInit(): void {
    this.initializeTabsWithCurrentUrl();
  }

  initializeTabsWithCurrentUrl(): void {
    this._router.events.pipe(take(1)).subscribe(() => {
      const currentUrl = this._router.url.slice(1);
      this.activeLinkIndex = this.navLinks.findIndex(
        (tab) => tab.link === currentUrl
      );
    });
  }
}
