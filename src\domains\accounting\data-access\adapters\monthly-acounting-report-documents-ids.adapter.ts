import { MonthlyAccountingReportDocumentsIds } from '@gc/accounting/models';
import { MonthlyAccountingReportsDocumentsApi } from '@gc/shared/api/data-access';

export class MonthlyAccountingReportDocumentsIdsAdapter {
  static fromApi(
    reports: MonthlyAccountingReportsDocumentsApi
  ): MonthlyAccountingReportDocumentsIds {
    const {
      saleDetailsId,
      categorySalesId,
      unpaidListingId,
      payementListingId,
    } = reports;

    return {
      saleDetailsId: saleDetailsId ?? undefined,
      categorySalesId: categorySalesId ?? undefined,
      unpaidListingId: unpaidListingId ?? undefined,
      payementListingId: payementListingId ?? undefined,
    };
  }
}
