import { inject, Injectable } from '@angular/core';
import {
  ClosedMonthsUseCase,
  ClosureUseCase,
  DestinationCountryCodesUseCase,
  DraftDeclarationsUseCase,
  EuNomenclaturesUseCase,
  LoadDetailsUseCase,
  ParametersUseCase,
  StatisticalProceduresUseCase,
  UncloseMonthUseCase,
} from '../use-cases';
import { DebStore, DebStoreEnum } from '@gc/core/deb/application/store';
import { handleStoreLoading } from '@gc/core/shared/store/operators';
import { AutoStartLoading } from '@gc/core/shared/store/decorators';
import { hasMissingParameters } from '@gc/core/deb/domains/rules';
import {
  DebParameters,
  LoadDetailsParameters,
  MonthToClose,
} from '@gc/core/deb/domains/models';
import { tap } from 'rxjs/operators';
import { map, of, switchMap } from 'rxjs';
import { SnackbarService } from '@gc/shared/ui';

@Injectable()
export class DebFacade {
  private readonly store: DebStore = inject(DebStore);
  private readonly snackbarService = inject(SnackbarService);

  // * use cases
  private readonly getClosedMonths = inject(ClosedMonthsUseCase);
  private readonly parameters = inject(ParametersUseCase);
  private readonly getDraftDeclarations = inject(DraftDeclarationsUseCase);
  private readonly saveClosure = inject(ClosureUseCase);
  private readonly uncloseMonth = inject(UncloseMonthUseCase);
  private readonly loadDetails = inject(LoadDetailsUseCase);
  private readonly euNomenclatures = inject(EuNomenclaturesUseCase);
  private readonly getStatisticalProcedures = inject(
    StatisticalProceduresUseCase
  );
  private readonly getDestinationCountryCodes = inject(
    DestinationCountryCodesUseCase
  );

  getParametersForCurrentCompany() {
    return this.store.get(DebStoreEnum.PARAMETERS);
  }

  @AutoStartLoading(DebStoreEnum.PARAMETERS)
  loadParametersForCompany(companyId: string) {
    this.parameters
      .getFor(companyId)
      .pipe(handleStoreLoading(this.store, DebStoreEnum.PARAMETERS))
      .subscribe();
  }

  getClosedMonthsForCurrentCompany() {
    return this.store.get(DebStoreEnum.CLOSED_MONTHS);
  }

  @AutoStartLoading(DebStoreEnum.CLOSED_MONTHS)
  loadClosedMonthsForCompany(companyId: string) {
    this.getClosedMonths
      .for(companyId)
      .pipe(handleStoreLoading(this.store, DebStoreEnum.CLOSED_MONTHS))
      .subscribe();
  }

  hasMissingParameters(parameters?: DebParameters): boolean {
    if (parameters) {
      return hasMissingParameters(parameters);
    }

    const parametersState = this.store.get(DebStoreEnum.PARAMETERS);
    const currentParameters = parametersState()?.data;
    return hasMissingParameters(currentParameters);
  }

  getDraftDeclarationsForCurrentFilters() {
    return this.store.get(DebStoreEnum.DECLARATIONS);
  }

  @AutoStartLoading(DebStoreEnum.DECLARATIONS)
  loadDraftDeclarationsWithFilters(filters: {
    includeDeliveryNotes?: boolean;
    includeInvoices?: boolean;
    declarationMonth?: Date;
    companyId?: string;
  }) {
    this.getDraftDeclarations
      .with(filters)
      .pipe(handleStoreLoading(this.store, DebStoreEnum.DECLARATIONS))
      .subscribe();
  }

  @AutoStartLoading(DebStoreEnum.CLOSURE)
  saveClosureFor(
    monthToClose: MonthToClose,
    options: { firstDeclaration?: boolean } = { firstDeclaration: false }
  ) {
    this.saveClosure
      .for(monthToClose)
      .pipe(
        handleStoreLoading(this.store, DebStoreEnum.CLOSURE),
        switchMap((closure) => {
          if (options.firstDeclaration) {
            const parameters: DebParameters = {
              ...this.store.get(DebStoreEnum.PARAMETERS)().data,
              declarationNumber: monthToClose.declarationNumber,
            };
            return this.parameters
              .saveFor(parameters, monthToClose.companyId)
              .pipe(handleStoreLoading(this.store, DebStoreEnum.PARAMETERS));
          }

          const filename = closure.filename!;
          if (!filename && !closure.file) {
            return of(null);
          }

          this.downloadXmlFile(closure.file, filename);
          this.snackbarService.success({
            key: 'deb.statement-page.closed-month.file-downloaded',
          });
          return of(closure);
        }),
        tap((_) => {
          this.store.clear(DebStoreEnum.DECLARATIONS);
          this.store.clear(DebStoreEnum.CLOSED_MONTHS);
          this.loadParametersForCompany(monthToClose.companyId);
          this.loadClosedMonthsForCompany(monthToClose.companyId);
        })
      )
      .subscribe();
  }

  @AutoStartLoading(DebStoreEnum.PARAMETERS)
  saveParametersFor(parameters: DebParameters, companyId: string) {
    this.parameters
      .saveFor(parameters, companyId)
      .pipe(
        switchMap((_) => this.parameters.getFor(companyId)),
        handleStoreLoading(this.store, DebStoreEnum.PARAMETERS)
      )
      .subscribe();
  }

  getUncloseMonth() {
    return this.store.get(DebStoreEnum.UNCLOSE_MONTH);
  }

  @AutoStartLoading(DebStoreEnum.UNCLOSE_MONTH)
  uncloseMonthFor(companyId: string, month: Date) {
    const shouldDecreaseDeclarationNumber$ =
      this.shouldDecreaseDeclarationNumber(month);

    this.uncloseMonth
      .for(companyId, month)
      .pipe(
        switchMap(() => this.getClosedMonths.for(companyId)),
        handleStoreLoading(this.store, DebStoreEnum.CLOSED_MONTHS),
        switchMap(() => shouldDecreaseDeclarationNumber$),
        tap({
          next: (shouldDecreaseDeclarationNumber) => {
            this.store.update(DebStoreEnum.UNCLOSE_MONTH, {
              isLoading: false,
              status: 'Success',
            });
            this.store.update(DebStoreEnum.CLOSURE, { status: 'Success' });
            this.store.clear(DebStoreEnum.DECLARATIONS);

            if (!shouldDecreaseDeclarationNumber) {
              return;
            }

            const parameters = this.store.get(DebStoreEnum.PARAMETERS)();
            const updatedParameters = {
              ...parameters.data,
              declarationNumber: parameters.data!.declarationNumber! - 1,
            };
            this.saveParametersFor(updatedParameters, companyId);
          },
          error: (_: unknown) => {
            this.store.update(DebStoreEnum.UNCLOSE_MONTH, {
              isLoading: false,
              status: 'Error',
            });
          },
        })
      )
      .subscribe();
  }

  acknowledgeUncloseMonth() {
    this.store.clear(DebStoreEnum.UNCLOSE_MONTH);
  }

  companyHasChangedTo(companyId: string) {
    // reset
    this.store.clear(DebStoreEnum.PARAMETERS);
    this.store.clear(DebStoreEnum.CLOSED_MONTHS);
    this.store.clear(DebStoreEnum.DECLARATIONS);
    this.store.clear(DebStoreEnum.DETAILS);

    // call uses cases
    this.loadParametersForCompany(companyId);
    this.loadClosedMonthsForCompany(companyId);
  }

  getDetails() {
    return this.store.get(DebStoreEnum.DETAILS);
  }

  @AutoStartLoading(DebStoreEnum.DETAILS)
  loadDetailsFor(parameters: LoadDetailsParameters) {
    this.loadDetails
      .for(parameters)
      .pipe(handleStoreLoading(this.store, DebStoreEnum.DETAILS))
      .subscribe();
  }

  getEuNomenclatures() {
    return this.store.get(DebStoreEnum.NOMENCLATURES);
  }

  @AutoStartLoading(DebStoreEnum.NOMENCLATURES)
  loadEuNomenclatures() {
    this.euNomenclatures
      .get()
      .pipe(handleStoreLoading(this.store, DebStoreEnum.NOMENCLATURES))
      .subscribe();
  }

  getStatisticalProceduresForCurrentCompany() {
    return this.store.get(DebStoreEnum.STATISTICAL_PROCEDURES);
  }

  @AutoStartLoading(DebStoreEnum.STATISTICAL_PROCEDURES)
  loadStatisticalProceduresFor(companyId: string) {
    this.getStatisticalProcedures
      .for(companyId)
      .pipe(handleStoreLoading(this.store, DebStoreEnum.STATISTICAL_PROCEDURES))
      .subscribe();
  }

  getDestinationCountryCodesForCurrentCompanyAndDeclarationMonth() {
    return this.store.get(DebStoreEnum.DESTINATION_COUNTRY_CODES);
  }

  @AutoStartLoading(DebStoreEnum.DESTINATION_COUNTRY_CODES)
  loadDestinationCountryCodes(companyId: string, month: Date) {
    this.getDestinationCountryCodes
      .for(companyId, month)
      .pipe(
        map((countryCodes) => countryCodes.sort()),
        handleStoreLoading(this.store, DebStoreEnum.DESTINATION_COUNTRY_CODES)
      )
      .subscribe();
  }

  getClosure() {
    return this.store.get(DebStoreEnum.CLOSURE);
  }

  private downloadXmlFile(xmlFile: ArrayBuffer, fileName: string): void {
    const blob = new Blob([xmlFile], { type: 'application/xml' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  private shouldDecreaseDeclarationNumber(declarationMonth: Date) {
    const closedMonths = this.store.get(DebStoreEnum.CLOSED_MONTHS)().data!;
    const parameters = this.store.get(DebStoreEnum.PARAMETERS)().data!;

    const currentMonth = closedMonths.find(
      (cm) =>
        new Date(cm.declarationMonth).getMonth() ===
          declarationMonth.getMonth() &&
        new Date(cm.declarationMonth).getFullYear() ===
          declarationMonth.getFullYear()
    )!;

    if (
      currentMonth &&
      currentMonth.declarationNumber <= 1 &&
      parameters.declarationNumber === 1
    ) {
      return of(false);
    }

    const prevMonthDate = new Date(declarationMonth);
    prevMonthDate.setMonth(prevMonthDate.getMonth() - 1);

    const previousMonth = closedMonths.find(
      (cm) =>
        new Date(cm.declarationMonth).getMonth() === prevMonthDate.getMonth() &&
        new Date(cm.declarationMonth).getFullYear() ===
          prevMonthDate.getFullYear()
    );

    const sameDeclarationNumber =
      previousMonth &&
      previousMonth.declarationNumber === currentMonth.declarationNumber &&
      parameters.declarationNumber === currentMonth.declarationNumber;

    return of(!sameDeclarationNumber);
  }
}
