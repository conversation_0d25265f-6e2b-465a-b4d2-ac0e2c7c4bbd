import { Routes } from '@angular/router';
import { Title<PERSON><PERSON>Header, TitleKeyTab } from '@gc/core/navigation/models';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { BusinessReviewComponent } from './business-review.component';

export const ROUTES: Routes = [
  {
    path: '',
    component: BusinessReviewComponent,
    title: TitleKeyTab.BUSINESS_REVIEW,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.BUSINESS_REVIEW)],
  },
];
