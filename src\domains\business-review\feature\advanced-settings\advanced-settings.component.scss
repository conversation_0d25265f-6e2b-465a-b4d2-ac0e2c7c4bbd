.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .master-detail-container {
        flex: 1;
        overflow: hidden;

        mat-selection-list {
            padding: 1rem 0;

            .mat-mdc-list-item {
                border-radius: 5px;
                margin-bottom: 1rem;
            }
        }

        .detail-panel-container {
            height: 100%;
            overflow: hidden;
        }
    }

    .buttons-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 3rem;
        gap: 32px;

        .save-needed-notification-container {
            height: 1.2rem;
            text-align: center;
            width: 100%;
        }
    }
}
