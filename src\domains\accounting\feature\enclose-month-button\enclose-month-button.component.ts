import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Input,
  OnInit,
  inject,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentService,
  selectCompanyIdAndLastEnclosedMonth,
  selectEmptyEditions,
  selectEmptyEditionsLoadingStatus,
  selectEnclosingStatus,
  selectEnclosureMonth,
  selectEnclosureMonthLoadingStatus,
  selectHasEnclosureMonth,
} from '@gc/accounting/data-access';
import {
  MonthlyAccountingReportDocumentsIds,
  RangeFormGroup,
} from '@gc/accounting/models';
import { LoadingStatus } from '@gc/shared/models';
import {
  AUTO_SIZES_DIALOG_CONFIG,
  CapitalizePipe,
  DialogService,
  LoaderComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import {
  TRANSLOCO_SCOPE,
  TranslocoModule,
  TranslocoService,
} from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { EMPTY, filter, map, Observable, switchMap, take, tap } from 'rxjs';
import { DefaultEnclosureMonthSelectionDialogComponent } from '../default-enclosure-month-selection-dialog/default-enclosure-month-selection-dialog.component';
import { delayInProgress } from '@gc/shared/utils';
import { IDocumentViewerManager } from '@isagri-ng/document-viewer';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-enclose-month-button',
  standalone: true,
  templateUrl: './enclose-month-button.component.html',
  styleUrl: './enclose-month-button.component.scss',
  imports: [
    TranslocoModule,
    TranslocoDatePipe,
    CapitalizePipe,
    MatButtonModule,
    LetDirective,
    LoaderComponent,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/form',
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EncloseMonthButtonComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _translocoService = inject(TranslocoService);
  private readonly _dialogService = inject(DialogService);
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);
  private readonly _documentViewerManager = inject(IDocumentViewerManager);
  private readonly _destroyRef = inject(DestroyRef);

  @Input() companyId!: string | undefined;
  @Input() rangeFormGroup!: FormGroup<RangeFormGroup>;

  enclosureMonthLoadingStatus$!: Observable<LoadingStatus>;
  enclosureMonth$!: Observable<Date | null | undefined>;
  showLoader$!: Observable<boolean>;
  showEmptyDataLoader$!: Observable<boolean>;

  hasEnclosureMonth!: boolean | null | undefined;

  ngOnInit(): void {
    this.enclosureMonth$ = this._store.select(selectEnclosureMonth);
    this.enclosureMonthLoadingStatus$ = this._store.select(
      selectEnclosureMonthLoadingStatus
    );
    this.showLoader$ = this.enclosureMonthLoadingStatus$.pipe(
      delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING),
      map((loadingStatus: LoadingStatus) => loadingStatus === 'IN_PROGRESS')
    );
    this.showEmptyDataLoader$ = this._store
      .select(selectEmptyEditionsLoadingStatus)
      .pipe(
        delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING),
        // We need to apply delay, and that is meant to be done in component side
        // eslint-disable-next-line @ngrx/avoid-mapping-selectors
        map((loadingStatus: LoadingStatus) => loadingStatus === 'IN_PROGRESS')
      );

    this.handleEmptyEdition();
    this.handleEnclosingSuccess();
    this.handleDefaultMonthSelection();
  }

  encloseMonth(): void {
    this._store.dispatch(monthlyTreatmentActions.loadHasEmptyEdition());
  }

  handleEmptyEdition(): void {
    this._store
      .select(selectEmptyEditions)
      .pipe(
        filter((emptyEditions) => emptyEditions !== undefined),
        switchMap((emptyEditions) => {
          return this._displayEmptyEditionsPopUp(emptyEditions);
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((confirmation: boolean) => {
        if (confirmation)
          this._store.dispatch(monthlyTreatmentActions.encloseMonth());

        this._store.dispatch(monthlyTreatmentActions.resetEmptyEditions());
      });
  }

  handleEnclosingSuccess(): void {
    this._store
      .select(selectEnclosingStatus)
      .pipe(
        filter((success) => success === 'DONE'),
        switchMap(() =>
          this._translocoService.selectTranslate(
            'accounting.monthly-edition-tab.enclose.success'
          )
        ),
        switchMap((messageConfirm: string) =>
          this._dialogService.confirm(
            messageConfirm,
            'action-button-label.consult',
            'action-button-label.ok',
            'titles.info'
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((hasConfirmed: boolean) => {
        if (hasConfirmed) {
          this._openEditions();
        }
        this._store.dispatch(monthlyTreatmentActions.resetEnclosingStatus());
      });
  }

  handleDefaultMonthSelection(): void {
    this._store
      .select(selectHasEnclosureMonth)
      .pipe(
        tap(
          (hasEnclosureMonth) => (this.hasEnclosureMonth = hasEnclosureMonth)
        ),
        filter((hasEnclosureMonth) => hasEnclosureMonth !== null),
        switchMap((hasEnclosureMonth) =>
          hasEnclosureMonth
            ? EMPTY
            : this._openSelectDefaultEnclosureMonthDialog()
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  onDefaultEncloseClicked(): void {
    this._openSelectDefaultEnclosureMonthDialog()
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe();
  }

  private _openSelectDefaultEnclosureMonthDialog(): Observable<void> {
    return this._dialogService
      .open<
        DefaultEnclosureMonthSelectionDialogComponent,
        void,
        Date
      >(DefaultEnclosureMonthSelectionDialogComponent, void 0, AUTO_SIZES_DIALOG_CONFIG)
      .pipe(
        tap((selectedMonth?: Date) => {
          if (selectedMonth) {
            this._store.dispatch(
              monthlyTreatmentActions.encloseDefaultMonth({
                defaultMonth: selectedMonth,
              })
            );
          }
        }),
        map(() => void 0)
      );
  }
  private _displayEmptyEditionsPopUp(
    emptyEditions?: boolean
  ): Observable<boolean> {
    const translateKey = emptyEditions
      ? 'accounting.monthly-edition-tab.editions.empty-editions'
      : 'accounting.monthly-edition-tab.enclose.confirm';

    return this._translocoService
      .selectTranslate(translateKey)
      .pipe(
        switchMap((messageConfirm: string) =>
          this._dialogService.confirm(messageConfirm)
        )
      );
  }

  private _openEditions(): void {
    this._store
      .select(selectCompanyIdAndLastEnclosedMonth)
      .pipe(
        take(1),
        switchMap(({ companyId, lastEnclosedMonth }) =>
          this._monthlyTreatmentService.getExistingReportsIds(
            companyId!,
            lastEnclosedMonth!
          )
        ),
        filter(Boolean),
        map((ids: MonthlyAccountingReportDocumentsIds) =>
          this._transformDocumentIdsObjectIntoArrayOfIds(ids)
        )
      )
      .subscribe((ids: string[]) => {
        ids.forEach((id) => this._documentViewerManager.open(id));
      });
  }

  private _transformDocumentIdsObjectIntoArrayOfIds(
    ids: MonthlyAccountingReportDocumentsIds
  ): string[] {
    return (Object.keys(ids) as (keyof MonthlyAccountingReportDocumentsIds)[])
      .map((key) => ids[key] as string)
      .filter(Boolean);
  }
}
