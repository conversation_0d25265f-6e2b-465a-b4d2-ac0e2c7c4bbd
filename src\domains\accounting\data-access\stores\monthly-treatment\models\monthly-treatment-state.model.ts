import {
  MonthlyTreatmentRange,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { LoadingStatus, ProcessStatus } from '@gc/shared/models';

export interface MonthlyTreatmentState {
  companyId?: string;
  range?: MonthlyTreatmentRange;
  enclosureMonth?: Date | null;
  hasAlreadyEnclosedMonths?: boolean | null;
  enclosureMonthLoadingStatus: LoadingStatus;
  defaultFiltersLoadingStatus: LoadingStatus;
  salesByProductCurrentFilter?: (ProductCharacteristicEnum | null)[];
  salesDetailsCurrentFilter?: SalesDetailsOrderByEnum;
  emptyEditions?: boolean;
  emptyEditionsLoadingStatus: LoadingStatus;
  enclosingStatus: ProcessStatus;
  lastEnclosedMonth?: Date | null;
  enclosingDefaultStatus: ProcessStatus;
}
