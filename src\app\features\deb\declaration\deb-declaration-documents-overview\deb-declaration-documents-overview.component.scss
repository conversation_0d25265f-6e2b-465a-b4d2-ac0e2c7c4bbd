@use 'sass:map';
@use '@angular/material' as mat;
@use 'gc-material-theme' as gc-material-theme;
@use 'gc-material-typography' as gc-material-typography;

$color-config: mat.m2-get-color-config(gc-material-theme.$theme);
$primary-palette: map.get($color-config, 'primary');

.disabled-row {
  background-color: mat.get-theme-color(
    gc-material-theme.$theme,
    background,
    disabled-list-option
  );
  color: mat.get-theme-color(
    gc-material-theme.$theme,
    foreground,
    disabled-text
  );
}

.disabled-lines-info {
  @include mat.m2-typography-level(
    gc-material-typography.$typography,
    'body-1'
  );
}

.add-line {
  color: mat.m2-get-color-from-palette($primary-palette, 'default');
  background-color: mat.m2-get-color-from-palette(
    $primary-palette,
    'default-contrast'
  );
}

.user-feedback-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

mat-card-content {
  .header {
    margin-top: 10px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;

    .add-line-container {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
    }
  }
}

/**
  * Atomic CSS inspired by TailwindCSS
  * @see https://tailwindcss.com
  */

.h-full {
  height: 100%;

  .actions-container {
    display: flex;
    align-items: center;
    justify-content: space-around;

    gc-action-container {
      cursor: pointer;
    }
  }
}
