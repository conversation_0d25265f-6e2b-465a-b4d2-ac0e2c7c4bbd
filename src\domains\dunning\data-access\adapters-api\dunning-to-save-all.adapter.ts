import {
  DueDateToSaveWithFiltersApi,
  DunningsDataToSaveWithFiltersApi,
} from '@gc/shared/api/data-access';
import {
  DunningsToSaveAll,
  DunningDueDateToSaveWithFilter,
} from '@gc/dunning/models';
import { DateAdapter } from '@gc/shared/utils';
import { DunningLevelAdapter } from '../adapters/dunning-level.adapter';

export class DunningToSaveAllAdapter {
  public static toApi(
    dunningsToSaveAll: DunningsToSaveAll
  ): DunningsDataToSaveWithFiltersApi {
    return {
      dueDateIdsExcluded: dunningsToSaveAll.dueDateIdsExcluded,
      invoicesDueUpTo: DateAdapter.dateToStringAPI(
        dunningsToSaveAll.invoicesDueUpTo
      ) as string,
      enterpriseIds: dunningsToSaveAll.enterpriseIds,
      dueDates: this._toDueDatesApi(dunningsToSaveAll.dueDates),
    };
  }

  private static _toDueDatesApi(
    dueDates: DunningDueDateToSaveWithFilter[] | undefined
  ): DueDateToSaveWithFiltersApi[] {
    return (
      dueDates?.map((dueDate) => ({
        dueDateId: dueDate.id,
        dunningLevel: DunningLevelAdapter.toApi(dueDate.level),
      })) ?? []
    );
  }
}
