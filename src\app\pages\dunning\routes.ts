import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { Paths, TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';

export const ROUTES: Routes = [
  {
    path: '',
    loadChildren: () => import('./dunnings/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.DUNNING,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.DUNNING)],
  },
  {
    path: Paths.PARAMETERS,
    loadChildren: () =>
      import('./dunning-parameters/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.DUNNING_PARAMETERS,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.DUNNING_PARAMETERS)],
  },
  {
    path: Paths.VIEWER,
    loadChildren: () =>
      import('./dunnings-viewer/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.DUNNINGS_VIEWER,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.DUNNINGS_VIEWER)],
  },
];
