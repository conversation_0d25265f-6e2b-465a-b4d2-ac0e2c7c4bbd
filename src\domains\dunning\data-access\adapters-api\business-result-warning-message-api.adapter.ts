import { BusinessResultAPIMessageApi } from '@gc/shared/api/data-access';
import { BusinessResultWarningMessage } from '@gc/dunning/models';

export class BusinessResultWarningMessageApiAdapter {
  static fromApi(
    businessResultAPIMessageApi: BusinessResultAPIMessageApi[]
  ): BusinessResultWarningMessage[] {
    const result: BusinessResultWarningMessage[] = [];
    businessResultAPIMessageApi.forEach((m: BusinessResultAPIMessageApi) =>
      result.push({ code: m.code, message: m.message })
    );
    return result;
  }
}
