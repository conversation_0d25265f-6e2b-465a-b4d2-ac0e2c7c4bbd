import { ShowDeclarationNumberPipe } from './show-declaration-number.pipe';
import { ClosedMonth } from '@gc/core/deb/domains/models';

describe('ShowDeclarationNumberPipe', () => {
  let pipe: ShowDeclarationNumberPipe;

  beforeEach(() => {
    pipe = new ShowDeclarationNumberPipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return "-" when declarationNumber is 0', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-01-01'),
      declarationNumber: 0,
    };
    const dataSource: ClosedMonth[] = [];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('-');
  });

  it('should return the declaration number as string when it is different from both previous and next items', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    };
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 1,
      },
      lineDetails,
      {
        declarationMonth: new Date('2023-03-01'),
        declarationNumber: 3,
      },
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('2');
  });

  it('should return the declaration number as string when it is different from next item', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    };
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 2,
      },
      lineDetails,
      {
        declarationMonth: new Date('2023-03-01'),
        declarationNumber: 3,
      },
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('2');
  });

  it('should return the declaration number as string when it is the last item', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-03-01'),
      declarationNumber: 3,
    };
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 1,
      },
      {
        declarationMonth: new Date('2023-02-01'),
        declarationNumber: 2,
      },
      lineDetails,
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('3');
  });

  it('should return "-" when declaration number is the same as previous and next items', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    };
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 2,
      },
      lineDetails,
      {
        declarationMonth: new Date('2023-03-01'),
        declarationNumber: 2,
      },
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('-');
  });

  it('should return "-" when different from previous but same as next item', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    };
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 1,
      },
      lineDetails,
      {
        declarationMonth: new Date('2023-03-01'),
        declarationNumber: 2,
      },
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('-');
  });

  it('should sort the data source by declaration month before processing', () => {
    const lineDetails: ClosedMonth = {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    };
    // Intentionally unsorted data source
    const dataSource: ClosedMonth[] = [
      {
        declarationMonth: new Date('2023-03-01'),
        declarationNumber: 3,
      },
      lineDetails,
      {
        declarationMonth: new Date('2023-01-01'),
        declarationNumber: 1,
      },
    ];

    const result = pipe.transform(lineDetails, dataSource);
    expect(result).toBe('2');
  });
});
