<ng-container *transloco="let t">
  <h3 class="text-lg">
    {{ t('businessReview.main-settings-tab.company.title') }}
  </h3>

  <p data-testid="company-description">
    {{ t('businessReview.main-settings-tab.company.description') }}
  </p>

  <gc-company-multi-select
    [formControl]="companiesFC"
    [defaultSelectionMode]="companySelectDefaultSelectionMode"
    [label]="t('company.companies-select.label')"
    [selectAllLabel]="t('company.companies-select.selectAll')"
    [invalid]="
      companiesFC.invalid && (companiesFC.dirty || companiesFC.touched)
    "
    [errorText]="t('sharedForm.error.required')" />
  <div data-testid="info-parameters" class="info-parameters-reinitilisation">
    <span>
      {{ t('businessReview.main-settings-tab.company.infoparameters') }}
    </span>
  </div>
</ng-container>
