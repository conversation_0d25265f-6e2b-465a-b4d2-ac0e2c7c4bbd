import { inject, Injectable } from '@angular/core';
import { LoadDraftDeclarationsPort } from '@gc/core/deb/domains/ports';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { GetDebDraftParamRequest } from '@gc/core/deb/infrastructure/api/request';
import { map } from 'rxjs';
import { GuidHelper } from '@isagri-ng/core';
import { LightDebLine } from '@gc/core/deb/models';
import { DraftDeclaration } from '@gc/core/deb/domains/models';

@Injectable()
export class LoadDraftDeclarationsAdapter implements LoadDraftDeclarationsPort {
  private readonly api = inject(DebApiService);

  with(filters: {
    includeDeliveryNotes?: boolean;
    includeInvoices?: boolean;
    declarationMonth?: Date;
    companyId?: string;
  }) {
    const withFilters: GetDebDraftParamRequest = {
      companyId: filters.companyId!,
      deliveryNotesIncluded: filters.includeDeliveryNotes!,
      invoicesIncluded: filters.includeInvoices!,
      month: filters.declarationMonth!,
    };

    return this.api
      .getDraft(withFilters)
      .pipe(map((draft) => this.map<DraftDeclaration[]>(draft.items)));
  }

  private map<TDestination>(drafts: LightDebLine[]) {
    return drafts.map((draft) => {
      const {
        invoicedAmount,
        statisticalProcedureCode,
        destinationCountryCode,
        euNomenclature,
        vatNumber,
      } = draft;
      const id = GuidHelper.newGuid();

      return {
        id,
        destinationCountryCode,
        euNomenclature,
        invoicedAmount,
        statisticalProcedureCode,
        vatNumber,
        state: 'initial',
      };
    }) as TDestination;
  }
}
