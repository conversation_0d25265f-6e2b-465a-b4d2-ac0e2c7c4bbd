import { inject, Injectable } from '@angular/core';
import { DeviceDetectorService } from 'ngx-device-detector';
import { DeviceType } from '../../models/types/device.type';

@Injectable({
  providedIn: 'root',
})
export class DeviceAccessService {
  private readonly deviceService = inject(DeviceDetectorService);
  private readonly deviceType: DeviceType;

  constructor() {
    this.deviceType = this.deviceService.getDeviceInfo()
      .deviceType as DeviceType;
  }

  allowedFor(deviceTypes?: DeviceType[]): boolean {
    if (!deviceTypes?.length) {
      return true;
    }
    if (this.deviceType === 'unknown') {
      return false;
    }
    return deviceTypes?.includes(this.deviceType);
  }
}
