import { Routes } from '@angular/router';
import { checkRequiredStimulsoftQueryParams } from './guards/check-required-stimulsoft-query-params.guard';

import { getTabTitleKeyResolver } from './resolvers/get-tab-title-key.resolver';
import { setStimulsoftHeaderTitleGuard } from './guards/header-stimulsoft-title-key.guard';

export const ROUTES: Routes = [
  {
    path: '',
    canActivate: [
      checkRequiredStimulsoftQueryParams,
      setStimulsoftHeaderTitleGuard,
    ],
    loadChildren: () =>
      import('./stimulsoft-viewer/routes').then((m) => m.ROUTES),
    title: getTabTitleKeyResolver,
  },
];
