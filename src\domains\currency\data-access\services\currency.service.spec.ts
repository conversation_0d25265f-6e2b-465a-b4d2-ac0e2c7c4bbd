import { CurrencyService } from './currency.service';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { CurrencyAdapter } from '../adapters/currency.adapter';
import { BehaviorSubject, Observable, of, skip, take, tap } from 'rxjs';
import { waitForAsync } from '@angular/core/testing';
import { LegacyApiService, CurrencyApi } from '@gc/shared/api/data-access';
import { Currency, LoadingStatus } from '@gc/shared/models';
import { CompanyService } from '@gc/company/data-access';
import { Company } from '@gc/company/models';

describe('CurrencyService', () => {
  let spectator: SpectatorService<CurrencyService>;
  let service: CurrencyService;

  const createService = createServiceFactory({
    service: CurrencyService,
    mocks: [CurrencyAdapter, LegacyApiService, CompanyService],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('getDefaultCurrencyLoadingStatus method', () => {
    it('should return the BehaviorSubject with the current loading status', () => {
      const loadingStatus = service.getDefaultCurrencyLoadingStatus$();

      expect(loadingStatus).toBeInstanceOf(BehaviorSubject);

      expect(loadingStatus.value).toBe('NOT_LOADED');

      (service as any)._defaultCurrencyLoadingStatus$$.next('IN_PROGRESS');
      expect(loadingStatus.value).toBe('IN_PROGRESS');

      (service as any)._defaultCurrencyLoadingStatus$$.next('LOADED');
      expect(loadingStatus.value).toBe('LOADED');

      (service as any)._defaultCurrencyLoadingStatus$$.next('ERROR');
      expect(loadingStatus.value).toBe('ERROR');
    });
  });

  describe('getDefaultCurrency method', () => {
    let loadDefaultCurrencySpy: jest.SpyInstance<void>;

    const fakeCurrency: Currency = { code: 'EUR', symbol: '€' };

    beforeEach(() => {
      loadDefaultCurrencySpy = jest
        .spyOn(service as any, '_loadDefaultCurrency')
        .mockImplementation(() => {
          (
            (service as any)
              ._defaultCurrencyLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          ).next('LOADED');
          (
            (service as any)._defaultCurrency$$ as BehaviorSubject<Currency>
          ).next(fakeCurrency);
        });
    });

    describe('given a state where defaultCurrencyLoadingStatus$$ BehaviorSubject value is NOT_LOADED', () => {
      it('should call loadDefaultCurrency internal method and return a Currency value', waitForAsync(() => {
        spectator.service.getDefaultCurrency$().subscribe((value: Currency) => {
          expect(loadDefaultCurrencySpy).toHaveBeenCalled();
          expect(value).toStrictEqual(fakeCurrency);
        });
      }));
    });

    describe('given a state where defaultCurrencyLoadingStatus$$ BehaviorSubject value is LOADED and defaultCurrency$$ as a currency as value', () => {
      beforeEach(() => {
        (
          (service as any)
            ._defaultCurrencyLoadingStatus$$ as BehaviorSubject<LoadingStatus>
        ).next('LOADED');
        ((service as any)._defaultCurrency$$ as BehaviorSubject<Currency>).next(
          fakeCurrency
        );
      });

      it('should not call loadDefaultCurrency internal method and return a Currency value', waitForAsync(() => {
        spectator.service.getDefaultCurrency$().subscribe((value: Currency) => {
          expect(loadDefaultCurrencySpy).not.toHaveBeenCalled();
          expect(value).toStrictEqual(fakeCurrency);
        });
      }));
    });
  });

  describe('loadDefaultCurrency method', () => {
    let currencyAdapterFromApiSpy: jest.SpyInstance<CurrencyAdapter>;
    let getCurrencySpy: jest.SpyInstance<Observable<Array<CurrencyApi>>>;
    let getCompaniesSpy: jest.SpyInstance<Observable<Company[]>>;

    const fakeCurrencyApi: CurrencyApi = { code: 'EUR', symbol: '€' };
    const fakeCurrenciesApi: CurrencyApi[] = [fakeCurrencyApi];
    const fakeCurrency: Currency = { code: 'EUR', symbol: '€' };
    const fakeCompanyId = '38c8e7cc-3153-4280-b6e4-519810c68c09';
    const fakeCompany: Company = {
      id: fakeCompanyId,
    };

    describe('given a state where CurrenciesApiService returns an ApiCurrency', () => {
      beforeEach(() => {
        const legacyApiService = spectator.inject(LegacyApiService);

        getCurrencySpy = jest.spyOn(
          legacyApiService,
          'currenciesGetCurrencies'
        ) as unknown as jest.SpyInstance<Observable<CurrencyApi[]>>;
        getCurrencySpy.mockReturnValue(of(fakeCurrenciesApi));

        currencyAdapterFromApiSpy = jest
          .spyOn(CurrencyAdapter, 'fromApi')
          .mockReturnValue(fakeCurrency);

        const companyService = spectator.inject(CompanyService);
        getCompaniesSpy = jest
          .spyOn(companyService, 'companies$', 'get')
          .mockReturnValue(of([fakeCompany]));
      });

      it('should call currenciesGetCurrencies method, currencyAdapter fromApi method and getCompanies method of CompanyService and store currency in _defaultCurrency$$ BehaviourSubject', async () => {
        await new Promise<void>((done) => {
          const firstValueOfBehaviorSubject = 1;
          (
            (service as any)
              ._defaultCurrency$$ as BehaviorSubject<Currency | null>
          )
            .pipe(skip(firstValueOfBehaviorSubject))
            .subscribe((currency: Currency | null) => {
              expect(currency).toStrictEqual(fakeCurrency);

              expect(getCompaniesSpy).toHaveBeenCalled();
              expect(getCurrencySpy).toHaveBeenCalledWith(
                fakeCompanyId,
                undefined,
                true
              );
              expect(currencyAdapterFromApiSpy).toHaveBeenCalled();

              done();
            });

          (service as any)._loadDefaultCurrency();
        }).then();
      });

      it('should change status of defaultCurrencyLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to LOADED', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._defaultCurrencyLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'LOADED',
                ]);
                done();
              },
            });

          (service as any)._loadDefaultCurrency();
        }).then();
      });
    });
  });
});
