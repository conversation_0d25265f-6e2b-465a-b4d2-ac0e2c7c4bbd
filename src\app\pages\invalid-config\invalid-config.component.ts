import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NoNavigationContainerComponent } from '@gc/core/navigation/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { CompanyService } from '@gc/company/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Company } from '@gc/company/models';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UserLocalStorageService } from '@gc/user/data-access';
import { Router } from '@angular/router';

@Component({
  standalone: true,
  selector: 'gc-invalid-config',
  templateUrl: './invalid-config.component.html',
  styleUrls: [
    './invalid-config.component.scss',
    './invalid-config-theme.component.scss',
  ],
  imports: [
    NoNavigationContainerComponent,
    TranslocoModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatDatepickerModule,
    CompanySingleSelectComponent,
    MatButtonModule,
    MatInputModule,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'invalid-config',
      multi: true,
    },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
  ],
})
export class InvalidConfigComponent implements OnInit {
  private readonly _companyService = inject(CompanyService);
  private readonly _userLocalStorageService = inject(UserLocalStorageService);
  private readonly _destroyRef = inject(DestroyRef);
  private readonly _fb = inject(FormBuilder);
  private readonly _router = inject(Router);

  companies!: Company[];
  isSingleCompany = true;
  companyIdControl = this._fb.control('', [Validators.required]);
  defaultDateControl = this._fb.control(new Date(), [Validators.required]);

  ngOnInit(): void {
    this._companyService.companies$
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((companies) => {
        if (companies?.length > 1) {
          this.isSingleCompany = false;
        }
        this.companies = companies;
      });
  }

  confirmDefaultConfig(): void {
    this._userLocalStorageService.set({
      defaultCompanyId: this.isSingleCompany
        ? this.companies[0].id
        : this.companyIdControl.value!,
      defaultDate: this.defaultDateControl.value!,
    });
    this._router.navigate(['']);
  }
}
