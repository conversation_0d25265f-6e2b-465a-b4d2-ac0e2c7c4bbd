import { Injectable, inject } from '@angular/core';
import { CompanyApi, LegacyApiService } from '@gc/shared/api/data-access';
import {
  BehaviorSubject,
  Observable,
  filter,
  map,
  switchMap,
  take,
} from 'rxjs';
import { CompanyAdapter } from '../adapters/company.adapter';
import { Company } from '@gc/company/models';
import { LoadingStatus } from '@gc/shared/models';

@Injectable({
  providedIn: 'root',
})
export class CompanyService {
  private readonly _legacyApiService = inject(LegacyApiService);

  private _userCompanies$$ = new BehaviorSubject<Company[] | null>(null);
  private _userCompanies$ = this._userCompanies$$.asObservable();
  private _userCompaniesLoadingStatus$$ = new BehaviorSubject<LoadingStatus>(
    'NOT_LOADED'
  );

  get userCompaniesLoadingStatus$(): Observable<LoadingStatus> {
    return this._userCompaniesLoadingStatus$$.asObservable();
  }

  get companies$(): Observable<Company[]> {
    if (this._userCompaniesLoadingStatus$$.value === 'NOT_LOADED') {
      this._loadCompanies();
    }

    return this.userCompaniesLoadingStatus$.pipe(
      filter((status: LoadingStatus) => status === 'LOADED'),
      switchMap(() => this._userCompanies$),
      filter(Boolean)
    );
  }

  get companiesIds$(): Observable<string[]> {
    return this.companies$.pipe(
      map((companies) => companies.map(({ id }) => id))
    );
  }

  get isSingleCompany$(): Observable<boolean> {
    return this.companies$.pipe(map((companies) => companies.length === 1));
  }

  get companyIdIfSingleCompany$(): Observable<string | undefined> {
    return this.companiesIds$.pipe(
      map((companiesIds) =>
        companiesIds.length === 1 ? companiesIds[0] : undefined
      )
    );
  }

  private _loadCompanies(): void {
    this._userCompaniesLoadingStatus$$.next('IN_PROGRESS');

    this._legacyApiService
      .companyGetCompanies1()
      .pipe(
        take(1),
        map((companiesApi: CompanyApi[]) =>
          companiesApi.map((companyApi: CompanyApi) =>
            CompanyAdapter.fromApi(companyApi)
          )
        )
      )
      .subscribe({
        next: (companies: Company[]) => {
          this._userCompanies$$.next(companies);
          this._userCompaniesLoadingStatus$$.next('LOADED');
        },
        error: () => {
          this._userCompaniesLoadingStatus$$.next('ERROR');
        },
      });
  }
}
