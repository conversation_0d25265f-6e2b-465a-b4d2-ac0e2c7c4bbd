# Performances

## Taille du bundle

L'un des points à prendre en compte pour la perfomance d'une application web est son temps de chargement et notamment le temps de chargement des différents fichiers js (chunck) qui la composent.

Les 1ers fichiers (chunks) chargés lors de l'ouverture d'une application angular sont :

-   main.xxx.js
-   runtime.xxx.js
-   poyfills.xxx.js
-   styles.xxx.js

Le fichier qui nous intéresse particulièrement est le main.xxx.js qui contient le code applicatif.

> :bulb: L'objectif est donc de limiter le plus possible la taille du main.xxx.js pour améliorer la vitesse de chargement initial de l'application web

### Visualiser la taille du bundle

#### Via la commande ng build

Au moment de l'exécution de la commande `ng build`, le resultat affiche la taille des différents fichiers (chunks) générés et leur taille

#### Via l'outil webpack bundle analyzer

L'outil webpack bundle analyzer permet de visualiser plus en détail les différents chunck contenus dans le bundle final.

Pour cela, il suivre les étapes suivantes :

-   lancer la commande `npm run build:stats` (génère le bundle avec les configurations de production et un fichier stats.json)
-   lancer la commande `npm run analyze` (lance un server web et le resultat du fichier stats.json sous forme d'une page web)
-   aller sur le navigateur (sur la page http://127.0.0.1:8888)

On peut alors visualiser les différents modules js présents dans notre application ainsi que ceux des dépendances (librairies tièrces)

> :bulb: Sur la page web nous pouvons visualiser la taille totale du bundle et celle de chaque chunk. Pour cela, cliquer sur la flèche en haut à gauche.

> :bulb: A noter que les tailles qui nous intéressent sont les suivantes :
>
> -   Parsed : taille des fichiers (chunck) générés
> -   Gzipped : estimation taille des fichiers générés après avoir été compressés.

### Améliorer les performances : comment réduire la taille du chunck main.xxx.js ?

Afin de réduire la taille du chunck principal (main.xxx.js), on peut :

-   Mettre en place le lazy-loading des modules de feature : cela permet de découper le main.xxx.js en plusieurs chunck qui sont chargés dynamiquement lors de la navigation sur l'application et de ne charger que le minimum au lancement de l'application.
-   Regarder la taille des modules des librairies tièrces : une taille excessive d'un module peut nous pousser à le remplacer (si cela est possible) ou d'essayer d'importer seulement une partie de celui-ci.
-   Redécouper la taille de nos modules si certains sont volumineux.

> :bulb: A noter que la configuration d'Angular (dans angular.json) permet de définir un "budget", c'est à dire une taille de bundle maximum autorisée (au delà de ça une erreur est générée au build)
