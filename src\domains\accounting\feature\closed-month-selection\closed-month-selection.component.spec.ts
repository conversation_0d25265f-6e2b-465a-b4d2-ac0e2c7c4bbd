import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { ClosedMonthSelectionComponent } from './closed-month-selection.component';
import { MockComponents, MockDirectives, MockModule } from 'ng-mocks';
import { Store } from '@ngrx/store';
import { CompanyService } from '@gc/company/data-access';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import {
  MonthlyTreatmentHistoryStoreModule,
  MonthlyTreatmentStoreModule,
} from '@gc/accounting/data-access';
import { TranslocoDirective } from '@jsverse/transloco';
import { Observable, of } from 'rxjs';
import { waitForAsync } from '@angular/core/testing';
import { Validators } from '@angular/forms';

describe('ClosedMonthSelectionComponent', () => {
  let spectator: Spectator<ClosedMonthSelectionComponent>;
  let component: ClosedMonthSelectionComponent;

  const createComponent = createComponentFactory({
    component: ClosedMonthSelectionComponent,
    declarations: [
      MockComponents(CompanySingleSelectComponent),
      MockDirectives(TranslocoDirective),
      MockModule(MonthlyTreatmentStoreModule),
      MockModule(MonthlyTreatmentHistoryStoreModule),
    ],
    mocks: [Store, CompanyService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call all handle methods', () => {
      const handleSelectedCompanyIdSpy = jest
        .spyOn(component, 'handleSelectedCompanyId')
        .mockImplementation();
      const handleYearsRetrievalSpy = jest
        .spyOn(component, 'handleYearsRetrieval')
        .mockImplementation();
      const handleMonthsRetrievalSpy = jest
        .spyOn(component, 'handleMonthsRetrieval')
        .mockImplementation();

      component.ngOnInit();

      expect(handleSelectedCompanyIdSpy).toHaveBeenCalled();
      expect(handleYearsRetrievalSpy).toHaveBeenCalled();
      expect(handleMonthsRetrievalSpy).toHaveBeenCalled();
    });
  });

  describe('handleSelectedCompanyId', () => {
    let getCompanyIdIfSingleCompanySpy: jest.SpyInstance<
      Observable<string | undefined>
    >;
    let initSelectCompanyFormControlSpy: jest.SpyInstance<Observable<void>>;

    beforeEach(() => {
      const companyService = spectator.inject(CompanyService);

      getCompanyIdIfSingleCompanySpy = jest.spyOn(
        companyService,
        'companyIdIfSingleCompany$',
        'get'
      );

      initSelectCompanyFormControlSpy = jest
        .spyOn(component as any, '_initSelectCompanyFormControl')
        .mockReturnValue(of(void 0));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return a company', () => {
      const companyId = 'companyId';
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should not call _initSelectCompanyFormControl', waitForAsync(() => {
        component.handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(initSelectCompanyFormControlSpy).not.toHaveBeenCalled();
      }));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return an undefined company', () => {
      const companyId = undefined;
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should call initSelectCompanyFormControl', waitForAsync(() => {
        component.handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(initSelectCompanyFormControlSpy).toHaveBeenCalled();
      }));
    });
  });

  describe('initSelectCompanyFormControl', () => {
    describe('given a state where selectMonthlyTreatmentCompanyId return a company id', () => {
      const companyId = 'companyId';

      beforeEach(() => {
        const store = spectator.inject(Store);
        jest.spyOn(store, 'select').mockReturnValue(of(companyId));
      });

      it('should init selectCompanyFC FormControl', waitForAsync(() => {
        (component as any)._initSelectCompanyFormControl().subscribe(() => {
          expect(component.selectCompanyFC?.value).toBe(companyId);
          expect(
            component.selectCompanyFC?.hasValidator(Validators.required)
          ).toBe(true);
        });
      }));
    });

    describe('given a state where selectMonthlyTreatmentCompanyId return an undefined company id', () => {
      const companyId = undefined;

      beforeEach(() => {
        const store = spectator.inject(Store);
        jest.spyOn(store, 'select').mockReturnValue(of(companyId));
      });

      it('should init selectCompanyFC FormControl', waitForAsync(() => {
        (component as any)._initSelectCompanyFormControl().subscribe(() => {
          expect(component.selectCompanyFC?.value).toBe('');
          expect(
            component.selectCompanyFC?.hasValidator(Validators.required)
          ).toBe(true);
        });
      }));
    });
  });
});
