import { SaveDunningsResponseApi } from '@gc/shared/api/data-access';
import {
  DunningsSaveResult,
  DunningSaveDueDateSuccess,
  DunningSaveDueDateFail,
} from '@gc/dunning/models';
import { DunningSaveDueDateSuccessApiAdapter } from './dunning-save-due-date-success-api.adapter';
import { DunningSaveDueDateFailApiAdapter } from './dunning-save-due-date-fail-api.adapter';

export class SaveDunningsResponseApiAdapter {
  static fromApi(
    dunningSaveResultApi: SaveDunningsResponseApi | undefined
  ): DunningsSaveResult | undefined {
    if (!dunningSaveResultApi) {
      return undefined;
    }
    let dueDateSucessResult: DunningSaveDueDateSuccess[] | undefined =
      undefined;
    let dueDateFailsResult: DunningSaveDueDateFail[] | undefined = undefined;
    let dunningTreatmentId: string | undefined = undefined;
    if (dunningSaveResultApi.dueDateSuccess) {
      dueDateSucessResult = DunningSaveDueDateSuccessApiAdapter.fromApi(
        dunningSaveResultApi.dueDateSuccess
      );
    }
    if (dunningSaveResultApi.dueDateFails) {
      dueDateFailsResult = DunningSaveDueDateFailApiAdapter.fromApi(
        dunningSaveResultApi.dueDateFails
      );
    }
    if (dunningSaveResultApi.dunningTreatmentId) {
      ({ dunningTreatmentId } = dunningSaveResultApi);
    }
    return {
      dueDateSuccess: dueDateSucessResult,
      dueDateFails: dueDateFailsResult,
      dunningTreatmentId: dunningTreatmentId,
    };
  }
}
