import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { CommissionsComponent } from './commissions.component';
import { MockComponents, MockDirectives, MockPipes } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { UpperCasePipe } from '@angular/common';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { FiltersComponent } from './filters/filters.component';
import { PayoutTableComponent } from './payout-table/payout-table.component';
import { PayoutSummaryFooterComponent } from './payout-summary-footer/payout-summary-footer.component';
import { CurrencyService } from '@gc/currency/data-access';

describe('CommissionsComponent', () => {
  let spectator: Spectator<CommissionsComponent>;
  let component: CommissionsComponent;
  const createComponent = createComponentFactory({
    component: CommissionsComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(
        MainContainerComponent,
        FiltersComponent,
        PayoutTableComponent,
        PayoutSummaryFooterComponent
      ),
      MockPipes(UpperCasePipe),
    ],
    mocks: [CurrencyService],
    imports: [MatTabsModule, MatButtonModule],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('setAmountToPay', () => {
    it('should set amountToPay to the provided number', () => {
      component.setAmountToPay(100);
      expect(component.amountToPay).toBe(100);
    });

    it('should set amountToPay to null when null is provided', () => {
      component.setAmountToPay(null);
      expect(component.amountToPay).toBeNull();
    });

    it('should update amountToPay when called multiple times', () => {
      component.setAmountToPay(100);
      expect(component.amountToPay).toBe(100);
      component.setAmountToPay(200);
      expect(component.amountToPay).toBe(200);
    });
  });
});
