import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  input,
  OnInit,
  output,
  Signal,
  ViewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTable, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  CapitalizePipe,
  ConfirmActionComponent,
  LoaderComponent,
} from '@gc/shared/ui';
import { ShowDeclarationNumberPipe } from './pipes/show-declaration-number.pipe';
import { TranslocoDirective } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { LetDirective } from '@ngrx/component';
import { DebFacade } from '@gc/core/deb/application/facades';
import { ResourceState } from '@gc/core/shared/store';
import { ClosedMonth } from '@gc/core/deb/domains/models';
import { provideDebInfrastructure } from '@gc/core/deb/infrastructure';
import { provideDebServices } from '@gc/core/deb';

@Component({
  selector: 'gc-deb-history-details',
  standalone: true,
  imports: [
    MatTableModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatTooltipModule,
    LetDirective,
    TranslocoDirective,
    TranslocoDatePipe,
    CapitalizePipe,
    LoaderComponent,
    ConfirmActionComponent,
    ShowDeclarationNumberPipe,
  ],
  providers: [provideDebInfrastructure(), provideDebServices()],
  templateUrl: './deb-history-details.component.html',
  styleUrls: ['./deb-history-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebHistoryDetailsComponent implements OnInit {
  private readonly debFacade = inject(DebFacade);
  private readonly injector = inject(Injector);
  readonly closedMonths: Signal<ResourceState<ClosedMonth[]>>;
  companyId = input.required<string>();
  lastClosedMonthUpdated = output();

  @ViewChild(MatTable) table!: MatTable<ClosedMonth>;

  displayUncloseActionButtons = false;
  displayedColumns: string[] = ['declarationNumber', 'closeMonth', 'action'];
  uncloseMonthState: Signal<ResourceState<void>>;

  constructor() {
    this.closedMonths = this.debFacade.getClosedMonthsForCurrentCompany();
    this.uncloseMonthState = this.debFacade.getUncloseMonth();

    effect(
      () => {
        const uncloseMonthState = this.uncloseMonthState();
        if (uncloseMonthState.status === 'Success') {
          this.debFacade.acknowledgeUncloseMonth();
          this.lastClosedMonthUpdated.emit();
        }
      },
      { allowSignalWrites: true }
    );
  }

  ngOnInit() {
    effect(
      () => {
        this.closedMonths();
        this.table?.renderRows();
      },
      { injector: this.injector }
    );
  }

  handleUncloseActions(confirm: boolean, declarationMonth: ClosedMonth) {
    setTimeout(() => {
      this.displayUncloseActionButtons = false;
    });

    if (!confirm) {
      return;
    }

    this.debFacade.uncloseMonthFor(
      this.companyId(),
      declarationMonth.declarationMonth
    );
  }
}
