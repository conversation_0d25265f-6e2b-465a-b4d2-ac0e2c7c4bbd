import { TranslocoService } from '@jsverse/transloco';
import { createPipeFactory, SpectatorPipe } from '@ngneat/spectator/jest';
import { FormatMissingFieldsPipe } from './format-missing-fields.pipe';

describe('FormatMissingFieldsPipe', () => {
  let spectator: SpectatorPipe<FormatMissingFieldsPipe>;
  let pipe: FormatMissingFieldsPipe;
  let translocoService: TranslocoService;

  const createPipe = createPipeFactory({
    pipe: FormatMissingFieldsPipe,
    mocks: [TranslocoService],
  });

  beforeEach(() => {
    spectator = createPipe();
    translocoService = spectator.inject(TranslocoService);
    pipe = new FormatMissingFieldsPipe(translocoService);
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return null when missingFields is undefined', () => {
    const result = pipe.transform(undefined);
    expect(result).toBeNull();
  });

  it('should return null when missingFields is null', () => {
    const result = pipe.transform(null);
    expect(result).toBeNull();
  });

  it('should return null when missingFields is an empty array', () => {
    const result = pipe.transform([]);
    expect(result).toBeNull();
  });

  describe('when there are missing fields', () => {
    beforeEach(() => {
      (translocoService.translate as jest.Mock).mockReturnValue(
        'Missing fields'
      );
    });

    it('should format message with one missing field', () => {
      const missingFields = ['Field1'];

      const result = pipe.transform(missingFields);

      expect(result).toBe('Missing fields :\n- Field1');
      expect(translocoService.translate).toHaveBeenCalledWith(
        'deb.deb-declaration-table-component.errors.missing-fields'
      );
    });

    it('should format message with multiple missing fields', () => {
      const missingFields = ['Field1', 'Field2', 'Field3'];

      const result = pipe.transform(missingFields);

      expect(result).toBe('Missing fields :\n- Field1\n- Field2\n- Field3');
      expect(translocoService.translate).toHaveBeenCalledWith(
        'deb.deb-declaration-table-component.errors.missing-fields'
      );
    });
  });
});
