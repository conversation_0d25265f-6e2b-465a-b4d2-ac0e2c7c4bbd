import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { NoNavigationContainerComponent } from '@gc/core/navigation/feature';
import { StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { StimulsoftPrimaryTypesAdapter } from '@gc/shared/stimulsoft/data-access';
import {
  StimulsoftNavigationService,
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
  REPORT_ID,
  ReportFamilies,
  ReportId,
  StimulsoftPropertyKey,
  StimulsoftSupplementaryProperties,
} from '@gc/shared/stimulsoft/models';
import { PushPipe } from '@ngrx/component';
import { map, Observable, of, switchMap, throwError } from 'rxjs';

type StimulsoftViewerProperties = CoreStimulsoftProperties &
  CommonStimulsoftProperties &
  StimulsoftSupplementaryProperties;

type ReportIdReportFamiliesCombination = `${ReportId}-${ReportFamilies}`;

const STIMULSOFT_PROPERTIES_QUERY_KEYS_FROM_FAMILY_AND_ID = new Map<
  ReportIdReportFamiliesCombination,
  StimulsoftQueryParamsKeys[]
>([
  [
    `${REPORT_ID.sales}-${ReportFamilies.SALES}`,
    [StimulsoftQueryParamsKeys.TREATMENT_ID],
  ],
  [
    `${REPORT_ID.details}-${ReportFamilies.DETAILS}`,
    [StimulsoftQueryParamsKeys.TREATMENT_ID],
  ],
  [
    `${REPORT_ID.debt}-${ReportFamilies.DEBT}`,
    [StimulsoftQueryParamsKeys.COMPANY_ID, StimulsoftQueryParamsKeys.END_DATE],
  ],
  [
    `${REPORT_ID.receipts}-${ReportFamilies.RECEIPTS}`,
    [StimulsoftQueryParamsKeys.COMPANY_ID, StimulsoftQueryParamsKeys.END_DATE],
  ],
  [
    `${REPORT_ID.depositSlip}-${ReportFamilies.DEPOSIT_SLIP}`,
    [StimulsoftQueryParamsKeys.TREATMENT_ID],
  ],
  [
    `${REPORT_ID.payment}-${ReportFamilies.PAYMENT}`,
    [StimulsoftQueryParamsKeys.TREATMENT_ID],
  ],
]);

const STIMULSOFT_PROPERTIES_KEYS_FROM_QUERY_PARAMS_KEYS = new Map<
  StimulsoftQueryParamsKeys,
  StimulsoftPropertyKey
>([
  [StimulsoftQueryParamsKeys.TREATMENT_ID, StimulsoftPropertyKey.TREATMENT_ID],
  [StimulsoftQueryParamsKeys.COMPANY_ID, StimulsoftPropertyKey.COMPANY_ID],
  [StimulsoftQueryParamsKeys.END_DATE, StimulsoftPropertyKey.END_DATE],
]);

@Component({
  selector: 'gc-stimulsoft-viewer',
  templateUrl: './stimulsoft-viewer.component.html',
  styleUrls: ['./stimulsoft-viewer.component.scss'],
  standalone: true,
  imports: [
    NoNavigationContainerComponent,
    StimulsoftViewerContainerComponent,
    PushPipe,
  ],
})
export class StimulsoftViewerComponent implements OnInit {
  private readonly _stimulsoftPropertiesService = inject(
    StimulsoftPropertiesService
  );
  private readonly _stimulsoftNavigationService = inject(
    StimulsoftNavigationService
  );
  private readonly _activatedRoute = inject(ActivatedRoute);

  stimulsoftProperties$!: Observable<StimulsoftViewerProperties>;

  ngOnInit(): void {
    this.stimulsoftProperties$ = this._stimulsoftNavigationService
      .getCommonStimulsoftPropertiesFromQueryParams$()
      .pipe(
        switchMap((commonStimulsoftProperties) =>
          this._getSupplementaryPropertiesQueryParamKeys(
            commonStimulsoftProperties.reportId as ReportId,
            commonStimulsoftProperties.reportFamily as ReportFamilies
          ).pipe(
            switchMap((supplementaryPropertiesQueryParamKeys) =>
              this._getSupplementaryPropertiesFromQueryParams$(
                supplementaryPropertiesQueryParamKeys
              )
            ),
            switchMap((supplementaryProperties) =>
              this._stimulsoftPropertiesService.createStimulsoftProperties$<StimulsoftSupplementaryProperties>(
                commonStimulsoftProperties.reportId,
                commonStimulsoftProperties.reportFamily,
                { ...supplementaryProperties }
              )
            )
          )
        )
      );
  }

  private _getSupplementaryPropertiesQueryParamKeys(
    reportId: ReportId,
    reportFamily: ReportFamilies
  ): Observable<StimulsoftQueryParamsKeys[]> {
    const supplementaryPropertiesQueryParamKeys:
      | StimulsoftQueryParamsKeys[]
      | undefined = STIMULSOFT_PROPERTIES_QUERY_KEYS_FROM_FAMILY_AND_ID.get(
      `${reportId}-${reportFamily}`
    );

    if (!supplementaryPropertiesQueryParamKeys) {
      return throwError(
        () =>
          new Error(
            `Stimulsoft supplementary properties query param keys are not defined for ${reportId}-${reportFamily}`
          )
      );
    }

    return of(supplementaryPropertiesQueryParamKeys);
  }

  private _getSupplementaryPropertiesFromQueryParams$(
    supplementaryPropertiesQueryParamKeys: StimulsoftQueryParamsKeys[]
  ): Observable<StimulsoftSupplementaryProperties> {
    return this._activatedRoute.queryParams.pipe(
      map((queryParams: Params) => {
        return supplementaryPropertiesQueryParamKeys.reduce<StimulsoftSupplementaryProperties>(
          (
            supplementaryProperties: StimulsoftSupplementaryProperties,
            queryParamKey: StimulsoftQueryParamsKeys
          ) => {
            const value = queryParams[queryParamKey] as string;
            const stimulsoftSupplementaryPropertyKey =
              STIMULSOFT_PROPERTIES_KEYS_FROM_QUERY_PARAMS_KEYS.get(
                queryParamKey
              )!;

            supplementaryProperties[stimulsoftSupplementaryPropertyKey] =
              StimulsoftPrimaryTypesAdapter.fromQueryParamValue(value);
            return supplementaryProperties;
          },
          {} as StimulsoftSupplementaryProperties
        );
      })
    );
  }
}
