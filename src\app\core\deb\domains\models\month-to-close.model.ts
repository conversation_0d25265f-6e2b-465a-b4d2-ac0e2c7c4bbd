import { SimplifiedDeclaration } from './simplified-declaration.model';
import { DeclarationType } from '@gc/core/deb/domains/models/declaration-type.enum';

export interface MonthToClose {
  archiveOnly?: boolean;
  companyId: string;
  declarationNumber: number;
  declarationType: DeclarationType.LIGHT;
  declarations?: SimplifiedDeclaration[];
  deliveryNotesIncluded: boolean;
  invoicesIncluded: boolean;
  month: Date;
}
