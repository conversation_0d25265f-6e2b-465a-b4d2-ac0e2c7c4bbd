<mat-card class="h-full" *transloco="let t">
  <mat-card-header>
    <mat-card-title>
      {{ t('deb.statement-page.parameters.title') }}
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <p>
      <strong>{{ t('deb.statement-page.parameters.declarantName') }}</strong>
      :
      {{ parameters()?.declarantName }}
    </p>
    <p>
      <strong>{{
        t('deb.statement-page.parameters.authorizationNumber')
      }}</strong>
      : {{ parameters()?.authorizationNumber }}
    </p>
    <p>
      <strong>{{
        t('deb.statement-page.parameters.declarationNumber')
      }}</strong>
      : {{ parameters()?.declarationNumber }}
    </p>
    <p>
      <strong>{{ t('deb.statement-page.parameters.declarationType') }}</strong>
      :

      @switch (parameters()?.declarationType) {
        @case ('light') {
          {{ t('deb.declaration-type.light') }}
        }
        @case ('full') {
          {{ t('deb.declaration-type.full') }}
        }
      }
    </p>
  </mat-card-content>
</mat-card>
