# Gc api services and models generation guide

Follow those steps one by one :

- Go to isagri stoplight online : <https://isagri-france.stoplight.io/docs/gc-net-openapi-sans-git/wqly6pkfuijqv-gc-gestion-de-la-deb>

- Select the domain you need (deb, legacy ...)

👉 on the left part :
![alt text](docs-images/stoplight.png)

- Then click on export (on top right) and click on "Bundled Reference"
  
👉 At this point, you get the openapi file (json) corresponding to the description of the api

⚠️ Make sure the original file name is not modified (i.e "IS-GC_DEB_V1" **and not** "IS-GC_DEB_V1 (1)" or something)

- Downlown all the openapi file you need to generate

- Then copy/paste them into gc.webapp project under /api-generate/openapi_files_last_version

- Then open terminal at the root of gc.webapp and run : **npm run openapi:generate**
  
👉 At this point the code has been generated under /shared/api/data-access

- **If you add a new domain services/models, you need to follow the next steps :**

  - Go under shared/api/data-access/IS-GC_THE-NEW-DOMAIN_VZ :
    - remove index.ts (there is no way not generating it, and we expose services and models through the parent shared/api/data-access/index.ts directly)
    - add index.ts to .openapi-generator-ignore file (for the generetor not to generate it during the next generation)

- All done ! You can commit and push 🚀
