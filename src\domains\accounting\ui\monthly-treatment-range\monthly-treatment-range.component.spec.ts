import { FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { RangeFormGroup } from '@gc/accounting/models';
import * as validators from '@gc/shared/form/utils';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import {
  TranslocoModule,
  TranslocoService,
  TranslocoTestingModule,
} from '@jsverse/transloco';
import { MonthlyTreatmentRangeComponent } from './monthly-treatment-range.component';
import { SimpleChange, SimpleChanges } from '@angular/core';

describe('MonthlyTreatmentRangeComponent', () => {
  let spectator: Spectator<MonthlyTreatmentRangeComponent>;
  let component: MonthlyTreatmentRangeComponent;

  const rangeFormGroup: FormGroup<RangeFormGroup> =
    new FormGroup<RangeFormGroup>({
      start: new FormControl<Date>(new Date(), { nonNullable: true }),
      end: new FormControl<Date>(new Date(), { nonNullable: true }),
    });

  const createComponent = createComponentFactory({
    component: MonthlyTreatmentRangeComponent,
    providers: [TranslocoTestingModule, TranslocoModule],
    mocks: [TranslocoService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent({
      props: {
        rangeFormGroup,
      },
    });
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    let differentMonthOrYearValidatorSpy: jest.SpyInstance<ValidatorFn>;
    let startDateAfterEndDateValidatorSpy: jest.SpyInstance<ValidatorFn>;

    beforeEach(() => {
      differentMonthOrYearValidatorSpy = jest.spyOn(
        validators,
        'differentMonthOrYearValidator'
      );
      startDateAfterEndDateValidatorSpy = jest.spyOn(
        validators,
        'startDateAfterEndDateValidator'
      );
    });
    it('should add differentMonthOrYearValidator and startDateAfterEndDateValidator validators to range FormGroup controls', () => {
      const changes: SimpleChanges = {
        rangeFormGroup: new SimpleChange(undefined, rangeFormGroup, true),
      };
      component.ngOnChanges(changes);

      expect(differentMonthOrYearValidatorSpy).toHaveBeenCalled();
      expect(startDateAfterEndDateValidatorSpy).toHaveBeenCalled();
    });
  });
});
