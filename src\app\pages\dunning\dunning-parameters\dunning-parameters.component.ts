import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  HostListener,
  OnInit,
  inject,
} from '@angular/core';
import {
  DunningParametersStoreModule,
  DunningStoreModule,
  selectCurrentDunningLetter,
  selectCurrentLetterHasUnsavedChanges,
} from '@gc/dunning/data-access';
import { DunningLetter } from '@gc/dunning/models';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { DunningLevelFromEnumPipe } from '@gc/dunning/ui';
import {
  DunningLetterEditComponent,
  DunningLetterEditFilterComponent,
} from '@gc/dunning/feature';
import { MasterDetailPanelsLayoutComponent } from '@gc/shared/ui';
import { LetDirective } from '@ngrx/component';
import { NavigationBackContainerComponent } from '@gc/core/navigation/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-dunning-parameters',
  templateUrl: './dunning-parameters.component.html',
  styleUrls: ['./dunning-parameters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    TranslocoModule,
    NavigationBackContainerComponent,
    LetDirective,
    MasterDetailPanelsLayoutComponent,
    DunningLetterEditFilterComponent,
    DunningLetterEditComponent,
    DunningLevelFromEnumPipe,
    DunningParametersStoreModule,
    DunningStoreModule,
  ],
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'dunning', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
  ],
})
export class DunningParametersComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  currentDunningLetter$: Observable<DunningLetter | undefined> =
    this._store.select(selectCurrentDunningLetter);

  currentLetterHasUnsavedChanges$ = this._store.select(
    selectCurrentLetterHasUnsavedChanges
  );

  showBrowserNotificationIfUnsavedChanges: boolean | undefined;

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.showBrowserNotificationIfUnsavedChanges) {
      event.returnValue = this.showBrowserNotificationIfUnsavedChanges;
    }
  }

  ngOnInit(): void {
    this.currentLetterHasUnsavedChanges$
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((haveChanges: boolean | undefined) => {
        this.showBrowserNotificationIfUnsavedChanges = haveChanges;
      });
  }
}
