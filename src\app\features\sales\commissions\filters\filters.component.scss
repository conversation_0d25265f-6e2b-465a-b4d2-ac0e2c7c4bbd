.filter-container {
    display: flex;
    flex-direction: column;

    .input-container {
        display: flex;
        justify-content: space-between;

        .left-container {
            display: flex;
            gap: 20px;

            gc-company-single-select,
            mat-form-field {
                display: flex;
                flex-direction: column;
                margin-bottom: 21.778px;
                width: fit-content;

                &:has(mat-error) {
                    margin-bottom: 0;
                }
            }
        }
    }
}
