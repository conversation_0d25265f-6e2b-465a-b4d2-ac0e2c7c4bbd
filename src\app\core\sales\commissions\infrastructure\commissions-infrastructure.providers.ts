import { Provider } from '@angular/core';
import { CommissionsApiService } from './api';
import {
  LoadCommissionDetailsPort,
  LoadCommissionsPort,
} from '../domains/ports';
import {
  LoadCommissionDetailsAdapter,
  LoadCommissionsAdapter,
  UpdateCommissionAdapter,
} from './adapter';
import { UpdateCommissionPort } from '../domains/ports/update-commission.port';

export function commissionsInfrastructureProviders(): Provider[] {
  return [
    CommissionsApiService,
    {
      provide: LoadCommissionsPort,
      useClass: LoadCommissionsAdapter,
    },
    {
      provide: LoadCommissionDetailsPort,
      useClass: LoadCommissionDetailsAdapter,
    },
    {
      provide: UpdateCommissionPort,
      useClass: UpdateCommissionAdapter,
    },
  ];
}
