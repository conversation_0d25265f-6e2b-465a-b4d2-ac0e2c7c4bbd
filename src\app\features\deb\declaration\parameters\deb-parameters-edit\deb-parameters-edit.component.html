<ng-container *transloco="let t">
  <div mat-dialog-title>
    {{ t('deb.statement-page.parameters.titleShort') }}*
  </div>
  <mat-dialog-content>
    <form [formGroup]="parametersFG">
      <div>
        <span>
          {{ t('deb.statement-page.parameters.declarationType') }}
        </span>
        <mat-radio-group color="primary" formControlName="declarationType">
          <mat-radio-button value="light">
            {{ t('deb.declaration-type.light') }}
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <div class="input-fields-container">
        <mat-form-field appearance="outline">
          <mat-label>
            {{ t('deb.statement-page.parameters.declarantName') }}
          </mat-label>
          <input
            matInput
            formControlName="declarantName"
            name="declarantName"
            [placeholder]="
              t('deb.parameters-dialog.placeholders.declarantName')
            " />
          <mat-hint align="end">
            {{ parametersFG.get('declarantName')?.value?.length }} /
            {{ declarantNameMaxLength }}
          </mat-hint>
          @if (parametersFG.controls.declarantName.hasError('maxlength')) {
            <mat-error>
              {{
                t('deb.parameters-dialog.errors.max-length', {
                  maxlength: declarantNameMaxLength,
                })
              }}
            </mat-error>
          }
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>
            {{ t('deb.statement-page.parameters.authorizationNumber') }}
          </mat-label>
          <input
            matInput
            formControlName="authorizationNumber"
            name="authorizationNumber" />
          <mat-hint align="end">
            {{ parametersFG.get('authorizationNumber')?.value?.length }}
            /
            {{ authorizationNumberMaxLength }}
          </mat-hint>
          @if (
            parametersFG.controls.authorizationNumber.hasError('maxlength')
          ) {
            <mat-error>
              {{
                t('deb.parameters-dialog.errors.max-length', {
                  maxlength: authorizationNumberMaxLength,
                })
              }}
            </mat-error>
          }
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline">
        <mat-label>
          {{ t('deb.statement-page.parameters.declarationNumber') }}
        </mat-label>
        <input
          matInput
          formControlName="declarationNumber"
          name="declarationNumber" />
        @if (parametersFG.controls.declarationNumber.hasError('min')) {
          <mat-error>
            {{
              t('deb.parameters-dialog.errors.greater-than', {
                min: declarationNumberMinValue - 1,
              })
            }}
          </mat-error>
        } @else if (
          parametersFG.controls.declarationNumber.hasError('pattern')
        ) {
          <mat-error>
            {{ t('deb.parameters-dialog.errors.pattern') }}
          </mat-error>
        }
      </mat-form-field>
    </form>

    <p>{{ t('sharedForm.mandatoryFields') }}</p>
  </mat-dialog-content>
  <mat-dialog-actions [align]="'end'">
    <button mat-stroked-button mat-dialog-close>
      {{ t('sharedAction.cancel') }}
    </button>
    <button
      mat-raised-button
      color="primary"
      [mat-dialog-close]="parametersFG.value"
      cdkFocusInitial
      [disabled]="parametersFG.invalid">
      {{ t('sharedAction.save') }}
    </button>
  </mat-dialog-actions>
</ng-container>
