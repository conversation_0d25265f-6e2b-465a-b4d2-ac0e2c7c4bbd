import { IAuthentication } from '@isagri-ng/security';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoDirective } from '@jsverse/transloco';
import { MockDirective } from 'ng-mocks';
import { MainContainerComponent } from './main-container.component';

const AuthenticationStub: Partial<IAuthentication> = {
  logout: jest.fn(),
};
describe('MainContainerComponent', () => {
  let spectator: Spectator<MainContainerComponent>;
  let component: MainContainerComponent;

  const createComponent = createComponentFactory({
    component: MainContainerComponent,
    declarations: [MockDirective(TranslocoDirective)],
    providers: [{ provide: IAuthentication, useValue: AuthenticationStub }],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create the component', () => {
    expect(spectator).toBeTruthy();
  });

  describe('logout method', () => {
    describe('given any state', () => {
      it('should call logout method of IAuthentication', () => {
        component.logout();
        expect(AuthenticationStub.logout).toHaveBeenCalled();
      });
    });
  });
});
