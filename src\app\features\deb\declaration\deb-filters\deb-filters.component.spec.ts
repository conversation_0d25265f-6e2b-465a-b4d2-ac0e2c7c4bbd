import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { DebFiltersComponent } from './deb-filters.component';
import { MockDirectives, MockPipes } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { CapitalizePipe } from '@gc/shared/ui';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Filters } from '@gc/core/deb/domains/models';

describe('DebFiltersComponent', () => {
  let spectator: Spectator<DebFiltersComponent>;
  let component: DebFiltersComponent;

  const createComponent = createComponentFactory({
    component: DebFiltersComponent,
    imports: [
      ReactiveFormsModule,
      MatButtonModule,
      MatCardModule,
      MatCheckboxModule,
      MatFormFieldModule,
      MatIconModule,
      MatInputModule,
    ],
    declarations: [
      MockDirectives(TranslocoDirective),
      MockPipes(TranslocoDatePipe, CapitalizePipe),
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;

    Object.defineProperty(component, 'declarationMonth', {
      get: () => () => new Date('2023-01-01'),
    });
  });

  it('should create', () => {
    spectator.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('form initialization', () => {
    it('should initialize form with both checkboxes checked by default', () => {
      spectator.detectChanges();

      expect(component.debFiltersFG.get('deliveryNoteRMAFC')?.value).toBe(true);
      expect(component.debFiltersFG.get('invoicesCreditNotesFC')?.value).toBe(
        true
      );
    });
  });

  describe('_forceOneCheckboxToBeChecked', () => {
    it('should set deliveryNoteRMAFC to true if both checkboxes are unchecked', () => {
      spectator.detectChanges();
      component.debFiltersFG.get('deliveryNoteRMAFC')?.setValue(false);
      component.debFiltersFG.get('invoicesCreditNotesFC')?.setValue(false);

      expect(component.debFiltersFG.get('deliveryNoteRMAFC')?.value).toBe(true);
      expect(component.debFiltersFG.get('invoicesCreditNotesFC')?.value).toBe(
        false
      );
    });

    it('should not change values if at least one checkbox is checked', () => {
      spectator.detectChanges();
      component.debFiltersFG.get('deliveryNoteRMAFC')?.setValue(false);
      component.debFiltersFG.get('invoicesCreditNotesFC')?.setValue(true);

      (component as any)._forceOneCheckboxToBeChecked();

      expect(component.debFiltersFG.get('deliveryNoteRMAFC')?.value).toBe(
        false
      );
      expect(component.debFiltersFG.get('invoicesCreditNotesFC')?.value).toBe(
        true
      );
    });
  });

  describe('applyFiltersSubmit', () => {
    it('should emit filter values when form is submitted', () => {
      spectator.detectChanges();
      const emitSpy = jest.spyOn(component.applyFilters, 'emit');

      component.debFiltersFG.get('deliveryNoteRMAFC')?.setValue(true);
      component.debFiltersFG.get('invoicesCreditNotesFC')?.setValue(false);

      component.applyFiltersSubmit();

      expect(emitSpy).toHaveBeenCalledWith({
        includeDeliveryNotes: true,
        includeInvoices: false,
      } as Partial<Filters>);
    });

    it('should emit both filter values as true when both checkboxes are checked', () => {
      spectator.detectChanges();
      const emitSpy = jest.spyOn(component.applyFilters, 'emit');

      component.debFiltersFG.get('deliveryNoteRMAFC')?.setValue(true);
      component.debFiltersFG.get('invoicesCreditNotesFC')?.setValue(true);

      component.applyFiltersSubmit();

      expect(emitSpy).toHaveBeenCalledWith({
        includeDeliveryNotes: true,
        includeInvoices: true,
      } as Partial<Filters>);
    });
  });
});
