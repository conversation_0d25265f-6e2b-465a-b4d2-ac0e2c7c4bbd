import { Component, OnInit, inject } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { PushPipe } from '@ngrx/component';
import { Observable } from 'rxjs';
import { HeaderTitleService } from '../../services/header-title.service';

@Component({
  selector: 'gc-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss', './header-theme.component.scss'],
  standalone: true,
  imports: [MatToolbarModule, PushPipe],
})
export class HeaderComponent implements OnInit {
  private readonly _headerTitleService = inject(HeaderTitleService);

  public title$!: Observable<string>;
  ngOnInit(): void {
    this.title$ = this._headerTitleService.title$;
  }
}
