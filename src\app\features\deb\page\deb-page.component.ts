import { Component, inject, OnInit } from '@angular/core';
import { TRANSLOCO_SCOPE, TranslocoDirective } from '@jsverse/transloco';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { DebDeclarationComponent } from '@gc/features/deb/declaration/deb-declaration';
import { DeviceDetectorService, DeviceInfo } from 'ngx-device-detector';

@Component({
  selector: 'gc-deb-page',
  standalone: true,
  imports: [
    DebDeclarationComponent,
    MainContainerComponent,
    TranslocoDirective,
  ],
  templateUrl: './deb-page.component.html',
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'deb', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'company', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/dialog', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/form', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
  ],
})
export class DebPageComponent implements OnInit {
  readonly deviceService = inject(DeviceDetectorService);
  deviceInfo: DeviceInfo | null = null;

  ngOnInit(): void {
    console.log('hello `Home` component');
    this.deviceInfo = this.deviceService.getDeviceInfo();
    const isMobile = this.deviceService.isMobile();
    const isTablet = this.deviceService.isTablet();
    const isDesktopDevice = this.deviceService.isDesktop();
    console.log('this.deviceInfo : ', this.deviceInfo);
    console.log('this.isMobile : ', isMobile); // returns if the device is a mobile device (android / iPhone / windows-phone etc)
    console.log('this.isTablet : ', isTablet); // returns if the device us a tablet (iPad etc)
    console.log('this.isDesktopDevice : ', isDesktopDevice); // returns if the app is running on a Desktop browser.  }
  }
}
