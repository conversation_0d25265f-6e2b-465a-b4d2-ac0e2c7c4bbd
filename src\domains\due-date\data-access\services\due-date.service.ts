import { Injectable, inject } from '@angular/core';
import {
  DueDateResponseApi,
  DocumentPostStateEnumApi,
  DueDateStateEnumApi,
  LegacyApiService,
} from '@gc/shared/api/data-access';
import { Observable, map } from 'rxjs';
import { DueDatePaginationAdapter } from '../adapters/due-date-pagination.adapter';
import { DueDateFilterApi } from '../models/api/due-date-filter-api.model';
import { DueDateFilterAdapter } from '../adapters/due-date-filter.adapter';
import { DueDatePagination, DueDateFilter, DueDate } from '@gc/due-date/models';

@Injectable({
  providedIn: 'root',
})
export class DueDateService {
  private readonly _legacyApiService = inject(LegacyApiService);

  public getDueDates(
    dueDateFilter: DueDateFilter
  ): Observable<DueDatePagination> {
    return this._getDueDatesFromFilterApi(
      DueDateFilterAdapter.toApi(dueDateFilter)
    ).pipe(
      map((dueDateResponseApi: DueDateResponseApi) =>
        DueDatePaginationAdapter.fromApi(dueDateResponseApi)
      )
    );
  }

  public getAllDueDatesByInvoiceId(invoiceId: string): Observable<DueDate[]> {
    const dueDateFilter: DueDateFilter = {
      invoiceId,
    };
    return this.getDueDates(dueDateFilter).pipe(
      map((dueDatePagination: DueDatePagination) => dueDatePagination.dueDates)
    );
  }

  private _getDueDatesFromFilterApi(
    dueDateFilterApi: DueDateFilterApi
  ): Observable<DueDateResponseApi> {
    return this._legacyApiService.dueDatesGetDueDates(
      dueDateFilterApi.numberContains,
      dueDateFilterApi.customerId,
      dueDateFilterApi.paymentId,
      dueDateFilterApi.creditNoteId,
      dueDateFilterApi.dueDateId,
      dueDateFilterApi.documentId,
      dueDateFilterApi.documentType,
      dueDateFilterApi.companyId,
      dueDateFilterApi.dueDateState as DueDateStateEnumApi | undefined,
      dueDateFilterApi.documentPostState as
        | DocumentPostStateEnumApi
        | undefined,
      dueDateFilterApi.firstElementNumber,
      dueDateFilterApi.elementsPerPage
    );
  }
}
