import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MockDirectives } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { MainSettingsStepperComponent } from './main-settings-stepper.component';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { VatService } from '@gc/vat/data-access';
import { SnackbarService } from '@gc/shared/ui';
import { LetDirective } from '@ngrx/component';

describe('MainSettingsStepperComponent', () => {
  let spectator: Spectator<MainSettingsStepperComponent>;
  const createComponent = createComponentFactory({
    component: MainSettingsStepperComponent,
    declarations: [MockDirectives(TranslocoDirective, LetDirective)],
    mocks: [FiscalYearService, WarehouseService, VatService, SnackbarService],
  });

  it('should create', () => {
    spectator = createComponent();

    expect(spectator.component).toBeTruthy();
  });
});
