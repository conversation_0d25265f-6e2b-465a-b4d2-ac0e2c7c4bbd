import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MonthlyTreatmentCardComponent } from '@gc/accounting/ui';
import { TranslocoDirective } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';

@Component({
  selector: 'gc-debt-list-treatment-id-retriever-card',
  standalone: true,
  imports: [MonthlyTreatmentCardComponent, TranslocoDirective, PushPipe],
  templateUrl: './debt-list-treatment-id-retriever-card.component.html',
})
export class DebtListTreatmentIdRetrieverCardComponent {
  @Input() invalid!: boolean;
  @Output() consult = new EventEmitter<void>();
}
