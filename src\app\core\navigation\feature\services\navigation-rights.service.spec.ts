import { NavigationRightsService } from './navigation-rights.service';
import { RightsService } from '@gc/core/rights/data-access';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';

describe('NavigationRightsService', () => {
  let spectator: SpectatorService<NavigationRightsService>;
  let service: NavigationRightsService;

  const createService = createServiceFactory({
    service: NavigationRightsService,
    mocks: [RightsService],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
