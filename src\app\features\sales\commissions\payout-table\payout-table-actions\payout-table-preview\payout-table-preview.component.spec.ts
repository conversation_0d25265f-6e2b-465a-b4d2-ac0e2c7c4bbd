import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { PayoutTablePreviewComponent } from './payout-table-preview.component';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { TranslocoDirective, TranslocoModule } from '@jsverse/transloco';
import { Commission } from '@gc/core/sales/commissions/domains/models';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MockDirective, MockPipe } from 'ng-mocks';
import { CurrencyService } from '@gc/currency/data-access';
import { PushPipe } from '@ngrx/component';
import { BehaviorSubject, of } from 'rxjs';
import { AmountWithCurrencyPipe } from '@gc/currency/ui';
import { LoaderComponent } from '@gc/shared/ui';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { LoadingStatus } from '@gc/shared/models';

describe('PayoutTablePreviewComponent', () => {
  let spectator: Spectator<PayoutTablePreviewComponent>;
  let commissionsFacade: CommissionsFacade;
  let currencyService: CurrencyService;

  const mockCommission: Commission = {
    documentId: 'DOC1',
    documentDate: '2024-01-01',
    amount: 100,
    amountToBePaid: 50,
    status: 1,
  };

  const createComponent = createComponentFactory({
    component: PayoutTablePreviewComponent,
    imports: [
      MatCardModule,
      MatTableModule,
      TranslocoModule,
      MatIconModule,
      MatDialogModule,
      MatButtonModule,
      PushPipe,
    ],
    declarations: [
      MockDirective(TranslocoDirective),
      MockPipe(AmountWithCurrencyPipe, (value) => `${value} €`),
      LoaderComponent,
    ],
    providers: [CommissionsFacade, CurrencyService],
    shallow: true,
  });

  beforeEach(() => {
    const mockCurrency = { code: 'EUR', symbol: '€' };
    const mockLoadingStatus = new BehaviorSubject<LoadingStatus>('LOADED');

    commissionsFacade = {
      loadCommissionDetailsFor: jest.fn(),
      getCommissionDetails: jest
        .fn()
        .mockReturnValue({ data: [], isLoading: false }),
    } as any;

    currencyService = {
      getDefaultCurrency$: jest.fn().mockReturnValue(of(mockCurrency)),
      getDefaultCurrencyLoadingStatus$: jest
        .fn()
        .mockReturnValue(mockLoadingStatus),
    } as any;

    spectator = createComponent({
      props: {
        commission: mockCommission,
      },
      providers: [
        { provide: CommissionsFacade, useValue: commissionsFacade },
        { provide: CurrencyService, useValue: currencyService },
      ],
    });
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  it('should call loadCommissionDetailsFor in constructor with the input commission', () => {
    expect(commissionsFacade.loadCommissionDetailsFor).toHaveBeenCalled();
    expect(commissionsFacade.loadCommissionDetailsFor).toHaveBeenCalledTimes(1);
  });

  it('should get the default currency loading status', () => {
    expect(currencyService.getDefaultCurrencyLoadingStatus$).toHaveBeenCalled();
    expect(spectator.component.defaultCurrencyLoadingStatus$).toBeDefined();
  });

  it('should get the default currency', () => {
    expect(currencyService.getDefaultCurrency$).toHaveBeenCalled();
    expect(spectator.component.currency$).toBeDefined();
  });
});
