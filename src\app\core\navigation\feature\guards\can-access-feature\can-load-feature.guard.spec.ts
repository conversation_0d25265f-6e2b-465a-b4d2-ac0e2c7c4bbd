import { Router } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { FlagService } from '../../services/flag.service';
import { canLoadFeature } from './can-load-feature.guard';
import { TestBed } from '@angular/core/testing';

describe('canLoadFeature', () => {
  let spectator: SpectatorService<FlagService>;
  let router: Router;

  const createService = createServiceFactory({
    service: FlagService,
    mocks: [Router],
  });

  beforeEach(() => {
    spectator = createService();
    router = spectator.inject(Router);
  });

  it('should return true when feature is accessible', (done) => {
    const featureName = 'someFeature';
    spectator.service.canAccessFeature = jest.fn().mockReturnValue(true);

    const guard = canLoadFeature();
    const route = { data: { featureFlag: featureName } };
    const result = TestBed.runInInjectionContext(() =>
      guard(route as any, {} as any)
    );

    expect(result).toBe(true);
    expect(spectator.service.canAccessFeature).toHaveBeenCalledWith(
      featureName
    );
    done();
  });

  it('should redirect to unavailable module when feature access is denied', () => {
    const featureName = 'someFeature';
    spectator.service.canAccessFeature = jest.fn();
    const canAccessFeatureSpy = jest.spyOn(
      spectator.service,
      'canAccessFeature'
    );
    canAccessFeatureSpy.mockReturnValue(false);
    router.parseUrl = jest.fn().mockReturnValue('/unavailable-module');

    const guard = canLoadFeature();
    const route = { data: { featureFlag: featureName } };
    const result = TestBed.runInInjectionContext(() =>
      guard(route as any, {} as any)
    );

    expect(result).toBe(router.parseUrl(`/${URL_PATHS.unavailableModule}`));
    expect(canAccessFeatureSpy).toHaveBeenCalledWith(featureName);
    expect(router.parseUrl).toHaveBeenCalledWith(
      `/${URL_PATHS.unavailableModule}`
    );
  });
});
