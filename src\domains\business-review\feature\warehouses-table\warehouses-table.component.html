<div *transloco="let t" class="container">
  <p data-testid="warehouses-information">
    {{ t('businessReview.advanced-settings-tab.warehouses.information') }}
  </p>

  <ng-container
    *ngrxLet="{
      warehouses: warehouses$,
      isLoading: isLoading$,
      showAll: showAll$$,
    } as vm">
    @if (vm.isLoading) {
      <div class="loader-container">
        <gc-loader />
      </div>
    } @else if (vm.warehouses && vm.warehouses.data.length > 0) {
      <mat-checkbox
        data-testid="warehouses-choice"
        #showAllCheckbox
        [value]="vm.showAll.toString()"
        [checked]="vm.showAll"
        class="show-all-checkbox"
        (change)="onShowAllCheckboxChange()"
        color="primary"
        >{{
          t('businessReview.advanced-settings-tab.warehouses.show-all-checkbox')
        }}</mat-checkbox
      >

      <div class="table-container" data-testid="table-container">
        <table mat-table [dataSource]="vm.warehouses">
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? toggleAllRows() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
                [aria-label]="checkboxAriaLabel()"
                color="primary" />
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox
                (click)="onRowSelectionChange($event, row)"
                (change)="$event ? selection.toggle(row) : null"
                [checked]="selection.isSelected(row)"
                [aria-label]="checkboxAriaLabel(row)"
                color="primary" />
            </td>
          </ng-container>

          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef>
              {{
                t(
                  'businessReview.advanced-settings-tab.warehouses.table.column-header.code'
                )
              }}
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.code }}
            </td>
          </ng-container>

          <!-- Label Column -->
          <ng-container matColumnDef="label">
            <th mat-header-cell *matHeaderCellDef>
              {{
                t(
                  'businessReview.advanced-settings-tab.warehouses.table.column-header.label'
                )
              }}
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.label }}
            </td>
          </ng-container>

          <tr
            mat-header-row
            *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr
            mat-row
            *matRowDef="let row; columns: displayedColumns"
            (click)="selection.toggle(row)"></tr>
        </table>
      </div>
    }
  </ng-container>
</div>
