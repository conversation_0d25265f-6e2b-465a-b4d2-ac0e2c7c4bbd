import { LOCKABLE_COMPONENT_TOKEN } from '@gc/core/navigation/models';
import { createDirectiveFactory, SpectatorDirective } from '@ngneat/spectator';
import { BlockReloadDirective } from './block-reload.directive';

describe('BlockReload with LockableComponent', () => {
  let spectator: SpectatorDirective<BlockReloadDirective>;
  let directive: BlockReloadDirective;
  const lockableComponentToken = { canDeactivate: jest.fn() };
  const createDirective = createDirectiveFactory({
    directive: BlockReloadDirective,
    template: `<div gcBlockReload #gcBlockReload="gcBlockReload">Test</div>`,
    providers: [
      {
        provide: LOCKABLE_COMPONENT_TOKEN,
        useValue: lockableComponentToken,
      },
    ],
  });

  beforeEach(() => {
    spectator = createDirective();
    ({ directive } = spectator);
    jest.resetAllMocks();
  });

  it('should create', () => {
    expect(spectator.directive).toBeTruthy();
  });

  describe('window:beforeunload', () => {
    const preventDefaultSpy = jest.fn();
    const event = {
      preventDefault: preventDefaultSpy,
    } as unknown as BeforeUnloadEvent;

    it('should prevent reload since canDeactivate return false', () => {
      lockableComponentToken.canDeactivate.mockReturnValue(false);

      directive.onBeforeUnload(event);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should not prevent reload since canDeactivate return true', () => {
      lockableComponentToken.canDeactivate.mockReturnValue(true);

      directive.onBeforeUnload(event);

      expect(preventDefaultSpy).not.toHaveBeenCalled();
    });
  });
});

describe('BlockReload without LockableComponent', () => {
  const createDirective = createDirectiveFactory({
    directive: BlockReloadDirective,
    template: `<div gcBlockReload #gcBlockReload="gcBlockReload">Test</div>`,
    providers: [
      {
        provide: LOCKABLE_COMPONENT_TOKEN,
        useValue: null,
      },
    ],
  });

  describe('ngOnInit', () => {
    it('should throw an error since LockableComponent is undefined', () => {
      expect(() => {
        createDirective();
      }).toThrow('You need to provide a LockableComponent');
    });
  });
});
