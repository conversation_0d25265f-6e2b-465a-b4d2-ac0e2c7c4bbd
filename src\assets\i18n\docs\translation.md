# Guide for Structuring and Naming Translation Files

This project uses **Transloco** to manage application translations. The goal of this guide is to present folder structure, translation file organization, and naming rules for translation keys, ensuring a coherent and maintainable structure.

## i18n Folder Structure

All translations are in src/assets/i18n/folder .

A translation file is organized by **page** and **business concepts** (e.g., "company").

There is also a `shared` folder for translations related to user interaction concepts (e.g., forms).

## Keys creation rules

**Core keys (/core)**: Use the main `fr-FR.json` file for translations from the `src/app/core/` folder.

**Business concept keys (/domain)**: Translations specific to a page, or for tabs of page, page tabs, of a business concept should be placed in the corresponding folder, named after the page or the business concept.
`accounting/fr-FR.json` will contain the translation keys related to the accounting page.
Example for `accounting/fr-FR.json` ( Translations for the accounting page), the "Business concept keys" can be divided into two subcategories:

-   A specific page : xxx-page: {} (e.g. "monthly-treatment-page", "monthly-treatment-history-page" if there are multiple pages) or simply "page" if there is only one page.
-   A business concept : yyy: {} (e.g. monthly-editions)

Example :

    ```json
    {
      // or with for only page :
      page {}
      business-concept: {}
      ...

      // or with mutilple tab for a page :
      page: {}
      monthly-treatment-tab: {}
      monthly-treatment-history-tab:{}
      business-concept: {}
      ...

      // or mutilple tab for and multiple page :
      aaa-page: {
        bbb-tab: {},
        ccc-tab: {}
      },

      mmm-page {
        xxx-tab: {}
        yyy-tab: {}
      }
      business-concept: {}
      ...

    }
    ```

    ⚠️ A business concept like `company` can evolve into a page if you decide to create a page for managing companies (creation, deletion, etc.).
    In that case, the `fr-FR.json` file for `company` can include a `page` key, just like other pages.
    Indeed, `company/fr-FR.json`: Translations specific to the business concept of companies.

**Reusable component (/domain)**:
For a reusable component, we place it in a folder just like we do for a page, similar to how we handle the "company" component.

**User interaction concept keys (/shared)**: Translations corresponding to user interaction concepts should be place in a shared file. For instance "Required field" should be placed in /shared/form/zzz.json under the key "required".

    For example : `i18n/shared/form`: Shared translations related to forms.

    ```json
    {
        "error": {
            "required": "Champs requis",
            "date": {
                "invalid": "Date invalide"
            }
        }
    }
    ```

### Business concept keys : Managing Similar Translations Across Different Components

When you have the same translation used within the same page but in different components, it is recommended to factorize this translation by creating a business-specific key in the corresponding translation file. For example, in `accounting/fr-FR.json`:

```json
{
    "page": {
        // Translations related to the page, such as tab names
    },
    "monthly-treatment-edition-tab": {
        // Translations related to the left tab for the component monthly-treatment-edition
    },
    "history": {
        // Translations related to the right tab for the component monthly-treatment-history
    },
    "monthly-treatment-categories": {
        // Business-related translations, accessible by both history and monthly-treatment-edition components
        "categories": {
            "sales": "Ventilation des ventes par catégorie de produit"
        }
    }
}
```

Example:

In `accounting/fr-FR.json`:

![alt text](accounting-edition-image.png)

![alt text](edition-history-image.png)

## Naming rules conventions

### Naming Rules for Translation Files

They should be in **kebab-case** and named in the format: `fr-FR.json` (for example, for French).

### Naming Rules for Translation Keys

Translation keys should be structured to reflect the application's hierarchy or business context.

The translation keys must follow the **kebab-case** format.

Example:

```json
[Dossiers] authentification/fr-FR.json :

{
 "button":{
     "login": "Login",
     "logout": "Logout",
 },
 "invalid-credentials": "Invalid username or password."
}
```

#### Best Practices

-   Avoid keys that are too deep or complex.
-   Do not use key names that are too abstract, ambiguous.
-   Use descriptive names: names that clearly reflect the content of the string they represent.

#### Factorization

It is recommended to **factorize** translations to avoid unnecessary repetition. If the same term is used in multiple contexts (e.g., the "Save" button), this will simplify future modifications.

-   **Example**: If the term "Save" changes to "Store", it only needs to be modified once in the global file.

#### Duplication

Duplication, however, can be justified when identical terms have different contextual meanings or may evolve independently.

## Transloco : practical guide

### Access to Translation Files in Subfolders

⚠️ **Attention**: When translation files are organized into subfolders, such as in `shared/form`, it is necessary to use the full path in kebab-case to access the translation key in the HTML.

-   **Example**: To access a translation in the file `shared/form/fr.json`, you will need to write: `t('sharedForm.error.required')` in the HTML.

### Loading Multiple Translation Files in the Same Module or Component

It is possible to load multiple translation files within the same module or component, particularly in the context of **standalone** components. To do this:

In the component, you can provide multiple Transloco scopes using the method that supplies the appropriate providers. It is also possible to provide one of the scopes in the parent component. The translations will then be available in the child component.

```typescript
providers: [
 {
  provide: TRANSLOCO_SCOPE,
  useValue: 'shared/form',
  multi: true,
 },

 {
  provide: TRANSLOCO_SCOPE,
  useValue: 'accounting',
  multi: true,
 },
],
```

After providing, add the `*transloco` directive in the template:

```html
<ng-container *transloco="let t">
    <gc-company-single-select
        [label]="t('accounting.companies-select.label')"
        [errorText]="t('sharedForm.error.required')">
    </gc-company-single-select>
</ng-container>
```

We prefer using the Transloco directive because it allows us to subscribe once and retrieve translations throughout the entire HTML template.

Here, we use two different translation scopes: `sharedForm` and `accounting`.

# Sources

-   <https://simplelocalize.io/blog/posts/what-is-translation-key/>
-   <https://lokalise.com/blog/translation-keys-naming-and-organizing/>
-   <https://tolgee.io/blog/naming-translation-keys>
-   <https://dev.to/omaiboroda/three-ways-to-name-i18n-translation-keys-2fed>
-   <https://poeditor.com/blog/translation-keys/>
