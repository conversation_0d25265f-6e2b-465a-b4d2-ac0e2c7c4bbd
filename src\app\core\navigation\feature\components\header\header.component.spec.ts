import { SpectatorRouting, createRoutingFactory } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { HeaderComponent } from './header.component';

describe('HeaderComponent', () => {
  let spectator: SpectatorRouting<HeaderComponent>;
  let headerComponent: HeaderComponent;

  const createComponent = createRoutingFactory({
    component: HeaderComponent,
    mocks: [TranslocoService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    headerComponent = spectator.component;
  });

  it('should create', () => {
    expect(headerComponent).toBeTruthy();
  });
});
