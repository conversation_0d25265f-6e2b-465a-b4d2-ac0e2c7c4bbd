import { Paths } from '../enums/paths.enum';

export const URL_PATHS = {
  accounting: `${Paths.ACCOUNTING}`,
  accountingHistory: `${Paths.ACCOUNTING}/${Paths.ACCOUNTING_HISTORY}`,
  accountingVisualization: `${Paths.ACCOUNTING}/${Paths.ACCOUNTING_VISUALIZATION}`,
  codeParameters: `${Paths.PRODUCT}/${Paths.PARAMETERS}`,
  codeStructure: `${Paths.PRODUCT}/${Paths.PARAMETERS}/${Paths.CODE_STRUCTURE}`,
  codeDesignation: `${Paths.PRODUCT}/${Paths.PARAMETERS}/${Paths.CODE_DESIGNATION}`,
  deb: `${Paths.DEB}`,
  dunning: `${Paths.DUNNING}`,
  dunningHistory: `${Paths.DUNNING}/${Paths.HISTORY}`,
  dunningParameters: `${Paths.DUNNING}/${Paths.PARAMETERS}`,
  dunningsViewer: `${Paths.DUNNING}/${Paths.VIEWER}`,
  businessReview: `${Paths.BUSINESS_REVIEW}`,
  businessReviewViewer: `${Paths.BUSINESS_REVIEW}/${Paths.VIEWER}`,
  invalidConfig: `${Paths.INVALID_CONFIG}`,
  login: `${Paths.LOGIN}`,
  product: `${Paths.PRODUCT}`,
  reports: `${Paths.REPORTS}`,
  reportsReport: `${Paths.REPORTS}/${Paths.REPORT}`,
  salesCommission: `${Paths.SALES}/${Paths.COMMISSIONS}`,
  stimulsoftViewer: `${Paths.STIMULSOFT_VIEWER}`,
  unavailableModule: `${Paths.UNAVAILABLE_MODULE}`,
  unavailableDevice: `${Paths.UNAVAILABLE_DEVICE}`,
};

export type UrlPaths = (typeof URL_PATHS)[keyof typeof URL_PATHS];

export const DEFAULT_ROUTE: UrlPaths = URL_PATHS.dunning;
