.deb-filters-container {
  display: flex;
  gap: 3rem;
  flex: 1 1 auto;
}

.form-container {
  display: flex;
  flex-direction: column;
}

/**
* Atomic CSS inspired by TailwindCSS
* @see https://tailwindcss.com
*/
.h-full {
  height: 100%;
}

.actions-aligned-end {
  display: flex;
  flex-direction: column-reverse;
  margin-left: auto;
}

.month-item {
  display: flex;
  align-items: center;
  margin: 8px 0;
  min-height: 2.5rem; /* Match the height of a checkbox */

  p {
    margin: 0;
  }
}
