import { SalesDetailsOrderByEnum } from '@gc/accounting/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { FEATURE_KEY_MONTHLY_TREATMENT } from '../models/monthly-treatment-key.constant';
import { MonthlyTreatmentState } from '../models/monthly-treatment-state.model';

export const selectMonthlyTreatmentState =
  createFeatureSelector<MonthlyTreatmentState>(FEATURE_KEY_MONTHLY_TREATMENT);

export const selectMonthlyTreatmentCompanyId = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.companyId
);

export const selectMonthlyTreatmentDateRange = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.range
);

export const selectMonthlyTreatmentDateRangeAndCompanyId = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => ({
    range: state.range,
    companyId: state.companyId,
  })
);

export const selectSalesByProductCurrentFilter = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.salesByProductCurrentFilter
);

export const selectSalesDetailsCurrentFilter = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) =>
    state.salesDetailsCurrentFilter === SalesDetailsOrderByEnum.BUSINESS_TYPE
);

export const selectDefaultFiltersLoadingStatus = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.defaultFiltersLoadingStatus
);

export const selectEnclosureMonth = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.enclosureMonth
);

export const selectEmptyEditions = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.emptyEditions
);

export const selectEmptyEditionsLoadingStatus = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.emptyEditionsLoadingStatus
);

export const selectEnclosingStatus = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.enclosingStatus
);

export const selectEnclosingDefaultStatus = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.enclosingDefaultStatus
);

export const selectEnclosureMonthLoadingStatus = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) => state.enclosureMonthLoadingStatus
);

export const selectHasEnclosureMonth = createSelector(
  selectMonthlyTreatmentState,
  (state: MonthlyTreatmentState) =>
    state.enclosureMonthLoadingStatus === 'LOADED'
      ? state.hasAlreadyEnclosedMonths
      : null
);

export const selectCompanyIdAndLastEnclosedMonth = createSelector(
  selectMonthlyTreatmentState,
  ({ companyId, lastEnclosedMonth }: MonthlyTreatmentState) => ({
    companyId,
    lastEnclosedMonth,
  })
);
