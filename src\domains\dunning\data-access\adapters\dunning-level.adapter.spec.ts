import { DunningLevelApi } from '@gc/shared/api/data-access';
import { DunningLevel } from '../../models/dunning-level.model';
import { DunningLevelAdapter } from './dunning-level.adapter';

describe('DunningLevelAdapter', () => {
  describe('fromApi', () => {
    describe('given all DunningLevelApi enum values', () => {
      describe('when method is called on each level', () => {
        it('should succeed to adapt and not throw error', () => {
          const levelEnumValues: DunningLevelApi[] = Object.values(
            DunningLevelApi
          ) as DunningLevelApi[];
          const levelEnumNumberValues = levelEnumValues.filter(
            (v) => !isNaN(Number(v))
          );

          levelEnumNumberValues.forEach((value: DunningLevelApi) => {
            expect(DunningLevelAdapter.fromApi(value)).toBeTruthy();
          });
        });
      });
    });

    describe('given all DunningLevelProposalApi.DunningLevelIdEnum enum values', () => {
      describe('when method is called on each level', () => {
        it('should succeed to adapt and not throw error', () => {
          const levelEnumValues: DunningLevelApi[] = Object.values(
            DunningLevelApi
          ) as DunningLevelApi[];
          const levelEnumNumberValues = levelEnumValues.filter(
            (v) => !isNaN(Number(v))
          );

          levelEnumNumberValues.forEach((value: DunningLevelApi) => {
            expect(DunningLevelAdapter.fromApi(value)).toBeTruthy();
          });
        });
      });
    });

    describe('given all DunningLevelProposalApi.NextDunningLevelIdProposalEnum enum values', () => {
      describe('when method is called on each level', () => {
        it('should succeed to adapt and not throw error', () => {
          const levelEnumValues: DunningLevelApi[] = Object.values(
            DunningLevelApi
          ) as DunningLevelApi[];
          const levelEnumNumberValues = levelEnumValues.filter(
            (v) => !isNaN(Number(v))
          );

          levelEnumNumberValues.forEach((value: DunningLevelApi) => {
            expect(DunningLevelAdapter.fromApi(value)).toBeTruthy();
          });
        });
      });
    });

    describe('given a null value', () => {
      describe('when method is called', () => {
        it('should return DunningLevel.NotExisting', () => {
          expect(DunningLevelAdapter.fromApi(null)).toBe(
            DunningLevel.NOT_EXISTING
          );
        });
      });
    });

    describe('given an undefined value', () => {
      describe('when method is called', () => {
        it('should return DunningLevel.NotExisting', () => {
          expect(DunningLevelAdapter.fromApi(undefined)).toBe(
            DunningLevel.NOT_EXISTING
          );
        });
      });
    });
  });
});
