import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { UnavailableDeviceComponent } from './unavailable-device.component';
import { DeviceDetectorService } from 'ngx-device-detector';

describe('UnavailableDeviceComponent', () => {
  let spectator: Spectator<UnavailableDeviceComponent>;
  const createComponent = createComponentFactory({
    component: UnavailableDeviceComponent,
    declarations: [MockDirective(TranslocoDirective), DeviceDetectorService],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });
});
