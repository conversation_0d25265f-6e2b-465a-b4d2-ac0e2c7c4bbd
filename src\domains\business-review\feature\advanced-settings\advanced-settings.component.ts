import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  signal,
  inject,
  DestroyRef,
} from '@angular/core';
import { MatListModule, MatSelectionListChange } from '@angular/material/list';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { WarehousesTableComponent } from '../warehouses-table/warehouses-table.component';
import { MatButtonModule } from '@angular/material/button';
import { UpperCasePipe } from '@angular/common';
import {
  MasterDetailPanelsLayoutComponent,
  SnackbarService,
} from '@gc/shared/ui';
import { VatTableComponent } from '../vat-table/vat-table.component';
import { BehaviorSubject } from 'rxjs';
import { BusinessReviewFormService } from '../services/business-review-form.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-advanced-settings',
  standalone: true,
  imports: [
    TranslocoModule,
    MasterDetailPanelsLayoutComponent,
    MatListModule,
    MatButtonModule,
    VatTableComponent,
    WarehousesTableComponent,
    UpperCasePipe,
    PushPipe,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'business-review',
      multi: true,
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/action',
      multi: true,
    },
  ],
  templateUrl: './advanced-settings.component.html',
  styleUrls: [
    './advanced-settings.component.scss',
    './advanced-settings-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdvancedSettingsComponent implements OnInit {
  private readonly _service = inject(BusinessReviewFormService);
  private readonly _snackbarService = inject(SnackbarService);
  private readonly _destroyRef = inject(DestroyRef);

  detailPanelTitleKey: string | undefined = 'panel-title.vat';

  pendingValidationWarehouseIds$$ = new BehaviorSubject<string[]>([]);

  pendingValidationVatRate$$ = new BehaviorSubject<number | null>(null);

  isLoading$ = this._service.isLoading$;

  hasPendingChanges = signal(false);

  ngOnInit(): void {
    this._service.warehousesFC.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((value) => {
        this.pendingValidationWarehouseIds$$.next(value);
        this.hasPendingChanges.set(false);
      });

    this._service.vatRateFC.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((value) => {
        this.pendingValidationVatRate$$.next(value);
        this.hasPendingChanges.set(false);
      });
  }

  onVatRateSelectionChange(rate: number | null): void {
    this.pendingValidationVatRate$$.next(rate);
    this.hasPendingChanges.set(true);
  }

  onWarehouseSelectionChange(ids: string[]): void {
    this.pendingValidationWarehouseIds$$.next(ids);
    this.hasPendingChanges.set(true);
  }

  onMatListOptionChange(event: MatSelectionListChange): void {
    switch (event.options[0].value) {
      case 'vat':
        this.detailPanelTitleKey = 'panel-title.vat';
        break;
      case 'warehouses':
        this.detailPanelTitleKey = 'panel-title.warehouses';
        break;
      default:
        this.detailPanelTitleKey = undefined;
        break;
    }
  }

  onCancel(): void {
    this.pendingValidationWarehouseIds$$.next(this._service.warehousesFC.value);
    this.pendingValidationVatRate$$.next(this._service.vatRateFC.value);
    this.hasPendingChanges.set(false);
  }

  onValidate(): void {
    this._validateWarehouseIds();
    this._validateVatRate();
    this._snackbarService.success({
      key: 'businessReview.advanced-settings-tab.notification.save-success',
    });
  }

  private _validateVatRate(): void {
    if (
      this._service.vatRateFC.value !== this.pendingValidationVatRate$$.value
    ) {
      this._service.vatRateFC.setValue(this.pendingValidationVatRate$$.value);
      this._service.vatRateFC.markAsDirty();
    }
  }

  private _validateWarehouseIds(): void {
    if (
      this._service.warehousesFC.value !==
      this.pendingValidationWarehouseIds$$.value
    ) {
      this._service.warehousesFC.setValue(
        this.pendingValidationWarehouseIds$$.value
      );
      this._service.warehousesFC.markAsDirty();
    }
  }
}
