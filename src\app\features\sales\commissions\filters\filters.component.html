<ng-container *transloco="let t">
  <div class="filter-container">
    <div class="input-container" [formGroup]="filterFG">
      <div class="left-container">
        <gc-company-single-select
          formControlName="companyIdFC"
          [inputId]="'companies-select'"
          [label]="t('company.companies-select.label')"
          [invalid]="
            companyIdFC.invalid && (companyIdFC.dirty || companyIdFC.touched)
          "
          [errorText]="t('sharedForm.error.required')" />

        <mat-form-field
          data-testid="mat-form-field-representative"
          appearance="outline"
          subscriptSizing="dynamic"
          class="representative-select-form-field">
          <mat-label class="label">
            {{
              t('sales.commissions-tab.filter.representative.label')
            }}</mat-label
          >
          <input
            matInput
            type="text"
            [matAutocomplete]="auto"
            formControlName="representativeFC"
            (blur)="onRepresentativeBlur()" />
          <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
            @for (
              filteredRepresentative of filteredRepresentatives();
              track filteredRepresentative.id
            ) {
              <mat-option [value]="filteredRepresentative">
                {{ filteredRepresentative.fullname }}
              </mat-option>
            }
          </mat-autocomplete>

          @if (representativeFC.hasError('required')) {
            <mat-error>{{ t('sharedForm.error.required') }}</mat-error>
          }
        </mat-form-field>
      </div>
      <div class="right-container">
        <mat-form-field
          class="due-date-selector"
          subscriptSizing="dynamic"
          appearance="outline">
          <mat-label>{{
            t('sales.commissions-tab.filter.date-due.label')
          }}</mat-label>

          <input
            matInput
            ingMatDatepickerDirective
            [matDatepicker]="picker"
            formControlName="dateCommissionsDueFC" />
          <mat-datepicker-toggle matIconSuffix [for]="picker" />
          <mat-datepicker #picker />
          @if (dateCommissionsDueFC.invalid) {
            <mat-error>{{ t('sharedForm.error.required') }}</mat-error>
          }
        </mat-form-field>
      </div>
    </div>
    <p class="rule-container">
      {{ t('sales.commissions-tab.filter.representative.rule') }}
    </p>
  </div>
</ng-container>
