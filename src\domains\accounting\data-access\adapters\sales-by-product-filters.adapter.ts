import { SalesByProductFilters } from '@gc/accounting/models';
import { ProductCharacteristicEnumAdapter } from '@gc/product/data-access';
import { StimulsoftTreatmentSalesByProductCategoryFilterApi } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';

export class SalesByProductFiltersAdapter {
  public static toApi(
    filters: SalesByProductFilters
  ): StimulsoftTreatmentSalesByProductCategoryFilterApi {
    const categories = filters.categories
      .filter((category) => category !== null && category !== undefined)
      .map((category) => {
        return ProductCharacteristicEnumAdapter.toApi(category!);
      });
    return {
      categories,
      dateFrom: DateAdapter.dateToStringAPI(filters.dateFrom) ?? undefined,
      dateTo: DateAdapter.dateToStringAPI(filters.dateTo) ?? undefined,
      companyId: filters.companyId,
    };
  }
}
