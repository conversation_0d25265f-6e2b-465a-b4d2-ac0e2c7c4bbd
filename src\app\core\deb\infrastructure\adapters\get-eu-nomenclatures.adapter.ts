import { inject, Injectable } from '@angular/core';
import { GetEuNomenclaturesPort } from '@gc/core/deb/domains/ports';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { map } from 'rxjs';

@Injectable()
export class GetEuNomenclaturesAdapter implements GetEuNomenclaturesPort {
  private readonly api = inject(DebApiService);

  get() {
    return this.api.getEuNomenclatures().pipe(
      map((nomenclatures) => {
        if (!nomenclatures) {
          return [] as string[];
        }
        return nomenclatures.map(
          (nomenclature) => nomenclature.code
        ) as string[];
      })
    );
  }
}
