import { BusinessResultAPIMessageApi } from '@gc/shared/api/data-access';
import { BusinessResultErrorMessage } from '@gc/dunning/models';

export class BusinessResultErrorMessageApiAdapter {
  static fromApi(
    businessResultAPIMessageApi: BusinessResultAPIMessageApi[]
  ): BusinessResultErrorMessage[] {
    const result: BusinessResultErrorMessage[] = [];
    businessResultAPIMessageApi.forEach((m: BusinessResultAPIMessageApi) =>
      result.push({ code: m.code, message: m.message })
    );
    return result;
  }
}
