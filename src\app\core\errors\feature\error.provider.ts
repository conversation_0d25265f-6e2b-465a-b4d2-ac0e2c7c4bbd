import {
  APP_INITIALIZER,
  EnvironmentProviders,
  <PERSON>rror<PERSON><PERSON><PERSON>,
  FactoryProvider,
  inject,
  makeEnvironmentProviders,
} from '@angular/core';
import { GcErrorHandlerService } from './services/gc-error-handler.service';
import { ITraceService } from '@isagri-ng/core/diagnostics';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TraceLogHttpErrorsInterceptor } from './interceptors/trace-log-http-errors.interceptor';

const providerNotPresent = <FactoryProvider>{
  provide: APP_INITIALIZER,
  multi: true,
  useFactory: () => {
    const traceService = inject(ITraceService);

    return (): void => {
      if (!traceService) {
        throw new Error(
          'ITraceService has not been provided. You should provide provideDiagnostic in the app.config.ts file.'
        );
      }
    };
  },
};

export function provideError(): EnvironmentProviders {
  return makeEnvironmentProviders([
    providerNotPresent,
    { provide: <PERSON><PERSON>r<PERSON><PERSON><PERSON>, useClass: GcErrorHandlerService },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TraceLogHttpErrorsInterceptor,
      multi: true,
    },
  ]);
}
