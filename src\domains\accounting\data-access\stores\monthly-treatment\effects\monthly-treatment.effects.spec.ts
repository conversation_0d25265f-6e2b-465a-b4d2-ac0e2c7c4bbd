import { waitForAsync } from '@angular/core/testing';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentService,
} from '@gc/accounting/data-access';
import {
  EnclosingMonthInformations,
  MonthlyTreatmentEditionsFiltersModel,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { provideMockActions } from '@ngrx/effects/testing';
import { Action, Store } from '@ngrx/store';
import { provideMockStore } from '@ngrx/store/testing';
import { Observable, of, throwError } from 'rxjs';
import { MonthlyTreatmentState } from '../models/monthly-treatment-state.model';
import { initialState } from '../reducers/monthly-treatment.reducer';
import { MonthlyTreatmentEffects } from './monthly-treatment.effects';

describe('MonthlyTreatmentEffects', () => {
  let spectator: SpectatorService<MonthlyTreatmentEffects>;
  let monthlyTreatmentEffects: MonthlyTreatmentEffects;
  let actions$: Observable<Action>;
  let monthlyTreatmentService: MonthlyTreatmentService;
  let store: Store;
  const createService = createServiceFactory({
    service: MonthlyTreatmentEffects,
    providers: [
      provideMockActions(() => actions$),
      provideMockStore({ initialState }),
    ],
    mocks: [MonthlyTreatmentService],
  });

  beforeEach(() => {
    spectator = createService();
    monthlyTreatmentEffects = spectator.service;

    monthlyTreatmentService = spectator.inject(MonthlyTreatmentService);
    store = spectator.inject(Store);
  });

  describe('loadDefaultFilters effect', () => {
    let getDefaultFiltersSpy: jest.SpyInstance<
      Observable<MonthlyTreatmentEditionsFiltersModel>
    >;

    beforeEach(() => {
      getDefaultFiltersSpy = jest.spyOn(
        monthlyTreatmentService,
        'getDefaultFilters'
      );
    });

    describe('given a state where getDefaultFilters return a MonthlyTreatmentEditionsFiltersModel', () => {
      const filters: MonthlyTreatmentEditionsFiltersModel = {
        salesDetailOrder: SalesDetailsOrderByEnum.BUSINESS_TYPE,
        productCategories: [
          ProductCharacteristicEnum.CUSTOM_0,
          ProductCharacteristicEnum.CUSTOM_10,
        ],
      };

      beforeEach(() => {
        getDefaultFiltersSpy.mockReturnValueOnce(of(filters));
      });

      describe('and a loadDefaultFilters action', () => {
        beforeEach(() => {
          actions$ = of(monthlyTreatmentActions.loadDefaultFilters());
        });

        it('should return loadDefaultFiltersSuccess action', waitForAsync(() => {
          monthlyTreatmentEffects.loadDefaultFilters$.subscribe(
            (action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadDefaultFiltersSuccess({
                  filters,
                })
              )
          );
        }));
      });
    });

    describe('given a state where getDefaultFilters throw an Error', () => {
      beforeEach(() => {
        getDefaultFiltersSpy.mockReturnValueOnce(
          throwError(() => new Error('getDefaultFiltersSpy error'))
        );
      });

      describe('and a loadDefaultFilters action', () => {
        beforeEach(() => {
          actions$ = of(monthlyTreatmentActions.loadDefaultFilters());
        });

        it('should return loadDefaultFiltersError action', waitForAsync(() => {
          monthlyTreatmentEffects.loadDefaultFilters$.subscribe(
            (action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadDefaultFiltersError()
              )
          );
        }));
      });
    });
  });

  describe('loadEnclosureMonth effect', () => {
    let getAccountingDefaultMonthSpy: jest.SpyInstance<
      Observable<EnclosingMonthInformations | null>
    >;
    const enclosureMonth = new Date('2024-01-01');
    const companyId = 'companyId';

    beforeEach(() => {
      getAccountingDefaultMonthSpy = jest.spyOn(
        monthlyTreatmentService,
        'getAccountingDefaultMonth'
      );
    });

    describe('given a state where getAccountingDefaultMonth return an enclosureMonth', () => {
      const enclosingMonthInformations: EnclosingMonthInformations = {
        enclosureMonth: enclosureMonth,
        hasAlreadyEnclosedMonths: true,
      };
      beforeEach(() => {
        getAccountingDefaultMonthSpy.mockReturnValueOnce(
          of(enclosingMonthInformations)
        );
      });

      describe('and a loadEnclosureMonth action', () => {
        beforeEach(() => {
          actions$ = of(
            monthlyTreatmentActions.loadEnclosureMonth({
              companyId,
            })
          );
        });

        it('should return loadEnclosureMonthSuccess action', waitForAsync(() => {
          monthlyTreatmentEffects.loadEnclosureMonth$.subscribe(
            (action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadEnclosureMonthSuccess({
                  enclosingMonthInformations,
                })
              )
          );
        }));
      });
    });

    describe('given a state where getAccountingDefaultMonth throw an Error', () => {
      beforeEach(() => {
        getAccountingDefaultMonthSpy.mockReturnValueOnce(
          throwError(() => new Error('getAccountingDefaultMonthSpy error'))
        );
      });

      describe('and a changeCompanyId action', () => {
        beforeEach(() => {
          actions$ = of(monthlyTreatmentActions.changeCompanyId({ companyId }));
        });

        it('should return loadEnclosureMonthError action', waitForAsync(() => {
          monthlyTreatmentEffects.loadEnclosureMonth$.subscribe(
            (action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadEnclosureMonthError()
              )
          );
        }));
      });
    });
  });

  describe('loadHasEmptyEdition effect', () => {
    describe('given a state where loadHasEmptyEdition is called', () => {
      let checkEmptyDataSpy: jest.SpyInstance<Observable<boolean | undefined>>;

      beforeEach(() => {
        actions$ = of(monthlyTreatmentActions.loadHasEmptyEdition());
        checkEmptyDataSpy = jest.spyOn(
          monthlyTreatmentService,
          'checkEmptyData'
        );
      });

      describe('given checkEmptyDataSpy return value', () => {
        beforeEach(() => {
          checkEmptyDataSpy.mockReturnValue(of(true));
        });

        it('should return loadHasEmptyEditionsSuccess action', waitForAsync(() => {
          const state = {
            companyId: 'companyId',
            range: {
              start: new Date('2024-06-01'),
              end: new Date('2024-06-30'),
            },
          } as MonthlyTreatmentState;

          jest.spyOn(store, 'select').mockReturnValue(of(state));
          monthlyTreatmentEffects.loadHasEmptyEdition$.subscribe(
            (action: Action) => {
              expect(checkEmptyDataSpy).toHaveBeenCalledWith(
                state.companyId,
                state.range?.start,
                state.range?.end
              );
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadHasEmptyEditionsSuccess({
                  isEmpty: true,
                })
              );
            }
          );
        }));
      });

      describe('given checkEmptyDataSpy return an error', () => {
        beforeEach(() => {
          checkEmptyDataSpy.mockReturnValue(
            throwError(() => new Error('checkEmptyDataSpy error'))
          );
        });

        it('should return loadHasEmptyEditionsError action', waitForAsync(() => {
          const state = {
            companyId: 'companyId',
            range: {
              start: new Date('2024-06-01'),
              end: new Date('2024-06-30'),
            },
          } as MonthlyTreatmentState;

          jest.spyOn(store, 'select').mockReturnValue(of(state));
          monthlyTreatmentEffects.loadHasEmptyEdition$.subscribe(
            (action: Action) => {
              expect(action).toStrictEqual(
                monthlyTreatmentActions.loadHasEmptyEditionsError()
              );
            }
          );
        }));
      });
    });
  });

  describe('encloseMonth effect', () => {
    describe('given a state where encloseMonth action is called', () => {
      beforeEach(() => {
        actions$ = of(monthlyTreatmentActions.encloseMonth());
      });

      it('should call saveFilters and encloseMonth method and return encloseMonthSuccess action', waitForAsync(() => {
        const state = {
          salesDetailsCurrentFilter: SalesDetailsOrderByEnum.BUSINESS_TYPE,
          salesByProductCurrentFilter: [
            ProductCharacteristicEnum.PRODUCT_FAMILY,
          ],
          enclosureMonth: new Date('2024-06-01'),
          companyId: 'companyId',
        } as MonthlyTreatmentState;

        jest.spyOn(store, 'select').mockReturnValue(of(state));

        const encloseMonthSpy = jest
          .spyOn(monthlyTreatmentService, 'encloseMonth')
          .mockReturnValue(of(void 0));

        const saveFiltersSpy = jest
          .spyOn(monthlyTreatmentService, 'saveFilters')
          .mockReturnValue(of({}));

        monthlyTreatmentEffects.encloseMonth$.subscribe((action: Action) => {
          expect(saveFiltersSpy).toHaveBeenCalledWith({
            salesDetailOrder: state.salesDetailsCurrentFilter,
            productCategories: state.salesByProductCurrentFilter,
          });
          expect(encloseMonthSpy).toHaveBeenCalledWith({
            companyId: state.companyId,
            month: state.enclosureMonth,
          });
          expect(action).toStrictEqual(
            monthlyTreatmentActions.encloseMonthSuccess()
          );
        });
      }));

      it('should return encloseMonthError action', waitForAsync(() => {
        jest
          .spyOn(monthlyTreatmentService, 'saveFilters')
          .mockReturnValue(of({}));

        jest
          .spyOn(monthlyTreatmentService, 'encloseMonth')
          .mockImplementation(() => {
            return throwError(() => new Error('An error occurred'));
          });

        const state = {
          enclosureMonth: new Date('2024-06-01'),
          companyId: 'companyId',
        } as MonthlyTreatmentState;

        jest.spyOn(store, 'select').mockReturnValue(of(state));
        monthlyTreatmentEffects.encloseMonth$.subscribe((action: Action) => {
          expect(action).toStrictEqual(
            monthlyTreatmentActions.encloseMonthError()
          );
        });
      }));
    });
  });

  describe('encloseDefaultMonth effect', () => {
    describe('given a state where encloseDefaultMonth action is called', () => {
      const selectedMonth = new Date('2024-06-01');
      beforeEach(() => {
        actions$ = of(
          monthlyTreatmentActions.encloseDefaultMonth({
            defaultMonth: selectedMonth,
          })
        );
      });

      it('should call saveDefaultEncloseMonth method and return encloseDefaultMonthSuccess action', waitForAsync(() => {
        const state = {
          companyId: 'companyId',
        } as MonthlyTreatmentState;

        jest.spyOn(store, 'select').mockReturnValue(of(state));

        const saveDefaultEncloseMonthSpy = jest
          .spyOn(monthlyTreatmentService, 'saveDefaultEncloseMonth')
          .mockReturnValue(of(void 0));

        monthlyTreatmentEffects.encloseDefaultMonth$.subscribe(
          (action: Action) => {
            expect(saveDefaultEncloseMonthSpy).toHaveBeenCalledWith({
              companyId: state.companyId,
              month: selectedMonth,
            });
            expect(action).toStrictEqual(
              monthlyTreatmentActions.encloseDefaultMonthSuccess({
                defaultMonth: selectedMonth,
              })
            );
          }
        );
      }));

      it('should return encloseMonthError action', waitForAsync(() => {
        const state = {
          companyId: 'companyId',
        } as MonthlyTreatmentState;

        jest.spyOn(store, 'select').mockReturnValue(of(state));

        jest
          .spyOn(monthlyTreatmentService, 'saveDefaultEncloseMonth')
          .mockImplementation(() => {
            return throwError(() => new Error('An error occurred'));
          });

        jest.spyOn(store, 'select').mockReturnValue(of(state));
        monthlyTreatmentEffects.encloseDefaultMonth$.subscribe(
          (action: Action) => {
            expect(action).toStrictEqual(
              monthlyTreatmentActions.encloseDefaultMonthError()
            );
          }
        );
      }));
    });
  });
});
