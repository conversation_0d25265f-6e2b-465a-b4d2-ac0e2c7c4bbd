import { inject, Injectable } from '@angular/core';
import { UncloseMonthPort } from '@gc/core/deb/domains/ports/unclose-month.port';
import { DebApiService } from '@gc/core/deb/infrastructure/api';

@Injectable()
export class UncloseMonthAdapter implements UncloseMonthPort {
  private readonly api = inject(DebApiService);

  for(companyId: string, month: Date) {
    return this.api.deleteClosure(companyId, month);
  }
}
