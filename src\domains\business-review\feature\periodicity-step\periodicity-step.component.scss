@use '@angular/material' as mat;
@use 'gc-material-typography' as gc-material-typography;

.button-toggle-container {
    margin-top: 2rem; /* 32px */
    text-align: center;
}

.periodicity-select-container {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 1rem; /* 16px */
    margin: 2rem 0; /* 32px */
    height: 3.5rem; /* 56px */

    .no-fiscal-years {
        display: flex;
        flex-direction: column;
    }
}

.selected-date-range-container {
    height: 1rem;
}

.text-lg {
    @include mat.m2-typography-level(
        gc-material-typography.$typography,
        'headline-6'
    );
}

/**
* Atomic CSS inspired by TailwindCSS
* @see https://tailwindcss.com
*/
.absolute {
    position: absolute;
}
