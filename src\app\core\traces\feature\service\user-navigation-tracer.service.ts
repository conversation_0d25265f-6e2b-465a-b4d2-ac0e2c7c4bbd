import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, Event, NavigationEnd } from '@angular/router';
import { ENVIRONMENT } from '@gc/environment';
import {
  TracerEventCustomPropertyKeys,
  TracerEventIdKeys,
} from '@gc/shared/models';
import {
  IMonitoringTraceContextService,
  ITraceService,
  LogLevel,
} from '@isagri-ng/core/diagnostics';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UserNavigationTracerService {
  private readonly _trace = inject(ITraceService);
  private readonly _router = inject(Router);
  private readonly _m = inject(IMonitoringTraceContextService);
  private readonly _destroyRef = inject(DestroyRef);

  init(): void {
    this._router.events
      .pipe(
        tap((navigationEvent: Event) => {
          if (navigationEvent instanceof NavigationEnd) {
            this._m.addCustomProperty(
              TracerEventCustomPropertyKeys.WINDOW,
              navigationEvent.urlAfterRedirects
            );
            this._m.addCustomProperty(
              TracerEventCustomPropertyKeys.PROJECT_VERSION,
              ENVIRONMENT.version
            );

            this._trace.logMetric(
              LogLevel.information,
              TracerEventIdKeys.USER_NAVIGATION,
              0
            );
          }
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }
}
