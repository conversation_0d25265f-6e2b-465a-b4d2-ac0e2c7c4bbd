import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {
  NonNullableFormBuilder,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { TRANSLOCO_SCOPE, TranslocoDirective } from '@jsverse/transloco';

import { DebParameters } from '@gc/core/deb/domains/models';
import { DebFacade } from '@gc/core/deb/application/facades';
import { provideDebServices } from '@gc/core/deb';
import { provideDebInfrastructure } from '@gc/core/deb/infrastructure';

const DECLARANT_NAME_MAX_LENGTH = 14;
const AUTHORIZATION_NUMBER_MAX_LENGTH = 4;
const DECLARATION_NUMBER_MIN_VALUE = 1;
@Component({
  selector: 'gc-deb-parameters-edit',
  standalone: true,
  imports: [
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatRadioModule,
    ReactiveFormsModule,
    TranslocoDirective,
  ],
  templateUrl: './deb-parameters-edit.component.html',
  styleUrl: './deb-parameters-edit.component.scss',
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/form',
      multi: true,
    },
    provideDebServices(),
    provideDebInfrastructure(),
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebParametersEditComponent {
  private readonly parameters = inject<DebParameters | undefined>(
    MAT_DIALOG_DATA
  );
  private readonly _fb = inject(NonNullableFormBuilder);
  private readonly facade = inject(DebFacade);

  readonly declarantNameMaxLength = DECLARANT_NAME_MAX_LENGTH;
  readonly authorizationNumberMaxLength = AUTHORIZATION_NUMBER_MAX_LENGTH;
  readonly declarationNumberMinValue = DECLARATION_NUMBER_MIN_VALUE;

  parametersFG = this._fb.group({
    authorizationNumber: [
      this.parameters?.authorizationNumber,
      [
        Validators.required,
        Validators.maxLength(this.authorizationNumberMaxLength),
      ],
    ],
    declarantName: [
      this.parameters?.declarantName,
      [Validators.required, Validators.maxLength(this.declarantNameMaxLength)],
    ],
    declarationNumber: [
      this.parameters?.declarationNumber,
      [
        Validators.required,
        Validators.pattern(/^\d+$/),
        Validators.min(this.declarationNumberMinValue),
      ],
    ],
    declarationType: [this.parameters?.declarationType, Validators.required],
  });

  hasMissingParameters = this.facade.hasMissingParameters(this.parameters);
}
