<div class="container" data-testid="monthly_visualization" *transloco="let t">
  <div class="wrapper">
    @if ((enclosingStatus$ | ngrxPush) !== 'IN_PROGRESS') {
      <div class="header">
        <form class="filters">
          @if (selectCompanyFC) {
            <gc-company-single-select
              [formControl]="selectCompanyFC"
              [label]="t('accounting.companies-select.label')"
              [invalid]="
                selectCompanyFC.invalid &&
                (selectCompanyFC.dirty || selectCompanyFC.touched)
              "
              [errorText]="t('sharedForm.error.required')" />
          }
          @if (rangeFormGroup) {
            <gc-monthly-treatment-range [rangeFormGroup]="rangeFormGroup" />
          }
        </form>
      </div>

      @if (rangeFormGroup) {
        <div class="body">
          <gc-sales-by-product-category-treatment-id-retriever-card
            [invalid]="rangeFormGroup.invalid || !!selectCompanyFC?.invalid"
            [isLoadingData]="isLoadingSalesByProductTreatmentId$ | ngrxPush"
            (consult)="consultSalesByProductCategory($event)"
            class="card full-width" />
          <gc-sales-details-treatment-id-retriever-card
            [invalid]="rangeFormGroup.invalid || !!selectCompanyFC?.invalid"
            [isLoadingData]="isLoadingSalesDetailsTreatmentId$ | ngrxPush"
            (consult)="consultSalesDetails($event)"
            class="card" />
          <gc-receipt-list-treatment-id-retriever-card
            [invalid]="rangeFormGroup.invalid || !!selectCompanyFC?.invalid"
            (consult)="consult(reportId.receipts, reportFamilies.RECEIPTS)"
            class="card" />
          <gc-debt-list-treatment-id-retriever-card
            [invalid]="rangeFormGroup.invalid || !!selectCompanyFC?.invalid"
            (consult)="consult(reportId.debt, reportFamilies.DEBT)"
            class="card" />
          <gc-account-entry-list-treatment-id-retriever-card
            [invalid]="rangeFormGroup.invalid || !!selectCompanyFC?.invalid"
            class="card" />
        </div>
      }

      <div class="actions-container">
        <ng-container
          *ngrxLet="{
            enclosureMonth: enclosureMonth$,
            companyId: companyId$,
          } as vm">
          @if (vm) {
            <gc-enclose-month-button
              [companyId]="vm.companyId"
              [rangeFormGroup]="rangeFormGroup" />
          }
        </ng-container>
      </div>
    } @else {
      @if (enclosureMonth$ | ngrxPush; as enclosureMonth) {
        <gc-loader
          [label]="
            t('accounting.monthly-edition-tab.enclose.ongoing', {
              encloseMonth:
                enclosureMonth
                | translocoDate: { year: 'numeric', month: 'long' }
                | capitalize,
            })
          " />
      }
    }
  </div>
</div>
