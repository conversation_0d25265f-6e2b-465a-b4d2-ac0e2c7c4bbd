import { DueDateSoldState } from '@gc/due-date/models';
import { DueDateSoldStateAdapter } from './due-date-sold-state.adapter';

describe('DueDateSoldStateAdapter', () => {
  describe('given all DueDateSoldState enum values', () => {
    describe('when transform method is called on each value', () => {
      it('should transform successfully', () => {
        const dueDateSoldStateEnumValues = Object.values(DueDateSoldState);
        const dueDateSoldStateEnumNumberValues = dueDateSoldStateEnumValues
          .filter((v) => !isNaN(Number(v)))
          .map((v) => v as DueDateSoldState);

        dueDateSoldStateEnumNumberValues.forEach((value: DueDateSoldState) =>
          expect(DueDateSoldStateAdapter.toApi(value)).not.toBeNull()
        );
      });
    });
  });
});
