import { inject, Injectable } from '@angular/core';
import { LoadCommissionDetailsPort } from '../../domains/ports';
import { Commission } from '../../domains/models';
import { Observable } from 'rxjs';
import { CommissionDetails } from '../../domains/models/commission-details.model';

@Injectable()
export class CommissionDetailsUseCase {
  private readonly loadCommissionDetails = inject(LoadCommissionDetailsPort);

  for(commission: Commission): Observable<CommissionDetails[]> {
    return this.loadCommissionDetails.for(commission);
  }
}
