@if (companiesOptions) {
  <mat-form-field
    data-testid="mat-form-field-single-company"
    appearance="outline"
    subscriptSizing="dynamic"
    class="company-select-form-field">
    @if (label && label.length) {
      <mat-label class="label"> {{ label }}</mat-label>
    }
    <mat-select
      [required]="required"
      id="{{ inputId }}"
      [ngModel]="getValue$() | async"
      (ngModelChange)="onCompaniesChanged($event)">
      @for (company of companiesOptions; track company) {
        <mat-option [value]="company.id">{{ company.code }}</mat-option>
      }
    </mat-select>
    @if (invalid) {
      <mat-error>{{ errorText }}</mat-error>
    }
  </mat-form-field>
}
