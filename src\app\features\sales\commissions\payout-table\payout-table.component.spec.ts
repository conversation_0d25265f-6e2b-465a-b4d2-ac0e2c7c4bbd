import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { PayoutTableComponent } from './payout-table.component';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockDirectives } from 'ng-mocks';
import { DialogService } from '@gc/shared/ui';
import { PayoutTablePreviewComponent } from './payout-table-actions/payout-table-preview/payout-table-preview.component';
import { Commission } from '@gc/core/sales/commissions/domains/models';
import { signal } from '@angular/core';
import { PayoutTableFormComponent } from './payout-table-actions/payout-table-form/payout-table-form.component';
import { RepresentativesFacade } from '@gc/core/sales/representatives/application';
import { CurrencyService } from '@gc/currency/data-access';

describe('PayoutTableComponent', () => {
  let spectator: Spectator<PayoutTableComponent>;
  let commissionsFacade: CommissionsFacade;
  let representativesFacade: RepresentativesFacade;
  let dialogService: DialogService;

  const mockCommissions: Commission[] = [
    {
      documentId: 'DOC1',
      documentDate: '2024-01-01',
      amount: 100,
      amountToBePaid: 50,
      status: 1,
    },
    {
      documentId: 'DOC2',
      documentDate: '2024-01-02',
      amount: 200,
      amountToBePaid: 100,
      status: 2,
    },
  ];

  const createComponent = createComponentFactory({
    component: PayoutTableComponent,
    declarations: [MockDirectives(TranslocoDirective)],
    mocks: [
      CommissionsFacade,
      DialogService,
      TranslocoService,
      RepresentativesFacade,
      CurrencyService,
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
    commissionsFacade = spectator.inject(CommissionsFacade);
    representativesFacade = spectator.inject(RepresentativesFacade);
    dialogService = spectator.inject(DialogService);

    jest
      .spyOn(commissionsFacade, 'getCommissionsForCurrentFilters')
      .mockReturnValue(
        signal({
          data: mockCommissions,
          loading: false,
          errors: undefined,
          status: 'Success',
        })
      );
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('constructor', () => {
    it('should update commissions when data changes', () => {
      jest.useFakeTimers();

      spectator = createComponent();
      jest.advanceTimersByTime(0);

      expect(spectator.component.dataSource.data).toEqual(mockCommissions);

      jest.useRealTimers();
    });
  });

  describe('onPreviewClick', () => {
    it('should stop event propagation and open dialog with correct parameters', () => {
      const [commission] = mockCommissions;
      const mockEvent = { stopPropagation: jest.fn() } as unknown as MouseEvent;
      const openSpy = jest.spyOn(dialogService, 'open');

      spectator.component.onPreviewClick(mockEvent, commission);

      expect(mockEvent.stopPropagation).toHaveBeenCalled();
      expect(openSpy).toHaveBeenCalledWith(
        PayoutTablePreviewComponent,
        { commission },
        expect.any(Object)
      );
    });
  });

  describe('onEditClick', () => {
    it('should stop event propagation and open dialog with correct parameters', () => {
      const [commission] = mockCommissions;
      const mockEvent = { stopPropagation: jest.fn() } as unknown as MouseEvent;
      const openSpy = jest.spyOn(dialogService, 'open');

      spectator.component.onEditClick(mockEvent, commission);

      expect(mockEvent.stopPropagation).toHaveBeenCalled();
      expect(openSpy).toHaveBeenCalledWith(
        PayoutTableFormComponent,
        { commission },
        expect.any(Object)
      );
    });
  });

  describe('selection methods', () => {
    beforeEach(() => {
      spectator.component.dataSource.data = mockCommissions;
    });

    describe('isAllSelected', () => {
      it('should return true when all rows are selected', () => {
        spectator.component.selection.select(...mockCommissions);
        expect(spectator.component.isAllSelected()).toBe(true);
      });

      it('should return false when not all rows are selected', () => {
        spectator.component.selection.select(mockCommissions[0]);
        expect(spectator.component.isAllSelected()).toBe(false);
      });
    });

    describe('toggleAllRows', () => {
      it('should select all rows when none are selected', () => {
        spectator.component.toggleAllRows();
        expect(spectator.component.selection.selected).toEqual(mockCommissions);
      });

      it('should deselect all rows when all are selected', () => {
        spectator.component.selection.select(...mockCommissions);
        spectator.component.toggleAllRows();
        expect(spectator.component.selection.selected).toEqual([]);
      });
    });
  });

  describe('computed values', () => {
    beforeEach(() => {
      spectator.component['updateCommissions'](mockCommissions);
    });

    it('should calculate totalCommissions correctly', () => {
      const expected = 300; // 100 + 200
      expect(spectator.component.totalCommissions()).toBe(expected);
    });

    it('should calculate totalCommissionsToBePaid correctly', () => {
      const expected = 150; // 50 + 100
      expect(spectator.component.totalCommissionsToBePaid()).toBe(expected);
    });
  });

  describe('onRowSelection', () => {
    const mockCommission: Commission = {
      documentId: '123',
      documentDate: '2024-01-01',
      amount: 100,
      amountToBePaid: 50,
      status: 1,
    };

    let selectedCommissionsChangeSpy: jest.SpyInstance;

    beforeEach(() => {
      selectedCommissionsChangeSpy = jest.spyOn(
        spectator.component.selectedCommissionsChange,
        'emit'
      );
    });

    it('should toggle the selection of the row', () => {
      const toggleSpy = jest.spyOn(spectator.component.selection, 'toggle');

      spectator.component.onRowSelection(mockCommission);

      expect(toggleSpy).toHaveBeenCalledWith(mockCommission);
    });

    it('should emit null when no rows are selected', () => {
      jest
        .spyOn(spectator.component.selection, 'toggle')
        .mockImplementation(() => {
          spectator.component.selection.clear();
        });

      spectator.component.onRowSelection(mockCommission);

      expect(selectedCommissionsChangeSpy).toHaveBeenCalledWith(null);
    });

    it('should call updateSelectionAndEmit when rows are selected', () => {
      const updateSelectionAndEmitSpy = jest.spyOn(
        spectator.component as any,
        'updateSelectionAndEmit'
      );
      jest
        .spyOn(spectator.component.selection, 'toggle')
        .mockImplementation(() => {
          spectator.component.selection.select(mockCommission);
        });

      spectator.component.onRowSelection(mockCommission);

      expect(updateSelectionAndEmitSpy).toHaveBeenCalled();
    });

    it('should emit total amount to be paid when rows are selected', () => {
      jest
        .spyOn(spectator.component.selection, 'toggle')
        .mockImplementation(() => {
          spectator.component.selection.select(mockCommission);
        });

      spectator.component.onRowSelection(mockCommission);

      expect(selectedCommissionsChangeSpy).toHaveBeenCalledWith(
        mockCommission.amountToBePaid
      );
    });
  });

  describe('toggleAllRows', () => {
    let selectedCommissionsChangeSpy: jest.SpyInstance;

    beforeEach(() => {
      spectator.component.dataSource.data = mockCommissions;
      selectedCommissionsChangeSpy = jest.spyOn(
        spectator.component.selectedCommissionsChange,
        'emit'
      );
    });

    describe('when all rows are selected', () => {
      beforeEach(() => {
        jest.spyOn(spectator.component, 'isAllSelected').mockReturnValue(true);
      });

      it('should clear selection', () => {
        const clearSpy = jest.spyOn(spectator.component.selection, 'clear');

        spectator.component.toggleAllRows();

        expect(clearSpy).toHaveBeenCalled();
      });

      it('should emit null', () => {
        spectator.component.toggleAllRows();

        expect(selectedCommissionsChangeSpy).toHaveBeenCalledWith(null);
      });
    });

    describe('when not all rows are selected', () => {
      beforeEach(() => {
        jest.spyOn(spectator.component, 'isAllSelected').mockReturnValue(false);
      });

      it('should select all rows', () => {
        const selectSpy = jest.spyOn(spectator.component.selection, 'select');

        spectator.component.toggleAllRows();

        expect(selectSpy).toHaveBeenCalledWith(...mockCommissions);
      });

      it('should call updateSelectionAndEmit', () => {
        const updateSelectionAndEmitSpy = jest.spyOn(
          spectator.component as any,
          'updateSelectionAndEmit'
        );

        spectator.component.toggleAllRows();

        expect(updateSelectionAndEmitSpy).toHaveBeenCalled();
      });

      it('should emit total amount to be paid', () => {
        const totalAmount = mockCommissions.reduce(
          (sum, commission) => sum + commission.amountToBePaid,
          0
        );

        spectator.component.toggleAllRows();

        expect(selectedCommissionsChangeSpy).toHaveBeenCalledWith(totalAmount);
      });
    });
  });
});
