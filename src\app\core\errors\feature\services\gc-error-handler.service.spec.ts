import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { GcErrorHandlerService } from './gc-error-handler.service';
import { ITraceService } from '@isagri-ng/core/diagnostics';
import { HttpErrorResponse } from '@angular/common/http';
import { ITRACE_SERVICES_STUB } from '../test/itrace-service.stub';

describe('GcErrorHandlerService', () => {
  let spectator: SpectatorService<GcErrorHandlerService>;
  let service: GcErrorHandlerService;

  const createService = createServiceFactory({
    service: GcErrorHandlerService,
    providers: [{ provide: ITraceService, useValue: ITRACE_SERVICES_STUB }],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  describe('handleError', () => {
    let logErrorExceptionSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      const traceService = spectator.inject(ITraceService);
      logErrorExceptionSpy = jest.spyOn(traceService, 'logErrorException');
    });

    afterEach(() => {
      logErrorExceptionSpy.mockClear();
    });

    describe('given a state where error is an instance of Error', () => {
      const error = new Error('test');
      it('should call logErrorException method', () => {
        service.handleError(error);
        expect(logErrorExceptionSpy).toHaveBeenCalledWith(error);
      });
    });

    describe('given a state where error is an instance of HttpErrorResponse', () => {
      const error = new HttpErrorResponse({ error: 'test' });
      it('should not call logErrorException method', () => {
        service.handleError(error);
        expect(logErrorExceptionSpy).not.toHaveBeenCalled();
      });
    });

    describe('given a state where error has another type', () => {
      const error = 'An error';
      it('should not call logErrorException method', () => {
        service.handleError(error);
        expect(logErrorExceptionSpy).not.toHaveBeenCalled();
      });
    });
  });
});
