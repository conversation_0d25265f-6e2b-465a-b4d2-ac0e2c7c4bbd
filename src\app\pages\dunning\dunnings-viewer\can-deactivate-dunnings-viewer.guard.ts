import { Injectable, inject } from '@angular/core';
import { DialogService } from '@gc/shared/ui';
import { TranslocoService } from '@jsverse/transloco';
import { Observable, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CanDeactivateDunningsViewerGuard {
  private readonly _dialogService = inject(DialogService);
  private readonly _translocoService = inject(TranslocoService);

  canDeactivate(): Observable<boolean> {
    return this._translocoService
      .selectTranslate(
        'dunning-viewer-page.leave-dunnings-viewer-message',
        {},
        'dunning'
      )
      .pipe(
        switchMap((confirmMessage: string) =>
          this._dialogService.confirm(confirmMessage)
        )
      );
  }
}
