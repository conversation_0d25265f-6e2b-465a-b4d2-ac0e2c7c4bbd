import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MonthlyTreatmentCardComponent } from '@gc/accounting/ui';
import { TranslocoDirective } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';

@Component({
  selector: 'gc-receipt-list-treatment-id-retriever-card',
  templateUrl: './receipt-list-treatment-id-retriever-card.component.html',
  standalone: true,
  imports: [
    MatCheckboxModule,
    MatIconModule,
    MonthlyTreatmentCardComponent,
    TranslocoDirective,
    PushPipe,
  ],
})
export class ReceiptListTreatmentIdRetrieverCardComponent {
  @Input() invalid!: boolean;
  @Output() consult = new EventEmitter<void>();
}
