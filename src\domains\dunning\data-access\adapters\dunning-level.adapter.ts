import {
  DueDateSaveSuccessApi,
  DunningLevelApi,
} from '@gc/shared/api/data-access';
import { DunningLevel } from '@gc/dunning/models';

export class DunningLevelAdapter {
  static fromApi(
    levelApi: DunningLevelApi | DueDateSaveSuccessApi | undefined | null
  ): DunningLevel {
    switch (levelApi) {
      case null:
      case undefined:
        return DunningLevel.NOT_EXISTING;
      case DunningLevelApi.Level1:
        return DunningLevel.LEVEL_1;
      case DunningLevelApi.Level2:
        return DunningLevel.LEVEL_2;
      case DunningLevelApi.Level3:
        return DunningLevel.LEVEL_3;
      case DunningLevelApi.Level4:
        return DunningLevel.LEVEL_4;
      default:
        throw new Error(
          `[DunningLevelAdapter] fromApi - Level inconnu : ${levelApi}`
        );
    }
  }

  static toApi(level: DunningLevel): DunningLevelApi {
    switch (level) {
      case DunningLevel.LEVEL_1:
        return DunningLevelApi.Level1;
      case DunningLevel.LEVEL_2:
        return DunningLevelApi.Level2;
      case DunningLevel.LEVEL_3:
        return DunningLevelApi.Level3;
      case DunningLevel.LEVEL_4:
        return DunningLevelApi.Level4;
      default:
        throw new Error(
          `[DunningLevelAdapter] toApi - Level inconnu : ${level}`
        );
    }
  }
}
