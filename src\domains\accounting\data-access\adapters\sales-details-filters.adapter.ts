import {
  SalesDetailsFilters,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { StimulsoftTreatmentSalesDetailsFiltersApi } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';

export class SalesDetailsFiltersAdapter {
  public static toApi(
    filters: SalesDetailsFilters
  ): StimulsoftTreatmentSalesDetailsFiltersApi {
    return {
      orderBy:
        filters.orderBy === SalesDetailsOrderByEnum.BUSINESS_TYPE ? '2' : '1',
      filters: {
        dateFrom: DateAdapter.dateToStringAPI(filters.dateFrom) ?? undefined,
        dateTo: DateAdapter.dateToStringAPI(filters.dateTo) ?? undefined,
        companyId: filters.companyId,
      },
    };
  }
}
