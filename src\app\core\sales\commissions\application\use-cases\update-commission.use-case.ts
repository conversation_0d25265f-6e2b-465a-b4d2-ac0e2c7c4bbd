import { inject, Injectable } from '@angular/core';
import { Commission } from '../../domains/models';
import { Observable } from 'rxjs';
import { UpdateCommissionPort } from '../../domains/ports/update-commission.port';

@Injectable()
export class UpdateCommissionUseCase {
  private readonly updateCommission = inject(UpdateCommissionPort);

  with(commission: Commission): Observable<Commission[]> {
    return this.updateCommission.with(commission);
  }
}
