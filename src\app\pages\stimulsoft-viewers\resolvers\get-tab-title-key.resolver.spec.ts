import { TestBed } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterModule,
  RouterStateSnapshot,
} from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { getTabTitleKeyResolver } from './get-tab-title-key.resolver';

describe('getTabTitleKeyResolver function', () => {
  let router: Router;
  let navigateSpy: jest.SpyInstance<Promise<boolean>>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterModule],
    });

    router = TestBed.inject(Router);
    navigateSpy = jest.spyOn(router, 'navigate').mockResolvedValue(true);
  });

  describe(`given a state where ActivatedRouteSnapshot has a reportFamily queryParams`, () => {
    describe(`and the queryParams value is a key of the stimulsoftReportFamilyToTabTranslationKeyMap`, () => {
      let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
      beforeEach(() => {
        activatedRouteSnapshot = {
          queryParams: {
            reportFamily: 'BRGLT',
          },
        };
      });

      it('should return the tab translation key and NOT call navigate method of Router', async () => {
        const headerTranslationKey = TestBed.runInInjectionContext(() => {
          return getTabTitleKeyResolver(
            activatedRouteSnapshot as ActivatedRouteSnapshot,
            {} as RouterStateSnapshot
          );
        });

        expect(navigateSpy).not.toHaveBeenCalled();
        expect(headerTranslationKey).toBeDefined();
      });
    });

    describe(`and the queryParams value is NOT a key of the stimulsoftReportFamilyToTabTranslationKeyMap`, () => {
      let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
      beforeEach(() => {
        activatedRouteSnapshot = {
          queryParams: {
            reportFamily: 'UNKNOWN_REPORT_FAMILY',
          },
        };
      });

      it('should navigate to invalid-config url and throw Error', async () => {
        TestBed.runInInjectionContext(() => {
          try {
            getTabTitleKeyResolver(
              activatedRouteSnapshot as ActivatedRouteSnapshot,
              {} as RouterStateSnapshot
            );
          } catch (err) {
            expect(err).toStrictEqual(
              new Error(
                'getTabTitleKeyResolver: tabTranslationKey is undefined'
              )
            );
            expect(navigateSpy).toHaveBeenCalledWith([URL_PATHS.invalidConfig]);
          }
        });
      });
    });
  });
});
