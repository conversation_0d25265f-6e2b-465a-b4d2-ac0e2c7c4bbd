import { Injectable } from '@angular/core';
import { of } from 'rxjs';
import { RepresentativeResponse } from './response';

@Injectable()
export class RepresentativesApiService {
  mockRepresentatives: RepresentativeResponse[] = [
    {
      id: '1',
      fullname: '<PERSON>',
      code: 'JD123',
      currentPage: 1,
      pageSize: 10,
    },
    {
      id: '2',
      fullname: '<PERSON>',
      code: 'JS456',
      currentPage: 1,
      pageSize: 10,
    },
  ];

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadRepresentatives(companyId: string) {
    return of(this.mockRepresentatives);
  }
}
