import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslocoDirective } from '@jsverse/transloco';
import { DraftDeclaration } from '@gc/core/deb/domains/models';
import { DebDeclarationFormComponent } from '@gc/features/deb/declaration/form/deb-declaration-form';
import { FormConfig } from '@gc/features/deb/declaration/form/models';
import { provideDebInfrastructure } from '@gc/core/deb/infrastructure';
import { provideDebServices } from '@gc/core/deb';

@Component({
  selector: 'gc-deb-declaration-form-container',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    TranslocoDirective,
    DebDeclarationFormComponent,
  ],
  providers: [provideDebServices(), provideDebInfrastructure()],
  templateUrl: './deb-declaration-form-container.component.html',
  styleUrls: ['./deb-declaration-form-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationFormContainerComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public formConfig: FormConfig,
    public dialogRef: MatDialogRef<DebDeclarationFormContainerComponent>
  ) {}

  onEdit(draftDeclaration: DraftDeclaration | null): void {
    if (!draftDeclaration) {
      this.dialogRef.close();
    }

    this.dialogRef.close(draftDeclaration);
  }
}
