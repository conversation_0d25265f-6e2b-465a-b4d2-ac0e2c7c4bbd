<gc-main-container *transloco="let t">
  <mat-tab-group
    mat-stretch-tabs="false"
    mat-align-tabs="center"
    class="gc-full-height-tabs">
    <mat-tab
      [label]="
        t('businessReview.page.navigation-tab.main-settings') | uppercase
      ">
      <gc-main-settings-stepper [(isLastStep)]="isLastStep" />

      <button
        *ngrxLet="{
          isLoading: isLoading$,
          isPreparingStimulosoftParams: isPreparingStimulosoftParams$,
        } as vm"
        mat-raised-button
        color="primary"
        [disabled]="
          businessReviewFG.invalid ||
          !isLastStep ||
          vm.isLoading ||
          vm.isPreparingStimulosoftParams
        "
        (click)="onOpenReport()">
        @if (isLastStep && (vm.isLoading || vm.isPreparingStimulosoftParams)) {
          <gc-loader />
        }
        {{ t('businessReview.main-settings-tab.generate') }}
      </button>
    </mat-tab>

    <mat-tab
      [label]="
        t('businessReview.page.navigation-tab.advanced-settings') | uppercase
      "
      [disabled]="companiesFC.invalid">
      <gc-advanced-settings />
    </mat-tab>
  </mat-tab-group>
</gc-main-container>
