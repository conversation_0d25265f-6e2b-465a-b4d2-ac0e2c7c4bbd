import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';

import { DebtListTreatmentIdRetrieverCardComponent } from './debt-list-treatment-id-retriever-card.component';

describe('DebtListTreatmentIdRetrieverCardComponent', () => {
  let spectator: Spectator<DebtListTreatmentIdRetrieverCardComponent>;
  let component: DebtListTreatmentIdRetrieverCardComponent;

  const createComponent = createComponentFactory({
    component: DebtListTreatmentIdRetrieverCardComponent,
    mocks: [TranslocoService, Store],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
