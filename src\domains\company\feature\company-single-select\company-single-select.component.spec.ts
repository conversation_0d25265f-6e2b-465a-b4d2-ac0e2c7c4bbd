import { Component } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { companiesFixture, Company } from '@gc/company/models';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { BaseControlValueAccessorTestHelper } from '@gc/shared/form/utils';
import { createHostFactory, SpectatorHost } from '@ngneat/spectator/jest';
import { Observable, of } from 'rxjs';
import { CompanyService } from '@gc/company/data-access';

@Component({
  template: ``,
})
class CustomHostComponent {
  hostId = 'host-id';
  hostLabel = 'label du Host';
  hostFC: FormControl = new FormControl('', [Validators.required]);
  hostFormGroup = new FormGroup({
    hostFC: this.hostFC,
  });
}

const mockCompanies: Company[] = companiesFixture();

describe('CompanySingleSelectComponent', () => {
  let spectatorHost: SpectatorHost<
    CompanySingleSelectComponent,
    CustomHostComponent
  >;
  let component: CompanySingleSelectComponent;

  const createHost = createHostFactory({
    component: CompanySingleSelectComponent,
    host: CustomHostComponent,
    imports: [
      MatSelectModule,
      MatFormFieldModule,
      MatCheckboxModule,
      ReactiveFormsModule,
    ],
    mocks: [CompanyService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectatorHost = createHost(
      `
                <form [formGroup]="hostFormGroup">
                    <gc-company-single-select
                        formControlName="hostFC"
                        [inputId]="hostId"
                        [label]="hostLabel"
                        [invalid]="false"
                        errorText="Ceci est une erreur"
                    ></gc-company-single-select>
                </form>
            `
    );
    ({ component } = spectatorHost);
  });

  describe('ngOnInit', () => {
    let companiesGetterSpy: jest.SpyInstance<Observable<Company[] | undefined>>;
    let handleDefaultSelectionSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      BaseControlValueAccessorTestHelper.mockBaseControlValueAccessorNgOnInit<string>(
        component,
        ''
      );

      const companyService = spectatorHost.inject(CompanyService);
      companiesGetterSpy = jest.spyOn(companyService, 'companies$', 'get');

      handleDefaultSelectionSpy = jest
        .spyOn(component, 'handleDefaultSelection')
        .mockReturnValue(void 0);
    });

    [
      {
        companies: mockCompanies,
        amountOfCompanyLabel: 'a list with more than one Company',
        shouldSetCompanyOptions: true,
        shouldCallHandleDefaultSelection: true,
      },
      {
        companies: [],
        amountOfCompanyLabel: 'an emtpy list',
        shouldSetCompanyOptions: false,
        shouldCallHandleDefaultSelection: false,
      },
    ].forEach(
      ({
        companies,
        amountOfCompanyLabel,
        shouldSetCompanyOptions,
        shouldCallHandleDefaultSelection,
      }) => {
        describe(`Given a state where companies getter of CompanyService return ${amountOfCompanyLabel}`, () => {
          beforeEach(() => {
            companiesGetterSpy.mockReturnValueOnce(of(companies));
          });

          it(`Should ${
            shouldSetCompanyOptions ? '' : 'not'
          } set companiesOptions property`, () => {
            component.ngOnInit();
            expect(component.companiesOptions).toStrictEqual(
              shouldSetCompanyOptions ? companies : undefined
            );
          });

          it(`Should ${
            shouldCallHandleDefaultSelection ? '' : 'not'
          } call handleDefaultSelection method`, () => {
            component.ngOnInit();
            shouldCallHandleDefaultSelection
              ? expect(handleDefaultSelectionSpy).toHaveBeenCalledWith(
                  companies
                )
              : expect(handleDefaultSelectionSpy).not.toHaveBeenCalled();
          });
        });
      }
    );
  });

  describe('onCompaniesChanged method', () => {
    let setValueSpy: jest.SpyInstance<void>;

    const aSelectedCompanyId = '1541515-1515-151';

    beforeEach(() => {
      setValueSpy = jest
        .spyOn(component, 'setValue')
        .mockReturnValueOnce(void 0);
    });

    it('should call setValue method with the selected Company id', () => {
      component.onCompaniesChanged(aSelectedCompanyId);
      expect(setValueSpy).toHaveBeenCalledWith(aSelectedCompanyId);
    });
  });

  describe('handleDefaultSelection method', () => {
    let resetParentSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      resetParentSpy = jest
        .spyOn(component, 'resetParent')
        .mockReturnValueOnce(void 0);
    });

    describe('given a list with 2 Company', () => {
      const companies = mockCompanies;

      it('should not call resetParent', () => {
        component.handleDefaultSelection(companies);
        expect(resetParentSpy).not.toHaveBeenCalled();
      });
    });

    describe('given a list with 1 Company', () => {
      const [company] = mockCompanies;

      it('should call resetParent with the given company id', () => {
        component.handleDefaultSelection([company]);
        expect(resetParentSpy).toHaveBeenCalledWith(company.id);
      });
    });
  });
});
