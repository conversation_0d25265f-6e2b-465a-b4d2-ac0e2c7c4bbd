import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { CommissionsApiService } from '../api';
import { Commission, CommissionDetails } from '../../domains/models';
import { CommissionDetailsResponse } from '../api/response';
import { LoadCommissionDetailsPort } from '../../domains/ports';

@Injectable()
export class LoadCommissionDetailsAdapter implements LoadCommissionDetailsPort {
  private readonly api = inject(CommissionsApiService);

  for(commission: Commission): Observable<CommissionDetails[]> {
    return this.api.loadCommissionDetails(commission).pipe(
      map((response: CommissionDetailsResponse[]) => {
        return response.map((details) => details as CommissionDetails);
      })
    );
  }
}
