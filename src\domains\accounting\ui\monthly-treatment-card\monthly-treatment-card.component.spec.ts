import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';

import { MonthlyTreatmentCardComponent } from './monthly-treatment-card.component';

describe('MonthlyTreatmentCardComponent', () => {
  let spectator: Spectator<MonthlyTreatmentCardComponent>;
  let component: MonthlyTreatmentCardComponent;

  const createComponent = createComponentFactory({
    component: MonthlyTreatmentCardComponent,
    detectChanges: false,
    mocks: [TranslocoService],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
