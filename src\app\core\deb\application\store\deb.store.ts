import { Injectable } from '@angular/core';
import { BaseStore, ResourceState } from '@gc/core/shared/store';
import {
  ClosedMonth,
  Closure,
  DebDetails,
  DebParameters,
  DraftDeclaration,
} from '@gc/core/deb/domains/models';

export enum DebStoreEnum {
  PARAMETERS = 'PARAMETERS',
  CLOSED_MONTHS = 'CLOSED_MONTHS',
  UNCLOSE_MONTH = 'UNCLOSE_MONTH',
  DECLARATIONS = 'DECLARATIONS',
  DETAILS = 'DETAILS',
  NOMENCLATURES = 'NOMENCLATURES',
  STATISTICAL_PROCEDURES = 'STATISTICAL_PROCEDURES',
  DESTINATION_COUNTRY_CODES = 'DESTINATION_COUNTRY_CODES',
  CLOSURE = 'CLOSURE',
}

export type DebStoreState = {
  [DebStoreEnum.PARAMETERS]: ResourceState<DebParameters>;
  [DebStoreEnum.CLOSED_MONTHS]: ResourceState<ClosedMonth[]>;
  [DebStoreEnum.DECLARATIONS]: ResourceState<DraftDeclaration[]>;
  [DebStoreEnum.UNCLOSE_MONTH]: ResourceState<void>;
  [DebStoreEnum.DETAILS]: ResourceState<DebDetails[]>;
  [DebStoreEnum.NOMENCLATURES]: ResourceState<string[]>;
  [DebStoreEnum.STATISTICAL_PROCEDURES]: ResourceState<string[]>;
  [DebStoreEnum.DESTINATION_COUNTRY_CODES]: ResourceState<string[]>;
  [DebStoreEnum.CLOSURE]: ResourceState<Closure>;
};

@Injectable({
  providedIn: 'root',
})
export class DebStore extends BaseStore<typeof DebStoreEnum, DebStoreState> {
  constructor() {
    super(DebStoreEnum);
  }
}
