import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { Paths, TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';
import { ProductParameterComponent } from './product-parameter.component';

export const ROUTES: Routes = [
  {
    path: '',
    redirectTo: Paths.CODE_STRUCTURE,
    pathMatch: 'full',
  },
  {
    path: '',
    component: ProductParameterComponent,
    title: TitleKeyTab.PRODUCT_PARAMETER,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.PRODUCT_PARAMETER)],
    children: [
      {
        path: Paths.CODE_STRUCTURE,
        loadChildren: () =>
          import('./code-structure/routes').then((m) => m.ROUTES),
      },
      {
        path: Paths.CODE_DESIGNATION,
        loadChildren: () =>
          import('./code-designation/routes').then((m) => m.ROUTES),
      },
    ],
  },
];
