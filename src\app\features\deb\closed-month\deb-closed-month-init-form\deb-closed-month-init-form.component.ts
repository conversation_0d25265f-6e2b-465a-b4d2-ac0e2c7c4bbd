import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
  output,
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CapitalizePipe } from '@gc/shared/ui';
import { getLastNMonths } from '@gc/shared/utils';
import { TranslocoDirective } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { ClosedMonth } from '@gc/core/deb/domains/models';
import {
  DEB_INITIAL_DECLARATION_NUMBER,
  MAXIMUM_MONTH_FROM_NOW_USER_CAN_CLOSE,
  MINIMUM_DECLARATION_NUMBER_VALUE,
} from '@gc/core/deb/application/constants';

@Component({
  selector: 'gc-deb-closed-month-init-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatDividerModule,
    TranslocoDirective,
    TranslocoDatePipe,
    CapitalizePipe,
  ],
  templateUrl: './deb-closed-month-init-form.component.html',
  styleUrls: ['./deb-closed-month-init-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebClosedMonthInitFormComponent implements OnInit {
  private readonly formBuilder = inject(FormBuilder);
  closedMonth = output<ClosedMonth | null>();
  declarationNumberFC!: FormControl<number>;
  declarationMonthFC!: FormControl<Date | null>;
  formGroup!: FormGroup<{
    declarationNumber: FormControl<number>;
    declarationMonth: FormControl<Date | null>;
  }>;
  lastSixMonths = getLastNMonths(MAXIMUM_MONTH_FROM_NOW_USER_CAN_CLOSE);
  minimumDeclarationNumberValue = MINIMUM_DECLARATION_NUMBER_VALUE;

  ngOnInit(): void {
    this.declarationNumberFC = this.formBuilder.nonNullable.control<number>(
      DEB_INITIAL_DECLARATION_NUMBER,
      [Validators.required, Validators.min(MINIMUM_DECLARATION_NUMBER_VALUE)]
    );
    this.declarationMonthFC = this.formBuilder.control<Date | null>(null, [
      Validators.required,
    ]);

    this.formGroup = this.formBuilder.group({
      declarationNumber: this.declarationNumberFC,
      declarationMonth: this.declarationMonthFC,
    });
  }

  onSubmit(): void {
    const closedMonth: ClosedMonth = {
      declarationNumber: this.declarationNumberFC.value,
      declarationMonth: this.declarationMonthFC.value!,
    };
    this.closedMonth.emit(closedMonth);
  }

  cancel(): void {
    this.closedMonth.emit(null);
  }
}
