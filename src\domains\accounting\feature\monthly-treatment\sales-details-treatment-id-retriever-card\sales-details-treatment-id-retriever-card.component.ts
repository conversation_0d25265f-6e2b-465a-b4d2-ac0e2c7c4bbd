import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import {
  monthlyTreatmentActions,
  selectDefaultFiltersLoadingStatus,
  selectSalesDetailsCurrentFilter,
} from '@gc/accounting/data-access';
import { SalesDetailsOrderByEnum } from '@gc/accounting/models';

import { MonthlyTreatmentCardComponent } from '@gc/accounting/ui';
import {
  LoaderComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import { TranslocoDirective } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { filter, Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { LoadingStatus } from '@gc/shared/models';
import { delayInProgress } from '@gc/shared/utils';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-sales-details-treatment-id-retriever-card',
  templateUrl: './sales-details-treatment-id-retriever-card.component.html',
  standalone: true,
  styleUrls: ['./sales-details-treatment-id-retriever-card.component.scss'],
  imports: [
    MatIconModule,
    MatOptionModule,
    MatSelectModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MonthlyTreatmentCardComponent,
    TranslocoDirective,
    PushPipe,
    LoaderComponent,
  ],
})
export class SalesDetailsTreatmentIdRetrieverCardComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  @Input() invalid!: boolean;
  @Input() isLoadingData?: boolean;
  @Output() consult = new EventEmitter<SalesDetailsOrderByEnum>();

  detailsFilterFC!: FormControl<boolean>;
  loadingFiltersStatus$!: Observable<LoadingStatus>;

  ngOnInit(): void {
    this.loadingFiltersStatus$ = this._store
      .select(selectDefaultFiltersLoadingStatus)
      .pipe(delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING));

    this._store
      .select(selectSalesDetailsCurrentFilter)
      .pipe(
        filter(
          (salesDetailsFilter) =>
            salesDetailsFilter !== undefined &&
            salesDetailsFilter !== this.detailsFilterFC?.value
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((salesDetailFilter: boolean) => {
        this.detailsFilterFC = new FormControl(salesDetailFilter, {
          nonNullable: true,
        });

        this.handleStoreUpdateWhenValueChange();
      });
  }

  handleStoreUpdateWhenValueChange(): void {
    this.detailsFilterFC.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((salesDetailFilter) => {
        this._store.dispatch(
          monthlyTreatmentActions.changeSalesDetailsCurrentFilter({
            filter: salesDetailFilter,
          })
        );
      });
  }

  handleVisualization(): void {
    this.consult.emit(
      this.detailsFilterFC.value
        ? SalesDetailsOrderByEnum.BUSINESS_TYPE
        : SalesDetailsOrderByEnum.INVOICES
    );
  }
}
