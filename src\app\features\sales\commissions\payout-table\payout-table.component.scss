/* stylelint-disable selector-pseudo-element-no-unknown */
.header {
  margin-top: 10px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;

  .selected-information {
    gap: 5px;
    text-align: start;
  }

  .add-commission-button {
    display: flex;
    text-align: end;
    gap: 5px;
    cursor: pointer;
    color: #626592;
  }
}

.mat-mdc-header-cell {
  color: grey;
}

.sticky-footer {
  position: sticky;
  bottom: 0;
  background: white;
  z-index: 1;
}

.footer-text {
  padding-top: 40px;
  padding-bottom: 10px;
  text-align: right;
  font-size: 0.875rem;

  .table-footer-text {
    margin-left: 30px;

    .table-footer-bold {
      font-weight: 550;
    }
  }
}

.actions-container {
  display: flex;
  align-items: center;
  justify-content: space-around;

  gc-action-container {
    cursor: pointer;
  }
}

.progress-bar-container {
  .progress-bar-amount {
    font-size: 14px;
    text-align: center;
  }

  .mat-progress-bar {
    ::ng-deep {
      .mdc-linear-progress__bar-inner {
        border-color: #69dfc4;
      }
    }
  }
}

.user-feedback-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
