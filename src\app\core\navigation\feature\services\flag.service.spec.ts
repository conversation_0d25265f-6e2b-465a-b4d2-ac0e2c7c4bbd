import { ENVIRONMENT } from '@gc/environment';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { FlagService } from './flag.service';

describe('FlagService', () => {
  let spectator: SpectatorService<FlagService>;
  const createService = createServiceFactory(FlagService);

  beforeEach(() => {
    spectator = createService();
  });

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  it('should return true when feature flag is not set', () => {
    const result = spectator.service.canAccessFeature('nonExistentFeature');
    expect(result).toBe(true);
  });

  it('should return false when feature flag is set to false', () => {
    ENVIRONMENT.featureFlags = { salesCommission: false };
    (spectator.service as any).featureFlags = new Map(
      Object.entries(ENVIRONMENT.featureFlags)
    );

    const result = spectator.service.canAccessFeature('salesCommission');
    expect(result).toBe(false);
  });

  it('should return true when feature flag is set to true', () => {
    ENVIRONMENT.featureFlags = { salesCommission: true };

    (spectator.service as any).featureFlags = new Map(
      Object.entries(ENVIRONMENT.featureFlags)
    );
    const result = spectator.service.canAccessFeature('salesCommission');
    expect(result).toBe(true);
  });
});
