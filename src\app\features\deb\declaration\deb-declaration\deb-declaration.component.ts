/* eslint-disable max-lines */
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
  Signal,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { LetDirective, PushPipe } from '@ngrx/component';
import { of, switchMap, take, tap } from 'rxjs';

import { CompanyService } from '@gc/company/data-access';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { CurrencyService } from '@gc/currency/data-access';
import {
  AUTO_SIZES_DIALOG_CONFIG,
  CapitalizePipe,
  DialogService,
  LoaderComponent,
  SnackbarService,
} from '@gc/shared/ui';
import { getNextMonth, getPreviousMonth } from '@gc/shared/utils';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  TranslocoDatePipe,
  TranslocoLocaleService,
} from '@jsverse/transloco-locale';
import { ResourceState } from '@gc/core/shared/store';
import { SharedUserFacade } from '@gc/core/shared/user/application/facades';
import { DebFacade } from '@gc/core/deb/application/facades';
import {
  ClosedMonth,
  Closure,
  DebParameters,
  DeclarationType,
  DraftDeclaration,
  Filters,
  MonthToClose,
} from '@gc/core/deb/domains/models';
import { DebClosedMonthInitFormContainerComponent } from '@gc/features/deb/closed-month/deb-closed-month-init-form-container';
import { DebDeclarationDocumentsOverviewComponent } from '@gc/features/deb/declaration/deb-declaration-documents-overview';
import { DebFiltersComponent } from '@gc/features/deb/declaration/deb-filters/deb-filters.component';
import { DebHistoryContainerComponent } from '@gc/features/deb/history';
import {
  DebParametersComponent,
  DebParametersEditComponent,
} from '@gc/features/deb/declaration/parameters';

export type MonthToCloseState = Partial<
  Pick<
    MonthToClose,
    'declarations' | 'deliveryNotesIncluded' | 'invoicesIncluded'
  >
>;

@Component({
  selector: 'gc-deb-declaration',
  standalone: true,
  imports: [
    CompanySingleSelectComponent,
    LetDirective,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    PushPipe,
    ReactiveFormsModule,
    TranslocoDirective,
    LoaderComponent,
    TranslocoDatePipe,
    CapitalizePipe,
    DebDeclarationDocumentsOverviewComponent,
    DebParametersComponent,
    DebFiltersComponent,
  ],
  templateUrl: './deb-declaration.component.html',
  styleUrl: './deb-declaration.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationComponent {
  private readonly companyService = inject(CompanyService);
  private readonly currencyService = inject(CurrencyService);
  private readonly dialogService = inject(DialogService);
  private readonly snackBarService = inject(SnackbarService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly sharedUserFacade = inject(SharedUserFacade);
  private readonly debFacade = inject(DebFacade);
  private readonly translocoService = inject(TranslocoService);
  private readonly translocoLocaleService = inject(TranslocoLocaleService);

  private readonly defaultUserCompany: Signal<ResourceState<string>>;
  private readonly closure: Signal<ResourceState<Closure>>;

  readonly areFiltersApplied = signal(false);
  readonly companyId = signal<string>('');
  readonly closedMonths: Signal<ResourceState<ClosedMonth[]>>;
  readonly parameters: Signal<ResourceState<DebParameters>>;
  readonly declarationMonth: Signal<Date | null>;
  readonly draftDeclarations: Signal<ResourceState<DraftDeclaration[]>>;
  readonly hasMissingParameters: Signal<boolean>;

  filters = signal<Filters | null>(null);

  companyIdControl = new FormControl('', {
    validators: [Validators.required],
    nonNullable: true,
  });

  // TODO : refactor when moving company select global
  singleCompanyId$ = this.companyService.companyIdIfSingleCompany$;
  currency$ = this.currencyService.getDefaultCurrency$();

  monthToClose: MonthToCloseState | null = null;

  constructor() {
    this.defaultUserCompany = this.sharedUserFacade.getCompanyForCurrentUser();
    this.parameters = this.debFacade.getParametersForCurrentCompany();
    this.closedMonths = this.debFacade.getClosedMonthsForCurrentCompany();
    this.draftDeclarations =
      this.debFacade.getDraftDeclarationsForCurrentFilters();
    this.closure = this.debFacade.getClosure();

    this.declarationMonth = this.initLastClosedMonthSignal();
    this.hasMissingParameters = this.getHasMissingParametersSignal();
    this.handleCompanyChange();
    this.updateCompanyId();
    this.handleFirstDeclaration();
    this.handleMissingParametersDialog();
    this.handleResetFiltersWhenSuccessClosure();
    this.setFilters();
  }

  onNoClosedMonthButtonClick(): void {
    this.handleFirstDeclarationDialog().pipe(take(1)).subscribe();
  }

  onParametersButtonClick(event: Event): void {
    event.stopPropagation();
    event.preventDefault();

    const parameters = this.parameters().data;

    this.openParametersDialogAndSave(parameters).pipe(take(1)).subscribe();
  }

  onHistoryButtonClick(): void {
    this.dialogService
      .open(
        DebHistoryContainerComponent,
        { companyId: this.companyId() },
        {
          ...AUTO_SIZES_DIALOG_CONFIG,
          disableClose: true,
        }
      )
      .pipe(take(1))
      .subscribe();
  }

  onApplyFilters(partialFilters: Partial<Filters>) {
    const hasMissingParameters = this.hasMissingParameters();

    this.monthToClose = {
      deliveryNotesIncluded: partialFilters.includeDeliveryNotes,
      invoicesIncluded: partialFilters.includeInvoices,
      declarations: [],
    };

    if (hasMissingParameters) {
      this.openParametersDialogAndSave(this.parameters().data)
        .pipe(take(1))
        .subscribe();
      return;
    }

    const companyId = this.companyId();
    const declarationMonth = this.declarationMonth()!;

    this.filters.set({
      ...partialFilters,
      declarationMonth,
      companyId,
    });
    this.debFacade.loadDraftDeclarationsWithFilters(this.filters()!);
    this.areFiltersApplied.set(true);
  }

  onSaveClosureButtonClick(): void {
    const formatMonthToClose = this.formatMonthToClose(
      this.declarationMonth()!
    );
    this.translocoService
      .selectTranslate(
        'statement-page.closed-month.confirm',
        { month: formatMonthToClose },
        'deb'
      )
      .pipe(
        switchMap((messageConfirm) =>
          this.dialogService.confirm(messageConfirm)
        ),
        take(1)
      )
      .subscribe((confirm) => {
        if (!confirm) {
          return;
        }
        this.saveClosure();
      });
  }

  onDeclarationsChange(declarations: DraftDeclaration[]): void {
    if (!this.monthToClose) {
      return;
    }
    this.monthToClose.declarations = declarations
      .filter((declaration) => declaration.status === 'enabled')
      .map((d) => ({
        euNomenclature: d.euNomenclature!,
        destinationCountryCode: d.destinationCountryCode!,
        invoicedAmount: d.invoicedAmount,
        statisticalProcedureCode: d.statisticalProcedureCode,
        vatNumber: d.vatNumber!,
      }));
  }

  private handleFirstDeclaration() {
    const isFirstDeclaration = computed(() => {
      const closedMonthsState = this.closedMonths();
      return (
        closedMonthsState.status === 'Success' &&
        !closedMonthsState?.data?.length
      );
    });

    effect(() => {
      if (!isFirstDeclaration()) {
        return;
      }

      this.handleFirstDeclarationDialog().pipe(take(1)).subscribe();
    });
  }

  private handleMissingParametersDialog() {
    effect(() => {
      const closedMonthsState = this.closedMonths();
      if (
        closedMonthsState.status !== 'Success' ||
        !closedMonthsState.data?.length
      ) {
        return;
      }

      const parametersState = this.parameters();
      if (parametersState.status !== 'Success' || !parametersState.data) {
        return;
      }

      if (this.hasMissingParameters()) {
        this.openParametersDialogAndSave(parametersState.data);
      }
    });
  }

  private openParametersDialogAndSave(parameters: DebParameters | undefined) {
    return this.dialogService
      .open<
        DebParametersEditComponent,
        DebParameters,
        DebParameters
      >(DebParametersEditComponent, parameters, { ...AUTO_SIZES_DIALOG_CONFIG, disableClose: true })
      .pipe(
        tap((dialogParameters) => {
          if (!dialogParameters) {
            return;
          }

          this.debFacade.saveParametersFor(dialogParameters!, this.companyId());
          this.displaySuccessSnackbar();
        }),
        take(1)
      );
  }

  private handleFirstDeclarationDialog() {
    return this.dialogService
      .open<
        DebClosedMonthInitFormContainerComponent,
        ClosedMonth,
        ClosedMonth
      >(DebClosedMonthInitFormContainerComponent, undefined, { ...AUTO_SIZES_DIALOG_CONFIG })
      .pipe(
        switchMap((startingMonthForDeclaration: ClosedMonth | undefined) => {
          if (!startingMonthForDeclaration) {
            return of(false);
          }

          const firstDeclarationToBeClosed: MonthToClose = {
            companyId: this.companyId(),
            declarationNumber: startingMonthForDeclaration.declarationNumber,
            deliveryNotesIncluded: false,
            invoicesIncluded: false,
            month: getPreviousMonth(
              startingMonthForDeclaration.declarationMonth
            ),
            archiveOnly: true,
            declarationType: DeclarationType.LIGHT,
          };

          this.debFacade.saveClosureFor(firstDeclarationToBeClosed, {
            firstDeclaration: true,
          });
          return of(true);
        })
      );
  }

  private displaySuccessSnackbar() {
    this.snackBarService.success({
      key: 'sharedSnackbar.default-message.save',
    });
  }

  private updateCompanyId() {
    const companyId = this.companyId();
    effect(() => {
      if (!companyId && this.defaultUserCompany().data) {
        this.companyIdControl.setValue(this.defaultUserCompany().data!);
      }
    });

    this.companyIdControl.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((selectedCompanyId) => {
          this.companyId.set(selectedCompanyId);
        })
      )
      .subscribe();
  }

  private initLastClosedMonthSignal() {
    return computed(() => {
      const closedMonthsData = this.closedMonths().data;
      if (!closedMonthsData || closedMonthsData.length === 0) {
        return null;
      }

      const lastDeclarationMonth = closedMonthsData.reduce((acc, curr) => {
        return !acc || curr.declarationMonth > acc.declarationMonth
          ? curr
          : acc;
      }).declarationMonth;

      return getNextMonth(lastDeclarationMonth);
    });
  }

  private saveClosure(): void {
    const declarationToBeClosed = <MonthToClose>{
      ...this.monthToClose!,
      companyId: this.companyId(),
      declarationNumber: this.parameters().data!.declarationNumber!,
      month: this.declarationMonth()!,
      archiveOnly: false,
    };

    this.debFacade.saveClosureFor(declarationToBeClosed);
  }

  private formatMonthToClose(date: Date): string {
    const formattedMonth = this.translocoLocaleService.localizeDate(
      date,
      this.translocoLocaleService.getLocale(),
      { year: 'numeric', month: 'long' }
    );

    return formattedMonth.charAt(0).toUpperCase() + formattedMonth.slice(1);
  }

  private getHasMissingParametersSignal() {
    return computed(() => {
      const params = this.parameters()?.data;
      return this.debFacade.hasMissingParameters(params);
    });
  }

  private handleCompanyChange() {
    effect(() => {
      const companyId = this.companyId();
      if (!companyId) {
        return;
      }

      setTimeout(() => {
        this.debFacade.companyHasChangedTo(companyId);
        this.areFiltersApplied.set(false);
      });
    });
  }

  private handleResetFiltersWhenSuccessClosure() {
    effect(() => {
      const closureState = this.closure();
      if (closureState.status === 'Success') {
        setTimeout(() => {
          this.areFiltersApplied.set(false);
        });
      }
    });
  }

  private setFilters() {
    effect(() => {
      const companyId = this.companyId();
      const declarationMonth = this.declarationMonth();

      if (!companyId || !declarationMonth) {
        return;
      }
      setTimeout(() => {
        this.filters.set({
          declarationMonth,
          companyId,
        });
      });
    });
  }
}
