import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';
import { provideDebInfrastructure } from '@gc/core/deb/infrastructure';
import { provideDebServices } from '@gc/core/deb';
import { DebPageComponent } from '@gc/features/deb/page/deb-page.component';

export const ROUTES: Routes = [
  {
    path: '',
    component: DebPageComponent,
    title: TitleKeyTab.DEB,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.DEB)],
    providers: [provideDebServices(), provideDebInfrastructure()],
  },
];
