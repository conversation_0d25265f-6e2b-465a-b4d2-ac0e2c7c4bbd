import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  inject,
} from '@angular/core';

import {
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { Company, CompanyId } from '@gc/company/models';
import { filter, take } from 'rxjs';
import { BaseControlValueAccessorComponent } from '@gc/shared/form/utils';
import { CompanyService } from '@gc/company/data-access';

@Component({
  selector: 'gc-company-single-select',
  templateUrl: './company-single-select.component.html',
  styleUrls: [
    './company-single-select.component.scss',
    './company-single-select-theme.component.scss',
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: CompanySingleSelectComponent,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    FormsModule,
    MatOptionModule,
    AsyncPipe,
  ],
})
export class CompanySingleSelectComponent
  extends BaseControlValueAccessorComponent<CompanyId>
  implements OnInit
{
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _companyService = inject(CompanyService);

  @Input() label!: string;
  @Input() inputId!: string;

  @Input() invalid = true;
  @Input() errorText?: string;

  companiesOptions!: Company[];

  override ngOnInit(): void {
    super.ngOnInit();

    this._companyService.companies$
      .pipe(
        filter((companies: Company[]) => companies.length > 0),
        take(1)
      )
      .subscribe((companies: Company[]) => {
        this.companiesOptions = companies;
        this._cdr.markForCheck();
        this.handleDefaultSelection(companies);
      });
  }

  onCompaniesChanged(companyId: CompanyId): void {
    this.setValue(companyId);
  }

  handleDefaultSelection(companies: Company[]): void {
    if (companies.length === 1) {
      this.resetParent(companies[0].id);
    }
  }
}
