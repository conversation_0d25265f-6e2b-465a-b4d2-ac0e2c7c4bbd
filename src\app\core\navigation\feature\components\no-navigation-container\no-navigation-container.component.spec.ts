import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { NoNavigationContainerComponent } from './no-navigation-container.component';
import { MockComponents } from 'ng-mocks';
import { HeaderComponent } from '../header/header.component';

describe('NoNavigationContainerComponent', () => {
  let spectator: Spectator<NoNavigationContainerComponent>;
  const createComponent = createComponentFactory({
    component: NoNavigationContainerComponent,
    declarations: [MockComponents(HeaderComponent)],
  });

  beforeEach(async () => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });
});
