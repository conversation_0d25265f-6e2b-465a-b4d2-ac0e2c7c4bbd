import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { BusinessReviewService } from '@gc/business-review/data-access';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { VatTableComponent } from './vat-table.component';
import { waitForAsync } from '@angular/core/testing';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { VatService } from '@gc/vat/data-access';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { BusinessReviewFormService } from '../services/business-review-form.service';

describe('VatTableComponent', () => {
  let spectator: Spectator<VatTableComponent>;
  let businessReviewFormService: BusinessReviewFormService;
  let businessReviewService: BusinessReviewService;

  const createComponent = createComponentFactory({
    component: VatTableComponent,
    imports: [MatTableDataSource, SelectionModel],
    mocks: [TranslocoService, FiscalYearService, WarehouseService, VatService],
    declarations: [MockDirective(TranslocoDirective)],
  });

  beforeEach(() => {
    spectator = createComponent();
    businessReviewFormService = spectator.inject(BusinessReviewFormService);
    businessReviewService = spectator.inject(BusinessReviewService);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('DataSource and Selection model', () => {
    describe('Given a state where the service returns a list of VATs', () => {
      const vatRates = [10, 15, 20, 25, 14.6];

      beforeEach(() => {
        businessReviewService.vatRates$$.next(vatRates);
      });

      it('should set the datasource with the list of Vat rates', waitForAsync(() => {
        expect.assertions(2);
        spectator.component.vatDatasource$.subscribe((dataSource) => {
          expect(dataSource).toBeInstanceOf(MatTableDataSource<number>);
          expect(dataSource.data).toEqual(vatRates);
        });
      }));

      it('should have a vat rate selected when the formControl value is set with a value', waitForAsync(() => {
        businessReviewFormService.vatRateFC.setValue(14.6);
        expect.assertions(1);
        spectator.component.vatDatasource$.subscribe(() => {
          expect(spectator.component.selection.selected).toEqual([14.6]);
        });
      }));

      it('should have no vat rate selected when the formControl is set to null', waitForAsync(() => {
        businessReviewFormService.vatRateFC.setValue(null);
        expect.assertions(1);
        spectator.component.vatDatasource$.subscribe(() => {
          expect(spectator.component.selection.selected).toEqual([]);
        });
      }));

      describe('then given a state where the selection is set with a value', () => {
        const initialSelection = 45.12;
        beforeEach(() => {
          spectator.component.selection.select(initialSelection);
        });

        it('should have the vat rate selected', waitForAsync(() => {
          expect(spectator.component.selection.selected).toEqual([
            initialSelection,
          ]);
        }));

        describe('then when the input selectedVatRate is set with a value', () => {
          beforeEach(() => {
            spectator.setInput('selectedVatRate', 88.88);
          });

          it('should select the new rate', waitForAsync(() => {
            expect(spectator.component.selection.selected).toEqual([88.88]);
          }));
        });

        describe('then when the input selectedVatRate is set to null', () => {
          beforeEach(() => {
            spectator.setInput('selectedVatRate', null);
          });

          it('should have no rate selected', waitForAsync(() => {
            expect(spectator.component.selection.selected).toEqual([]);
          }));
        });
      });
    });

    describe('Given a state where the service returns an empty list of VATs', () => {
      beforeEach(() => {
        businessReviewService.vatRates$$.next([]);
      });

      it('should set the datasource with no data', waitForAsync(() => {
        spectator.component.vatDatasource$.subscribe((dataSource) => {
          expect(dataSource).toBeInstanceOf(MatTableDataSource<number>);
          expect(dataSource.data).toEqual([]);
        });
      }));

      it('should have no vat rate selected', waitForAsync(() => {
        expect.assertions(1);
        spectator.component.vatDatasource$.subscribe(() => {
          expect(spectator.component.selection.selected).toEqual([]);
        });
      }));
    });
  });

  describe('toggleRow method', () => {
    let selectionChangeSpy: jest.SpyInstance;

    beforeEach(() => {
      selectionChangeSpy = jest.spyOn(
        spectator.component.selectionChange,
        'emit'
      );
    });

    describe('Given a state where the service returns a list of Vat Rates', () => {
      const vatRates = [10, 15.12, 20, 38.12, 40];
      beforeEach(() => {
        businessReviewService.vatRates$$.next(vatRates);
      });

      describe('and given a state where the new selected rate is not the current selected rate', () => {
        let event = {} as MatCheckboxChange;

        beforeEach(() => {
          spectator.component.selection.select(38.12);
          event = {
            checked: false,
            source: { checked: false },
          } as MatCheckboxChange;
          spectator.component.toggleRow(event, 10);
        });

        it('should select the new selected rate', () => {
          expect(spectator.component.selection.selected).toEqual([10]);
        });

        it('should emit the new selected rate', () => {
          expect(selectionChangeSpy).toHaveBeenCalledWith(10);
        });
      });

      describe('and given a state where the new selected rate is already the current selected rate', () => {
        let event = {} as MatCheckboxChange;

        beforeEach(() => {
          spectator.component.selection.select(10);
          event = {
            checked: false,
            source: { checked: false },
          } as MatCheckboxChange;
          spectator.component.toggleRow(event, 10);
        });

        it('should keep the rate selected', () => {
          expect(spectator.component.selection.selected).toEqual([10]);
        });

        it('should not emit agian the selected rate', () => {
          expect(selectionChangeSpy).not.toHaveBeenCalledWith(10);
        });
      });
    });
  });
});
