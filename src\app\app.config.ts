import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import angular_frFR from '@angular/common/locales/fr';
import {
  ApplicationConfig,
  ChangeDetectionStrategy,
  Component,
  importProvidersFrom,
} from '@angular/core';
import { DateAdapter, MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, TitleStrategy } from '@angular/router';
import { provideError } from '@gc/core/errors/feature';
import { TitlePageStrategyService } from '@gc/core/navigation/feature';
import { provideTraces } from '@gc/core/traces/feature';
import { ENVIRONMENT } from '@gc/environment';
import { provideAllApiConfigs } from '@gc/shared/api/data-access';
import { provideHttpErrors } from '@gc/shared/api/http-errors/feature';
import { provideStimulsoftConfiguration } from '@gc/shared/stimulsoft/feature';
import {
  BaseLanguageLocaleData,
  provideLanguage,
  withAngularLanguage,
  withDefaultConfiguration,
  withLocalesConfigured,
  withMessageFormat,
} from '@isagri-ng/core/language';
import {
  ExtendedNativeDateAdapter,
  MaterialDialogBoxComponent,
  withAngularMaterialLanguage,
} from '@isagri-ng/ui/angular-material';
import { EffectsModule } from '@ngrx/effects';

import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { ROUTES } from './app.routes';
import { provideDocumentsViewer } from '@gc/core/documents-viewer/feature';
import { provideDialogs } from '@isagri-ng/dialogs';
import {
  provideCommunication,
  withCommunicationInterceptors,
} from '@isagri-ng/communication';
import { provideConfiguration } from '@isagri-ng/core/configuration';
import { provideAuthentication } from '@isagri-ng/security/authentication/all';
import {
  ILoginMediaConfig,
  withLoginMediaConfig,
} from '@isagri-ng/security/login';
import { withLogin } from '@isagri-ng/security/advanced-components/login';
import { withForgottenPassword } from '@isagri-ng/security/forgotten-password';
import {
  withChangePasswordAdapter,
  withChangePasswordComponent,
} from '@isagri-ng/security/change-password';
import {
  ChangePasswordAdapter,
  ChangePasswordComponent,
} from '@isagri-ng/security/advanced-components/change-password';
import { ModalButton } from '@isagri-ng/core';
import { Observable, of } from 'rxjs';
import { provideSharedUserInfrastructure } from '@gc/core/shared/user/infrastructure';

const AvailableLanguages: Array<BaseLanguageLocaleData> = [
  {
    languageCultureCode: 'fr-FR',
    angularLocale: angular_frFR,
  },
];

const standaloneLoginMediaConfig = {
  appTitle: 'loginConfig.appTitle',
  logoImagePath: './assets/img/icone-533d0a76.svg',
} as ILoginMediaConfig;

@Component({
  // This line is disabled locally because it is an isagri-ng component (it is not suppose to follow the gc rule for component selector naming)
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'ing-material-dialog-box',
  template: '',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WorkaroundMaterialDialogBoxComponent extends MaterialDialogBoxComponent {
  override createDialog(): Observable<ModalButton> {
    return of(ModalButton.ok);
  }
}

const isLoginPageEnabled = ENVIRONMENT.featureFlags['authenticationLogin'];

export const APP_CONFIG: ApplicationConfig = {
  providers: [
    provideSharedUserInfrastructure(),
    provideHttpClient(
      withInterceptorsFromDi(),
      withCommunicationInterceptors()
    ),
    provideCommunication(),
    provideConfiguration(),
    isLoginPageEnabled
      ? provideAuthentication(
          withLoginMediaConfig(standaloneLoginMediaConfig),
          withLogin(),
          withForgottenPassword(),
          withChangePasswordAdapter(ChangePasswordAdapter),
          withChangePasswordComponent(ChangePasswordComponent)
        )
      : provideAuthentication(),
    provideDialogs(WorkaroundMaterialDialogBoxComponent),
    provideTraces(),
    provideError(),
    provideLanguage(
      withLocalesConfigured(AvailableLanguages, 'fr-FR'),
      withDefaultConfiguration(),
      withAngularLanguage(),
      withAngularMaterialLanguage(),
      withMessageFormat()
    ),
    provideDocumentsViewer(),
    provideAnimations(),
    ...provideAllApiConfigs(),
    provideHttpErrors(),
    importProvidersFrom(
      MatNativeDateModule,
      MatSnackBarModule,
      MatDialogModule
    ),
    importProvidersFrom(
      StoreModule.forRoot({}, {}),
      EffectsModule.forRoot([]),
      StoreDevtoolsModule.instrument({
        maxAge: 25,
        logOnly: false,
        serialize: {
          options: {
            map: true,
          },
        },
      })
    ),
    {
      provide: TitleStrategy,
      useExisting: TitlePageStrategyService,
    },
    {
      provide: DateAdapter,
      useClass: ExtendedNativeDateAdapter,
    },
    provideRouter(ROUTES),
    provideStimulsoftConfiguration(ENVIRONMENT.hostStimulsoftUrl),
  ],
};
