import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { BusinessResult } from '@isagri-ng/core';
import { HttpHelper } from '@isagri-ng/core/communication';
import {
  DocumentContext,
  IDocumentContextService,
} from '@isagri-ng/document-viewer';
import { map, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DocumentContextService implements IDocumentContextService {
  private readonly _httpClient = inject(HttpClient);

  getDocumentContext(): Observable<BusinessResult<DocumentContext>> {
    return this._httpClient
      .get('tools/v1/document-context', { observe: 'response' })
      .pipe(this.convertResponseToBusinessResult()) as unknown as Observable<
      BusinessResult<DocumentContext>
    >;
  }

  // This code has been copy/paste form isagri-ng document viewer lib
  convertResponseToBusinessResult<T>() {
    return (
      source$: Observable<HttpResponse<T>>
    ): Observable<BusinessResult<T>> => {
      return source$.pipe(
        map((response: HttpResponse<T>) => {
          return HttpHelper.getWarnings<T>(response);
        })
      );
    };
  }
}
