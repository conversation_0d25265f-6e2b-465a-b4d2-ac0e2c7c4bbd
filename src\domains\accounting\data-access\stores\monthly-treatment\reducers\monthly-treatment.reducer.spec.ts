import {
  EnclosingMonthInformations,
  MonthlyTreatmentEditionsFiltersModel,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { monthlyTreatmentActions } from '../actions/monthly-treatment.actions';
import { MonthlyTreatmentState } from '../models/monthly-treatment-state.model';
import {
  initialState,
  monthlyTreatmentReducer,
} from './monthly-treatment.reducer';

describe('MonthlyTreatmentReducer', () => {
  [
    {
      currentState: initialState,
      action: monthlyTreatmentActions.changeCompanyId({
        companyId: 'any company id',
      }),
      expectedNewState: {
        ...initialState,
        companyId: 'any company id',
      } as MonthlyTreatmentState,
      actionTitle: 'changeCompanyId',
      givenTitle: 'given an initial state and a payload with any companyId',
      shouldTitle: 'should return new state with companyId updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.changeDateRange({
        startDate: new Date('2024-07-08'),
        endDate: new Date('2024-07-31'),
      }),
      expectedNewState: {
        ...initialState,
        range: {
          start: new Date('2024-07-08'),
          end: new Date('2024-07-31'),
        },
      } as MonthlyTreatmentState,
      actionTitle: 'changeDateRange',
      givenTitle:
        'given an initial state and a payload with any startDate and endDate',
      shouldTitle: 'should return new state with startDate and endDate updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadDefaultFilters(),
      expectedNewState: {
        ...initialState,
        defaultFiltersLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      actionTitle: 'loadDefaultFilters',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with defaultFiltersLoadingStatus updated to IN_PROGRESS',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadDefaultFiltersSuccess({
        filters: {
          salesDetailOrder: SalesDetailsOrderByEnum.BUSINESS_TYPE,
          productCategories: [
            ProductCharacteristicEnum.CUSTOM_0,
            ProductCharacteristicEnum.CUSTOM_10,
          ],
        } as MonthlyTreatmentEditionsFiltersModel,
      }),
      expectedNewState: {
        ...initialState,
        defaultFiltersLoadingStatus: 'LOADED',
        salesByProductCurrentFilter: [
          ProductCharacteristicEnum.CUSTOM_0,
          ProductCharacteristicEnum.CUSTOM_10,
        ],
        salesDetailsCurrentFilter: SalesDetailsOrderByEnum.BUSINESS_TYPE,
      } as MonthlyTreatmentState,
      actionTitle: 'loadDefaultFiltersSuccess',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with salesByProductCurrentFilter, salesDetailsCurrentFilter updated and defaultFiltersLoadingStatus updated to LOADED',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadDefaultFiltersError(),
      expectedNewState: {
        ...initialState,
        defaultFiltersLoadingStatus: 'ERROR',
      } as MonthlyTreatmentState,
      actionTitle: 'loadDefaultFiltersError',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with defaultFiltersLoadingStatus updated to ERROR',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadEnclosureMonthError(),
      expectedNewState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'ERROR',
      } as MonthlyTreatmentState,
      actionTitle: 'loadEnclosureMonthError',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosureMonthLoadingStatus updated to ERROR',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.changeSalesByProductCurrentFilter({
        filter: [
          ProductCharacteristicEnum.CUSTOM_0,
          ProductCharacteristicEnum.CUSTOM_10,
        ],
      }),
      expectedNewState: {
        ...initialState,
        salesByProductCurrentFilter: [
          ProductCharacteristicEnum.CUSTOM_0,
          ProductCharacteristicEnum.CUSTOM_10,
        ],
      } as MonthlyTreatmentState,
      actionTitle: 'changeSalesByProductCurrentFilter',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with salesByProductCurrentFilter updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.changeSalesDetailsCurrentFilter({
        filter: true,
      }),
      expectedNewState: {
        ...initialState,
        salesDetailsCurrentFilter: SalesDetailsOrderByEnum.BUSINESS_TYPE,
      } as MonthlyTreatmentState,
      actionTitle: 'changeSalesDetailsCurrentFilter',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with salesDetailsCurrentFilter updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadEnclosureMonth({
        companyId: 'an id',
      }),
      expectedNewState: {
        ...initialState,
        enclosureMonthLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      actionTitle: 'loadEnclosureMonth',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosureMonthLoadingStatus set to IN_PROGRESS',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadEnclosureMonthSuccess({
        enclosingMonthInformations: {
          enclosureMonth: new Date('2024-01-30'),
          hasAlreadyEnclosedMonths: true,
        } as EnclosingMonthInformations,
      }),
      expectedNewState: {
        ...initialState,
        enclosureMonth: new Date('2024-01-30'),
        hasAlreadyEnclosedMonths: true,
        enclosureMonthLoadingStatus: 'LOADED',
        range: {
          start: expect.any(Date),
          end: expect.any(Date),
        },
      } as MonthlyTreatmentState,
      actionTitle: 'loadEnclosureMonthSuccess',
      givenTitle:
        'given an state with enclosureMonth and hasAlreadyEnclosedMonths to true',
      shouldTitle:
        'should return new state with enclosureMonth updated and enclosureMonthLoadingStatus updated to LOADED',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadEnclosureMonthSuccess({
        enclosingMonthInformations: {
          enclosureMonth: new Date('2024-01-30'),
          hasAlreadyEnclosedMonths: false,
        } as EnclosingMonthInformations,
      }),
      expectedNewState: {
        ...initialState,
        enclosureMonth: new Date('2024-01-30'),
        hasAlreadyEnclosedMonths: false,
        enclosureMonthLoadingStatus: 'LOADED',
        range: {
          start: expect.any(Date),
          end: expect.any(Date),
        },
      } as MonthlyTreatmentState,
      actionTitle: 'loadEnclosureMonthSuccess',
      givenTitle:
        'given an state with enclosureMonth and hasAlreadyEnclosedMonths to false',
      shouldTitle:
        'should return new state with enclosureMonth updated and enclosureMonthLoadingStatus updated to LOADED',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadHasEmptyEdition(),
      expectedNewState: {
        ...initialState,
        emptyEditionsLoadingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      actionTitle: 'loadHasEmptyEdition',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with emptyEditionsLoadingStatus updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadHasEmptyEditionsSuccess({
        isEmpty: true,
      }),
      expectedNewState: {
        ...initialState,
        emptyEditions: true,
        emptyEditionsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentState,
      actionTitle: 'loadHasEmptyEditionsSuccess',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with emptyEditions and emptyEditionsLoadingStatus updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.loadHasEmptyEditionsError(),
      expectedNewState: {
        ...initialState,
        emptyEditionsLoadingStatus: 'ERROR',
      } as MonthlyTreatmentState,
      actionTitle: 'loadHasEmptyEditionsError',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with emptyEditionsLoadingStatus updated',
    },
    {
      currentState: initialState,
      action: monthlyTreatmentActions.resetEmptyEditions(),
      expectedNewState: {
        ...initialState,
        emptyEditions: undefined,
        emptyEditionsLoadingStatus: 'NOT_LOADED',
      } as MonthlyTreatmentState,
      actionTitle: 'resetEmptyEditions',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with emptyEditions and emptyEditionsLoadingStatus updated',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.encloseMonth(),
      expectedNewState: {
        ...initialState,
        enclosingStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      actionTitle: 'encloseMonth',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosingStatus set to IN_PROGRESS',
    },
    {
      currentState: {
        ...initialState,
        enclosureMonth: new Date('2024-06-01'),
      },
      action: monthlyTreatmentActions.encloseMonthSuccess(),
      expectedNewState: {
        ...initialState,
        enclosingStatus: 'DONE',
        enclosureMonth: new Date('2024-06-30T22:00:00.000Z'),
        range: {
          start: new Date('2024-06-30T22:00:00.000Z'),
          end: new Date('2024-07-31T21:59:59.999Z'),
        },
        lastEnclosedMonth: new Date('2024-06-01'),
      } as MonthlyTreatmentState,
      actionTitle: 'encloseMonthSuccess',
      givenTitle: 'given an initial state',
      shouldTitle: 'should return new state with enclosure month inc of 1',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.encloseMonthError(),
      expectedNewState: {
        ...initialState,
        enclosingStatus: 'ERROR',
      } as MonthlyTreatmentState,
      actionTitle: 'encloseMonthError',
      givenTitle: 'given an initial state',
      shouldTitle: 'should return new state with enclosingStatus set to ERROR',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.resetEnclosingStatus(),
      expectedNewState: {
        ...initialState,
        enclosingStatus: 'NOT_PROCESSED',
      } as MonthlyTreatmentState,
      actionTitle: 'resetEnclosingStatus',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosingStatus set to NOT_PROCESSED',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.encloseDefaultMonth({
        defaultMonth: new Date('2024-07-01'),
      }),
      expectedNewState: {
        ...initialState,
        enclosingDefaultStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentState,
      actionTitle: 'encloseDefaultMonth',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosingDefaultStatus set to IN_PROGRESS',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.encloseDefaultMonthSuccess({
        defaultMonth: new Date('2024-07-01'),
      }),
      expectedNewState: {
        ...initialState,
        enclosingDefaultStatus: 'NOT_PROCESSED',
        enclosureMonth: new Date('2024-07-31T22:00:00.000Z'),
        hasAlreadyEnclosedMonths: true,
        range: {
          start: new Date('2024-07-31T22:00:00.000Z'),
          end: new Date('2024-08-31T21:59:59.999Z'),
        },
      } as MonthlyTreatmentState,
      actionTitle: 'encloseDefaultMonthSuccess',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosingDefaultStatus set to NOT_PROCESSED',
    },
    {
      currentState: {
        ...initialState,
      },
      action: monthlyTreatmentActions.encloseDefaultMonthError(),
      expectedNewState: {
        ...initialState,
        enclosingDefaultStatus: 'ERROR',
      } as MonthlyTreatmentState,
      actionTitle: 'encloseDefaultMonthError',
      givenTitle: 'given an initial state',
      shouldTitle:
        'should return new state with enclosingDefaultStatus set to error',
    },
  ].forEach(
    ({
      currentState,
      action,
      expectedNewState,
      actionTitle,
      givenTitle,
      shouldTitle,
    }) => {
      describe(`${actionTitle} action handling`, () => {
        describe(`${givenTitle}`, () => {
          it(`${shouldTitle}`, () => {
            const newState = monthlyTreatmentReducer(currentState, action);

            expect(newState).toStrictEqual(expectedNewState);
          });
        });
      });
    }
  );
});
