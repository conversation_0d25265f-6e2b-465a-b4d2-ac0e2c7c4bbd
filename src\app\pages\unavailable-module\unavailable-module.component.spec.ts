import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { UnavailableModuleComponent } from './unavailable-module.component';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';

describe('UnavailableModuleComponent', () => {
  let spectator: Spectator<UnavailableModuleComponent>;
  const createComponent = createComponentFactory({
    component: UnavailableModuleComponent,
    declarations: [MockDirective(TranslocoDirective)],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });
});
