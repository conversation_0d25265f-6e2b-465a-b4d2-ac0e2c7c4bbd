import { Injectable, inject } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { RouterStateSnapshot, TitleStrategy } from '@angular/router';
import { TitleKeyTab } from '@gc/core/navigation/models';
import { TranslocoService } from '@jsverse/transloco';
import { of, switchMap, take } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TitlePageStrategyService extends TitleStrategy {
  private readonly _translocoService = inject(TranslocoService);
  private readonly _title = inject(Title);

  override updateTitle(routerState: RouterStateSnapshot): void {
    const closeAfterGetTitlePage = 1;
    const providedTitle = this.buildTitle(routerState);

    if (providedTitle) {
      of(this._isTranslocoKeyTitleKeyTab(providedTitle))
        .pipe(
          switchMap((isTranslocoKeyTitleKeyTab: boolean) => {
            if (isTranslocoKeyTitleKeyTab) {
              return this._translocoService.selectTranslate(providedTitle);
            } else {
              return of(providedTitle);
            }
          }),
          take(closeAfterGetTitlePage)
        )
        .subscribe((title) => {
          this._title.setTitle(title);
        });
    }
  }

  private _isTranslocoKeyTitleKeyTab(searchedValue: string): boolean {
    return Object.values(TitleKeyTab).includes(searchedValue as TitleKeyTab);
  }
}
