import { waitForAsync } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { MonthlyTreatmentService } from '@gc/accounting/data-access';
import { DateAdapter } from '@gc/shared/utils';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { Store } from '@ngrx/store';
import { MockDirective, MockPipe } from 'ng-mocks';
import { DefaultEnclosureMonthSelectionDialogComponent } from './default-enclosure-month-selection-dialog.component';
import { Observable, of } from 'rxjs';
import { FormControl } from '@angular/forms';

describe('DefaultEnclosureMonthSelectionDialogComponent', () => {
  let spectator: Spectator<DefaultEnclosureMonthSelectionDialogComponent>;
  let component: DefaultEnclosureMonthSelectionDialogComponent;
  let store: Store;
  let monthlyTreatmentService: MonthlyTreatmentService;
  let dialogRef: MatDialogRef<DefaultEnclosureMonthSelectionDialogComponent>;

  const createComponent = createComponentFactory({
    component: DefaultEnclosureMonthSelectionDialogComponent,
    declarations: [
      MockDirective(TranslocoDirective),
      MockPipe(TranslocoDatePipe),
    ],
    mocks: [Store, TranslocoService, MatDialogRef, MonthlyTreatmentService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    store = spectator.inject(Store);
    dialogRef = spectator.inject(MatDialogRef);
    monthlyTreatmentService = spectator.inject(MonthlyTreatmentService);
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    describe('given a selector selectMonthlyTreatmentCompanyId who return a companyId', () => {
      let selectSpy: jest.SpyInstance;
      const companyIdMock = 'companyId';

      beforeEach(() => {
        selectSpy = jest.spyOn(store, 'select');
        selectSpy.mockReturnValueOnce(of(companyIdMock));
      });

      describe('and getAvailableStartMonths who return dates of availableStartMonths', () => {
        let getAvailableStartMonthsSpy: jest.SpyInstance<Observable<Date[]>>;
        const dateStartClosureMonth1 = DateAdapter.dateFromStringAPI('2024-06');
        const dateStartClosureMonth2 = DateAdapter.dateFromStringAPI('2024-07');

        const datesStartClosureMonth: Date[] = [
          dateStartClosureMonth1!,
          dateStartClosureMonth2!,
        ];

        beforeEach(() => {
          getAvailableStartMonthsSpy = jest.spyOn(
            monthlyTreatmentService,
            'getAvailableStartMonths'
          );

          getAvailableStartMonthsSpy.mockReturnValue(
            of(datesStartClosureMonth)
          );
        });

        it('should return a list of availableStartMonth', waitForAsync(() => {
          const expectedDatesStartClosureMonth = [...datesStartClosureMonth];

          spectator.detectChanges();

          expect(component.availableStartMonths).toEqual(
            expectedDatesStartClosureMonth
          );
        }));

        it('should setValue to selectedDefaultMonthsFC', waitForAsync(() => {
          const expectedDateStartClosureMonth = dateStartClosureMonth1;

          spectator.detectChanges();

          expect(component.selectedDefaultMonthsFC.value).toStrictEqual(
            expectedDateStartClosureMonth
          );
        }));
      });
    });
  });

  describe('onSelect', () => {
    it('should close the dialog with the selected default month value', () => {
      const selectedDefaultMonth = new Date('2024-08-15');
      const expectedSelectedDefaultMonth = new Date('2024-07-15');
      component.selectedDefaultMonthsFC = new FormControl(
        selectedDefaultMonth,
        { nonNullable: true }
      );

      const dialogRefCloseSpy = jest.spyOn(dialogRef, 'close');

      component.onSelect();

      expect(dialogRefCloseSpy).toHaveBeenCalledWith(
        expectedSelectedDefaultMonth
      );
    });
  });
});
