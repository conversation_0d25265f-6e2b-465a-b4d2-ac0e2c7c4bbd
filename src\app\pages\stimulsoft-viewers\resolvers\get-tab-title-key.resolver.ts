import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  ResolveFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { ReportFamilies } from '@gc/shared/stimulsoft/models';
import { STIMULSOFT_REPORT_FAMILY_TO_TAB_TRANSLATION_KEY_MAP } from '../constants/stimulsoft-viewer-translation-keys-from-report-family.constant';

export const getTabTitleKeyResolver: ResolveFn<string> = (
  route: ActivatedRouteSnapshot,
  _state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const reportFamily: ReportFamilies = route.queryParams[
    StimulsoftQueryParamsKeys.REPORT_FAMILY
  ] as ReportFamilies;

  const tabTranslationKey =
    STIMULSOFT_REPORT_FAMILY_TO_TAB_TRANSLATION_KEY_MAP.get(reportFamily);

  if (!tabTranslationKey) {
    router.navigate([URL_PATHS.invalidConfig]);
    throw new Error('getTabTitleKeyResolver: tabTranslationKey is undefined');
  }

  return tabTranslationKey;
};
