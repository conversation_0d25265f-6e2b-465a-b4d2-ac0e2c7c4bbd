import {
  MonthlyTreatmentEditionsFiltersModel,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnumAdapter } from '@gc/product/data-access';
import { ProductCharacteristicEnum } from '@gc/product/models';
import {
  MonthlyAccountingReportsCategorySalesKindApi,
  MonthlyAccountingReportsDefaultParameterApi,
} from '@gc/shared/api/data-access';

export class MonthlyTreatmentEditionsFiltersAdapter {
  public static toApi(
    monthlyFilters: MonthlyTreatmentEditionsFiltersModel
  ): MonthlyAccountingReportsDefaultParameterApi {
    const { salesDetailOrder, productCategories } = monthlyFilters;
    const salesDetailOrderByBusinessType =
      salesDetailOrder === SalesDetailsOrderByEnum.BUSINESS_TYPE;

    const productCategorySales = productCategories
      ?.filter(
        (
          productCharacteristic
        ): productCharacteristic is ProductCharacteristicEnum =>
          productCharacteristic !== null
      )
      .map((productCharacteristic) => {
        return ProductCharacteristicEnumAdapter.toApi(productCharacteristic);
      }) as unknown as MonthlyAccountingReportsCategorySalesKindApi[];

    return {
      salesDetailOrderByBusinessType,
      productCategorySales,
    };
  }

  public static fromApi(
    filterApi?: MonthlyAccountingReportsDefaultParameterApi
  ): MonthlyTreatmentEditionsFiltersModel {
    const salesDetailOrderChoice = filterApi?.salesDetailOrderByBusinessType
      ? SalesDetailsOrderByEnum.BUSINESS_TYPE
      : SalesDetailsOrderByEnum.INVOICES;
    return {
      productCategories: filterApi?.productCategorySales?.map(
        (productCharacteristicEnumApi) =>
          productCharacteristicEnumApi === null ||
          productCharacteristicEnumApi === undefined
            ? null
            : ProductCharacteristicEnumAdapter.fromApi(
                productCharacteristicEnumApi
              )
      ),
      salesDetailOrder:
        filterApi?.salesDetailOrderByBusinessType === undefined
          ? undefined
          : salesDetailOrderChoice,
    };
  }
}
