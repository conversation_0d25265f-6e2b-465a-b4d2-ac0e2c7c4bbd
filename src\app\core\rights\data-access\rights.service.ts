import { inject, Injectable } from '@angular/core';
import {
  LicenseDTOApi,
  LicensesApiService,
  LicenseTypeApi,
} from '@gc/shared/api/data-access';
import { LoadingStatus } from '@gc/shared/models';
import {
  BehaviorSubject,
  filter,
  map,
  Observable,
  switchMap,
  take,
} from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RightsService {
  private readonly _licensesApiService = inject(LicensesApiService);
  private readonly _isViti$$ = new BehaviorSubject<boolean | null>(null);
  private readonly _isVitiLoadingStatus$$ = new BehaviorSubject<LoadingStatus>(
    'NOT_LOADED'
  );

  get isVitiLoadingStatus$(): Observable<LoadingStatus> {
    return this._isVitiLoadingStatus$$.asObservable();
  }

  get isViti$(): Observable<boolean> {
    if (this._isVitiLoadingStatus$$.value === 'NOT_LOADED') {
      this._loadIsViti();
    }

    return this.isVitiLoadingStatus$.pipe(
      filter((status: LoadingStatus) => status === 'LOADED'),
      switchMap(() => this._isViti$$.asObservable()),
      filter((isViti) => isViti !== null)
    ) as Observable<boolean>;
  }

  private _loadIsViti(): void {
    this._isVitiLoadingStatus$$.next('IN_PROGRESS');

    this._licensesApiService
      .getLicense()
      .pipe(
        take(1),
        map((licence: LicenseDTOApi) => {
          return licence.licenseType === LicenseTypeApi.Vigne;
        })
      )
      .subscribe({
        next: (isViti: boolean) => {
          this._isViti$$.next(isViti);
          this._isVitiLoadingStatus$$.next('LOADED');
        },
        error: () => {
          this._isVitiLoadingStatus$$.next('ERROR');
        },
      });
  }
}
