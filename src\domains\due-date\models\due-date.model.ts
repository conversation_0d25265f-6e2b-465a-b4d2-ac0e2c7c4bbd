import { DueDateDocumentType } from './due-date-document-type.model';

export interface DueDate {
  /**
   * Unique identifier of the due date.
   */
  id: string;
  /**
   * Date of the due date
   */
  date: Date;
  /**
   * Balance of the due date.
   */
  balance?: number;
  /**
   * Document Id of the due date.
   */
  documentId?: string;
  /**
   * Number of the document of the due date.
   */
  documentNumber?: string;
  /**
   * Date of the document.
   */
  documentDate: Date;
  /**
   * Type of the document. (0-Unknown, 1-Invoice)
   */
  documentType?: DueDateDocumentType;
  /**
   * Amount (VAT included) of the document.
   */
  documentAmount?: number;
  /**
   * Unique identifier of the customer of the document.
   */
  customerDocumentId?: string;
  /**
   * Code of the customer of the document.
   */
  customerDocumentCode?: string;
  /**
   * Amount Percentage of the due date
   */
  rate?: number;
  /**
   * Amount of due date.
   */
  dueDateAmount?: number;
  /**
   * Type of payment.
   */
  paymentTypeId?: number;
  /**
   * Billing company\'s code
   */
  companyCode?: string;
}
