<ng-container *transloco="let t">
  <h3 class="text-lg">
    {{ t('businessReview.main-settings-tab.periodicity.title') }}
  </h3>

  <p data-testid="periodicity-description">
    {{ t('businessReview.main-settings-tab.periodicity.description') }}
  </p>

  <ng-container *ngrxLet="fiscalYearsOptions$ as fiscalYearsOptions">
    <div class="button-toggle-container" data-testid="toggle-container">
      <mat-button-toggle-group
        [formControl]="periodicityKindFC"
        hideSingleSelectionIndicator="true">
        <mat-button-toggle value="calendarYear">
          {{
            t(
              'businessReview.main-settings-tab.periodicity.calendar-year.title'
            )
          }}
        </mat-button-toggle>
        <mat-button-toggle value="fiscalYear">
          {{
            t('businessReview.main-settings-tab.periodicity.fiscal-year.title')
          }}
        </mat-button-toggle>
      </mat-button-toggle-group>

      <div
        class="periodicity-select-container"
        data-testid="select-periodicity">
        @if (periodicityKind === PeriodicityKind.CALENDAR_YEAR) {
          <span>
            {{
              t(
                'businessReview.main-settings-tab.periodicity.calendar-year.description'
              )
            }}
            :
          </span>
          <mat-form-field>
            <mat-select [formControl]="calendarYearFC">
              @for (date of calendarYearsOptions; track date) {
                <mat-option [value]="date">
                  {{ date.viewValue }}
                </mat-option>
              }
            </mat-select>
          </mat-form-field>
        } @else if (periodicityKind === PeriodicityKind.FISCAL_YEAR) {
          @if (isLoading$ | ngrxPush) {
            <gc-loader />
          } @else if (fiscalYearsOptions.length > 0) {
            <span data-testid="fiscal-year-description">
              {{
                t(
                  'businessReview.main-settings-tab.periodicity.fiscal-year.description'
                )
              }}
              :
            </span>

            <mat-form-field
              class="gc-form-field-large"
              subscriptSizing="dynamic">
              <mat-select [formControl]="fiscalYearFC">
                @for (fiscalYear of fiscalYearsOptions; track fiscalYear) {
                  <mat-option [value]="fiscalYear">
                    {{
                      t('businessReview.main-settings-tab.from-to', {
                        startDate: fiscalYear.dateFrom.toLocaleDateString(),
                        endDate: fiscalYear.dateTo.toLocaleDateString(),
                      })
                    }}
                  </mat-option>
                }
              </mat-select>
            </mat-form-field>
          } @else {
            <div class="no-fiscal-years">
              <a
                href="javascript:void(0);"
                [style.color]="'inherit'"
                [style.text-decoration]="'none'"
                data-testid="no-fiscal-years"
                (click)="
                  periodicityKindFC.setValue(PeriodicityKind.CALENDAR_YEAR)
                ">
                {{
                  t(
                    'businessReview.main-settings-tab.periodicity.fiscal-year.no-fiscal-years'
                  )
                }}
              </a>
              <div>
                <button
                  mat-button
                  (click)="
                    periodicityKindFC.setValue(PeriodicityKind.CALENDAR_YEAR)
                  ">
                  {{
                    t(
                      'businessReview.main-settings-tab.periodicity.fiscal-year.to-calendar-year-button-label'
                    )
                  }}
                </button>
              </div>
            </div>
          }
        }
      </div>
    </div>
    <p class="absolute" data-testid="year-selected">
      <strong>
        @if (periodicityKind === PeriodicityKind.CALENDAR_YEAR) {
          {{
            t(
              'businessReview.main-settings-tab.periodicity.calendar-year.title'
            ) +
              ' ' +
              t(
                'businessReview.main-settings-tab.periodicity.calendar-year.selected'
              )
          }}
        } @else if (
          periodicityKind === PeriodicityKind.FISCAL_YEAR &&
          fiscalYearsOptions.length > 0
        ) {
          {{
            t(
              'businessReview.main-settings-tab.periodicity.fiscal-year.title'
            ) +
              ' ' +
              t(
                'businessReview.main-settings-tab.periodicity.fiscal-year.selected'
              )
          }}
        }
      </strong>
      <span *ngrxLet="selectedDateRange$ as dateRange">
        @if (periodicityFG.valid) {
          :
          {{
            t('businessReview.main-settings-tab.from-to', {
              startDate: dateRange.startDate | translocoDate,
              endDate: dateRange.endDate | translocoDate,
            })
          }}
        }
      </span>
    </p>
  </ng-container>
</ng-container>
