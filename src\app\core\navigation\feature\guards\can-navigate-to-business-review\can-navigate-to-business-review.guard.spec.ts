import { TestBed, waitForAsync } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { canNavigateToBusinessReview } from './can-navigate-to-business-review.guard';
import { NavigationRightsService } from '../../services/navigation-rights.service';
import { Observable, of } from 'rxjs';
import { URL_PATHS } from '@gc/core/navigation/models';
import { MockProviders } from 'ng-mocks';

describe('canNavigateToBusinessReview', () => {
  const route: ActivatedRouteSnapshot = {} as ActivatedRouteSnapshot;
  const state: RouterStateSnapshot = {} as RouterStateSnapshot;

  let canAccessBusinessReviewSpy: jest.SpyInstance<Observable<boolean>>;
  let navigateSpy: jest.SpyInstance<Promise<boolean>>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [MockProviders(NavigationRightsService, Router)],
    });

    const navigationRightsService = TestBed.inject(NavigationRightsService);
    const router = TestBed.inject(Router);

    canAccessBusinessReviewSpy = jest.spyOn(
      navigationRightsService,
      'canAccessBusinessReview'
    );
    navigateSpy = jest.spyOn(router, 'navigate').mockResolvedValue(true);
  });

  describe('given a state where canAccessBusinessReview method return an Observable of true', () => {
    beforeEach(() => {
      canAccessBusinessReviewSpy.mockReturnValue(of(true));
    });

    it('should return true', waitForAsync(() => {
      TestBed.runInInjectionContext(() =>
        (
          canNavigateToBusinessReview(route, state) as Observable<boolean>
        ).subscribe((canNavigate) => {
          expect(canNavigate).toBe(true);
        })
      );
    }));
  });

  describe('given a state where canAccessBusinessReview method return an Observable of false', () => {
    beforeEach(() => {
      canAccessBusinessReviewSpy.mockReturnValue(of(false));
    });

    it('should return false and navigate to unavailable-module url', waitForAsync(() => {
      TestBed.runInInjectionContext(() =>
        (
          canNavigateToBusinessReview(route, state) as Observable<boolean>
        ).subscribe((canNavigate) => {
          expect(canNavigate).toBe(false);
          expect(navigateSpy).toHaveBeenCalledWith([
            URL_PATHS.unavailableModule,
          ]);
        })
      );
    }));
  });
});
