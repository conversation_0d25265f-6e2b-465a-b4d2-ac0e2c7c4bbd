-   [Ergonomie](#ergonomie)
    -   [Gestion du thème : couleur et typographie](#gestion-du-thème--couleur-et-typographie)
        -   [Les couleurs](#les-couleurs)
            -   [Appliquer une couleur sur un composant Angular Material](#appliquer-une-couleur-sur-un-composant-angular-material)
            -   [Appliquer une couleur sur nos composants](#appliquer-une-couleur-sur-nos-composants)
        -   [Les typographies](#les-typographies)
            -   [Overrider les typographies proposées par material](#overrider-les-typographies-proposées-par-material)
            -   [Appliquer une typographie sur un composant Angular Material](#appliquer-une-typographie-sur-un-composant-angular-material)
            -   [Appliquer une typographie sur nos composants](#appliquer-une-typographie-sur-nos-composants)

# Ergonomie

L'objectif est de suivre les guidelines Material lors de la conception de nos écrans.

## Gestion du thème : couleur et typographie

Nous utilisons les composants graphiques (bouton, popup, liste déroulante ...) de la librairie Angular Material pour construire nos différents écrans.

En terme de customisation de ces composants graphiques, nous avons la main sur les couleurs et la typographie.

> :bulb: Les fichiers permettant d'agir sur les thèmes de l'application sont dans le dossier `/styles`.

> :bulb: Plus d'information sur : <https://material.angular.io/guides>

### Les couleurs

Les différentes couleurs utilisables dans l'application sont définies dans le fichier suivant :

-   `_gc-colors.scss`

> :bulb: Pour plus d'informations, se renseigner sur la notion de palette : <https://material.angular.io/guide/theming#palettes>

Il existe 3 palettes différentes :

-   `Primary` : la couleur primaire et ses dérivées (définie par l'équipe outils)
-   `Accent` : la couleur secondaire et ses dérivées (définie par l'équipe outils)
-   `Warn` : la couleur d'erreur et ses dérivées (définie par défaut d'angular material)

#### Appliquer une couleur sur un composant Angular Material

La configuration des couleurs pour les composants Angular Material est faites dans les fichiers suivants :

-   `_gc-material-theme.scss`
-   `styles.scss`

Chaque composant Angular Material possède une propriété color qui accepte les valeurs `primary`,`accent` et `warn`.

Exemple :

```html
<mat-toolbar color="primary"></mat-toolbar>
```

#### Appliquer une couleur sur nos composants

Afin d'améliorer la lisibilité des fichiers scss de nos composants, la gestion du theming (et donc des couleurs) est fait dans un fichier à part : `component_name-theme.component.scss`

Exemple :

```scss
(1)
@use 'sass:map';
@use '@angular/material' as mat;
@use '_gc-material-theme' as gc-material-theme;

$color-config: mat.get-color-config(gc-material-theme.$theme); (2)
$secondary-palette: map.get($color-config, 'accent'); (3)

.my_container {
    background-color: mat.get-color-from-palette($secondary-palette, 'default'); (4)

    .my_item {
        color: mat.get-color-from-palette(
            $secondary-palette,
            'default-contrast'
        ); (5)
```

-   (1) on importe les fichiers scss qui contiennent les variables scss de couleurs
-   (2) on récupère la config des couleurs à partir du thème de l'application
-   (3) on récupère la palette de couleur que l'on souhaite : les choix possibles sont `primary`,`accent` et `warn`
-   (4) et (5) on applique la couleur à une propriété scss. Pour chacunes des palettes, les choix possibles sont `default`,`darker`, `lighter` et `default-contrast`

### Les typographies

Les différentes typographies utilisables dans l'application sont définies dans le fichier suivant :

-   `_gc-material-typography.scss`

> :bulb: A noter que nous utilisons la configuration fournie par les Outils : <https://isagri-ng-doc.groupeisagri.com/guides/demarrer/theme.html>

Les différents niveaux possible de typographie sont les suivants (cf <https://material.angular.io/guide/typography#typography-levels>) :

-   headline-1
-   headline-2
-   headline-3
-   headline-4
-   headline-5
-   headline-6
-   subtitle-1
-   subtitle-2
-   body-1
-   body-2
-   caption
-   button

#### Overrider les typographies proposées par material

> Cf <https://material.angular.io/guide/typography#typography-config>

#### Appliquer une typographie sur un composant Angular Material

La configuration des typographies pour les composants Angular Material est faite dans le fichier suivant :

-   `_gc-material-typography.scss`

La librairie applique automatiquement le niveau de typographie à chacun de ses composants

#### Appliquer une typographie sur nos composants

Afin d'améliorer la lisibilité des fichiers scss de nos composants, la gestion du theming (et donc de la typographie) est fait dans un fichier à part : `component_name-theme.component.scss`

Exemple :

```scss
(1)
@use '@angular/material' as mat;
@use '_gc-material-typography' as gc-material-typography;


.my_class {
    @include mat.typography-level(gc-material-typography.$typography, 'body-1'); (2)
```

-   (1) on importe les fichiers scss qui contiennent les variables scss de typographie
-   (2) on applique le niveau de typographie à un élement html
