import { waitForAsync } from '@angular/core/testing';
import {
  monthlyTreatmentHistoryActions,
  MonthlyTreatmentService,
} from '@gc/accounting/data-access';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { provideMockActions } from '@ngrx/effects/testing';
import { Action, Store } from '@ngrx/store';
import { provideMockStore } from '@ngrx/store/testing';
import { Observable, of, throwError } from 'rxjs';
import { initialState } from '../reducers/monthly-treatment-history.reducer';
import { MonthlyTreatmentHistoryEffects } from './monthly-treatment-history.effects';
import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { IDocumentViewerManager } from '@isagri-ng/document-viewer';

describe('MonthlyTreatmentHistoryEffects', () => {
  let spectator: SpectatorService<MonthlyTreatmentHistoryEffects>;
  let effects: MonthlyTreatmentHistoryEffects;
  let actions$: Observable<Action>;
  let monthlyTreatmentService: MonthlyTreatmentService;
  let store: Store;

  const createService = createServiceFactory({
    service: MonthlyTreatmentHistoryEffects,
    providers: [
      provideMockActions(() => actions$),
      provideMockStore({ initialState }),
      IDocumentViewerManager,
    ],
    mocks: [MonthlyTreatmentService],
  });

  beforeEach(() => {
    spectator = createService();
    effects = spectator.service;

    monthlyTreatmentService = spectator.inject(MonthlyTreatmentService);
    store = spectator.inject(Store);
  });

  describe('loadSelectedCompanyEnclosedYears effect', () => {
    let getCompanyEnclosedYearsSpy: jest.SpyInstance<Observable<number[]>>;

    let selectMonthlyTreatmentCompanyIdSpy: jest.SpyInstance<
      Observable<string>
    >;

    beforeEach(() => {
      getCompanyEnclosedYearsSpy = jest.spyOn(
        monthlyTreatmentService,
        'getCompanyEnclosedYears'
      );

      selectMonthlyTreatmentCompanyIdSpy = jest.spyOn(
        store,
        'select'
      ) as jest.SpyInstance<Observable<string>>;
    });

    describe('given a state where select of selectMonthlyTreatmentCompanyId return a companyId', () => {
      const companyId = 'companyId';

      beforeEach(() => {
        selectMonthlyTreatmentCompanyIdSpy.mockReturnValue(of(companyId));
      });

      describe('and getCompanyEnclosedYears return years list', () => {
        const enclosedYears = [2023, 2024];
        beforeEach(() => {
          getCompanyEnclosedYearsSpy.mockReturnValue(of(enclosedYears));
        });

        describe('and a loadSelectedCompanyEnclosedYears action', () => {
          beforeEach(() => {
            actions$ = of(
              monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears()
            );
          });

          it('should return loadSelectedCompanyEnclosedYearsSuccess action', waitForAsync(() => {
            effects.loadSelectedCompanyEnclosedYears$.subscribe(
              (action: Action) =>
                expect(action).toStrictEqual(
                  monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYearsSuccess(
                    {
                      years: enclosedYears,
                    }
                  )
                )
            );
          }));
        });
      });
    });
  });

  describe('loadSelectedYearEnclosedMonths effect', () => {
    let getCompanyYearEnclosedMonthsSpy: jest.SpyInstance<Observable<Date[]>>;

    let selectSpy: jest.SpyInstance<Observable<any>>;

    beforeEach(() => {
      getCompanyYearEnclosedMonthsSpy = jest.spyOn(
        monthlyTreatmentService,
        'getCompanyYearEnclosedMonths'
      );

      selectSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
        Observable<any>
      >;
    });

    describe('given a state where select of selectMonthlyTreatmentCompanyId return a companyId and selectSelectedEnclosedYear a year', () => {
      const companyId = 'companyId';
      const selectedEnclosedYear = 2022;

      beforeEach(() => {
        selectSpy.mockReturnValueOnce(of(companyId));
        selectSpy.mockReturnValueOnce(of(selectedEnclosedYear));
      });

      describe('and getCompanyYearEnclosedMonths return months list', () => {
        const enclosedMonths = [new Date('2024-01-01'), new Date('2024-02-01')];
        beforeEach(() => {
          getCompanyYearEnclosedMonthsSpy.mockReturnValue(of(enclosedMonths));
        });

        describe('and a loadSelectedYearEnclosedMonths action', () => {
          beforeEach(() => {
            actions$ = of(
              monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonths()
            );
          });

          it('should return loadSelectedYearEnclosedMonthsSuccess action', waitForAsync(() => {
            effects.loadSelectedYearEnclosedMonths$.subscribe(
              (action: Action) =>
                expect(action).toStrictEqual(
                  monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonthsSuccess(
                    {
                      months: enclosedMonths,
                    }
                  )
                )
            );
          }));
        });
      });
    });
  });

  describe('loadSelectedMonthAvailableEditions effect', () => {
    let getAvailableEditionsSpy: jest.SpyInstance<
      Observable<MonthlyTreatmentAvailableEdition[]>
    >;

    let selectSpy: jest.SpyInstance<Observable<any>>;

    beforeEach(() => {
      getAvailableEditionsSpy = jest.spyOn(
        monthlyTreatmentService,
        'getAvailableEditions'
      );

      selectSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
        Observable<any>
      >;
    });

    describe('given a state where select of selectMonthlyTreatmentCompanyId return a companyId and selectSelectedEnclosedMonth a Date', () => {
      const companyId = 'companyId';
      const selectedEnclosedMonth = new Date('2024-02-01');

      beforeEach(() => {
        selectSpy.mockReturnValueOnce(of(companyId));
        selectSpy.mockReturnValueOnce(of(selectedEnclosedMonth));
      });

      describe('and getAvailableEditions return a list of available editions', () => {
        const availableMonthlyEditions = [
          {
            id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
            isAvailable: true,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DEBT,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DETAILS,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.RECEIPTS,
            isAvailable: true,
          },
          {
            id: MonthlyTreatmentEditionsEnum.SALES,
            isAvailable: false,
          },
        ];

        beforeEach(() => {
          getAvailableEditionsSpy.mockReturnValue(of(availableMonthlyEditions));
        });

        describe('and a loadSelectedMonthAvailableEditions action', () => {
          beforeEach(() => {
            actions$ = of(
              monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions()
            );
          });

          it('should return loadSelectedMonthAvailableEditionsSuccess action', waitForAsync(() => {
            effects.loadSelectedMonthAvailableEditions$.subscribe(
              (action: Action) =>
                expect(action).toStrictEqual(
                  monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditionsSuccess(
                    {
                      availableMonthlyEditions,
                    }
                  )
                )
            );
          }));
        });
      });
    });
  });

  describe('uncloseSelectedMonth effect', () => {
    let uncloseMonthSpy: jest.SpyInstance<Observable<void>>;

    let selectSpy: jest.SpyInstance<Observable<any>>;

    beforeEach(() => {
      uncloseMonthSpy = jest.spyOn(monthlyTreatmentService, 'uncloseMonth');

      selectSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
        Observable<any>
      >;
    });

    describe('given a state where select of selectMonthlyTreatmentCompanyId return a companyId and selectSelectedEnclosedMonth a Date', () => {
      const companyId = 'companyId';
      const selectedEnclosedMonth = new Date('2022-01-01');

      beforeEach(() => {
        selectSpy.mockReturnValueOnce(of(selectedEnclosedMonth));
        selectSpy.mockReturnValueOnce(of(companyId));
      });

      describe('and uncloseMonth success', () => {
        beforeEach(() => {
          uncloseMonthSpy.mockReturnValue(of(void 0));
        });

        describe('and a uncloseSelectedMonth action', () => {
          beforeEach(() => {
            actions$ = of(
              monthlyTreatmentHistoryActions.uncloseSelectedMonth()
            );
          });

          it('should return uncloseSelectedMonthSuccess action', waitForAsync(() => {
            effects.uncloseSelectedMonth$.subscribe((action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess()
              )
            );
          }));
        });
      });

      describe('and uncloseMonth fail', () => {
        beforeEach(() => {
          uncloseMonthSpy.mockReturnValue(
            throwError(() => new Error('test:uncloseMonth fail'))
          );
        });

        describe('and a uncloseSelectedMonth action', () => {
          beforeEach(() => {
            actions$ = of(
              monthlyTreatmentHistoryActions.uncloseSelectedMonth()
            );
          });

          it('should return uncloseSelectedMonthError action', waitForAsync(() => {
            effects.uncloseSelectedMonth$.subscribe((action: Action) =>
              expect(action).toStrictEqual(
                monthlyTreatmentHistoryActions.uncloseSelectedMonthError()
              )
            );
          }));
        });
      });
    });
  });

  describe('uncloseSelectedMonthSuccess effect', () => {
    describe('given a uncloseSelectedMonthSuccess action', () => {
      beforeEach(() => {
        actions$ = of(
          monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess()
        );
      });

      it('should return loadSelectedCompanyEnclosedYears action', waitForAsync(() => {
        effects.uncloseSelectedMonthSuccess$.subscribe((action: Action) =>
          expect(action).toStrictEqual(
            monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears()
          )
        );
      }));
    });
  });
});
