import { signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { DebFacade } from '@gc/core/deb/application/facades';
import { DebDetails, DraftDeclaration } from '@gc/core/deb/domains/models';
import { ResourceState } from '@gc/core/shared/store';
import { TranslocoDirective } from '@jsverse/transloco';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { LetDirective } from '@ngrx/component';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebDeclarationDetailsComponent } from '../deb-declaration-details/deb-declaration-details.component';
import { DebDeclarationDetailsContainerComponent } from './deb-declaration-details-container.component';

describe('DebDeclarationDetailsContainerComponent', () => {
  let spectator: Spectator<DebDeclarationDetailsContainerComponent>;
  let component: DebDeclarationDetailsContainerComponent;

  const mockDraftDeclaration: DraftDeclaration = {
    id: 'test-id',
    euNomenclature: 'EU123',
    destinationCountryCode: 'FR',
    invoicedAmount: 100,
    statisticalProcedureCode: 'SPC1',
    vatNumber: 'VAT123',
    state: 'initial',
    status: 'enabled',
  };

  const mockDebDetails: DebDetails[] = [
    {
      documentType: 'Invoice' as any,
      documentNumber: 'INV001',
      customerCode: 'CUST001',
      deliveryDate: '2023-01-01',
      productCode: 'PROD001',
      productLabel: 'Test Product',
      lineAmount: 100,
    },
  ];

  const mockResourceState: ResourceState<DebDetails[]> = {
    data: mockDebDetails,
    isLoading: false,
    status: 'Success',
    errors: undefined,
  };
  const mockDebFacade = {
    provide: DebFacade,
    useValue: {
      getDetails: jest.fn().mockReturnValue(signal(mockResourceState)),
    },
  };
  const createComponent = createComponentFactory({
    component: DebDeclarationDetailsContainerComponent,
    imports: [MatDialogModule, MatIconModule],
    declarations: [
      MockDirectives(TranslocoDirective, LetDirective),
      MockComponents(DebDeclarationDetailsComponent, MatButton),
    ],
    providers: [
      { provide: MAT_DIALOG_DATA, useValue: mockDraftDeclaration },
      mockDebFacade,
    ],
    componentProviders: [mockDebFacade],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    spectator.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should inject the dialog data correctly', () => {
    expect(component.debSummaryInfo).toEqual(mockDraftDeclaration);
  });

  it('should have details from the debFacade', () => {
    expect(component.details).toBeDefined();

    expect(component.details().isLoading).toBe(false);
  });
});
