import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebHistoryDetailsComponent } from '../deb-history-details/deb-history-details.component';

@Component({
  selector: 'gc-deb-history-container',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    TranslocoDirective,
    DebHistoryDetailsComponent,
  ],
  templateUrl: './deb-history-container.component.html',
  styleUrl: './deb-history-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebHistoryContainerComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public debHistoryConfig: {
      companyId: string;
    },
    public dialogRef: MatDialogRef<DebHistoryContainerComponent>
  ) {}

  closeDialog() {
    this.dialogRef.close();
  }

  onCloseButtonClick(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();

    this.closeDialog();
  }
}
