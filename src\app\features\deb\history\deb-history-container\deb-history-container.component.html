<ng-container *transloco="let t">
  <div class="container">
    <div mat-dialog-title>
      {{ t('deb.history.title') }}
    </div>
    <mat-dialog-content class="content-container">
      <div class="label-container">
        {{ t('deb.history.label') }}
      </div>
      <gc-deb-history-details
        [companyId]="debHistoryConfig.companyId"
        (lastClosedMonthUpdated)="closeDialog()" />
    </mat-dialog-content>
    <mat-dialog-actions [align]="'center'" class="action-container">
      <button
        color="primary"
        mat-raised-button
        mat-dialog-close
        type="button"
        (click)="onCloseButtonClick($event)">
        {{ t('sharedAction.close') }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-container>
