import { Injectable, inject } from '@angular/core';
import { Cur<PERSON>cyApi, LegacyApiService } from '@gc/shared/api/data-access';
import {
  BehaviorSubject,
  Observable,
  filter,
  map,
  switchMap,
  take,
} from 'rxjs';
import { CurrencyAdapter } from '../adapters/currency.adapter';
import { Currency, LoadingStatus } from '@gc/shared/models';
import { CompanyService } from '@gc/company/data-access';
import { Company } from '@gc/company/models';

@Injectable({
  providedIn: 'root',
})
export class CurrencyService {
  private readonly _legacyApiService = inject(LegacyApiService);
  private readonly _companyService = inject(CompanyService);

  private _defaultCurrency$$ = new BehaviorSubject<Currency | null>(null);
  private _defaultCurrency$ = this._defaultCurrency$$.asObservable();
  private _defaultCurrencyLoadingStatus$$ = new BehaviorSubject<LoadingStatus>(
    'NOT_LOADED'
  );
  private _defaultCurrencyLoadingStatus$ =
    this._defaultCurrencyLoadingStatus$$.asObservable();

  public getDefaultCurrency$(): Observable<Currency> {
    if (this._defaultCurrencyLoadingStatus$$.value === 'NOT_LOADED') {
      this._loadDefaultCurrency();
    }

    return this._defaultCurrencyLoadingStatus$.pipe(
      filter((status: LoadingStatus) => status === 'LOADED'),
      switchMap(() => this._defaultCurrency$),
      filter(Boolean)
    );
  }

  public getDefaultCurrencyLoadingStatus$(): BehaviorSubject<LoadingStatus> {
    return this._defaultCurrencyLoadingStatus$$;
  }

  private _loadDefaultCurrency(): void {
    this._defaultCurrencyLoadingStatus$$.next('IN_PROGRESS');

    this._companyService.companies$
      .pipe(
        switchMap((companies: Company[]) =>
          this._legacyApiService.currenciesGetCurrencies(
            companies[0].id,
            undefined,
            true
          )
        ),
        take(1),
        map((currenciesApi: CurrencyApi[]) =>
          CurrencyAdapter.fromApi(currenciesApi[0])
        )
      )
      .subscribe({
        next: (currency: Currency) => {
          this._defaultCurrency$$.next(currency);
          this._defaultCurrencyLoadingStatus$$.next('LOADED');
        },
        error: () => {
          this._defaultCurrencyLoadingStatus$$.next('ERROR');
        },
      });
  }
}
