# Source code management and release process

**👉 Team evolved** : Null Re<PERSON> and <PERSON><PERSON><PERSON>

<div style ="text-align:center; font-weight:bold">👉 SUM UP : URLs per branch</div>

| Branche                                        | Version                     |                                     Url                                     |
| :--------------------------------------------- | :-------------------------- | :-------------------------------------------------------------------------: |
| develop                                        | latest version (ex: 25.20)  |             <https://test-isagclive-yyyy-(v+1).saasgroupe.com>              |
|                                                |                             |          Example : <https://test-isagclive-2025-2.saasgroupe.com>           |
| release/yy.vv (yy = year & vv = version)       | next version (ex: 25.15)    |               <https://test-isagclive-yyyy-v.saasgroupe.com>                |
|                                                |                             | Example : release/25.15 // <https://test-isagclive-2025-1-5.saasgroupe.com> |
| release/yy.vv (after validation process is ok) |                             |              <https://preprodinterne-IsaGCLive.saasgroupe.com>              |
| hotfix/yy.vv                                   | patch for current version   |               <https://test-isagclive-yyyy-v.saasgroupe.com>                |
|                                                |                             |  Example : hotfix/25.10 // <https://test-isagclive-2025-1.saasgroupe.com>   |
| master **(production)**                        | current version (ex: 25.10) |                     <https://IsaGCLive.saasgroupe.com>                      |

💡Note : **release/yy.vv** branch is linked with 2 urls : once the validation process is done on test, the release/yy.vv branch is deployed on /preprodinterne for preprod validation (by the null ref team).

[TOC]

## Glossary

-   **Pull request** : process of code integration from one branch into another.
-   **Tag** : it is a git reference of a commit (=snapshot of a version of the code).
-   **Version**: version number of our gc webapp is located in the property _version_ of _package.json_
-   **Release / delivery** : process of compiling and sending application on a remote URL to be accessible from the browser.
    💡 The application is configred for different environments (prelease, uat, preprod, prod ...) ; each environment is associated to a specific backend (api) and thus to a specific data set.
-   **Continuous integration (CI)** : process of automatic code quality validation before merging from one branch to another.
    -   build : check whether build works or not
    -   linter : launch code linters
    -   unit tests : launch unit tests and generate xml report for coverage

## ⌨️ Source code management (Git branches)

**Remote repository**

👉 The remote repository that hold our sources is AZDO (Azure DevOps) : [gc.webapp repo](https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_git/gc.webapp/pullrequests?_a=mine)

**Branches lifecycle**

| Branch             |         Lifecycle          |                                           When create / merge / rebase ?                                           |            Who ?            |
| :----------------- | :------------------------: | :----------------------------------------------------------------------------------------------------------------: | :-------------------------: |
| develop            |          All time          |           Rebase onto release/yy.vv **OR** onto master after production release process (hotfix, mep ..)           |         **Pixels**          |
| feat/zzz / fix/zzz | During feature development | Merge into develop (**with squash**) when development is done and tested by the QA on prerelease url (ex: /cortex) |         **Pixels**          |
| release/yy.vv      | Before production release  |                      Created from develop and merged into master when release process is done                      | **Pixels** and **Null ref** |
|                    |                            |                   ⚠️ create release/yy.vv **before** merging code for next version into develpop                   |                             |
| master             |          All time          |                                                         -                                                          |        **Null ref**         |

**Developpement phase**

-   Step 1 : Check the version you need to develop for (develop or release/yy.vv) with the team
-   Step 2 : Create your branch (feat/zzz or fix/zzz) from the target defined in step 1
-   Step 3 : Make your dev and commit and push and make PR
    💡PR title naming convention : **type(azdo_number): description**
    💡PR description convention : use [template](./pull_request_template/branches/develop.md) (it should appear automatically when making a PR to develop)
-   Step 4 : Deploy on tmp url for QA testing :
    -   git checkout deploy/cortex (if branch already exist : `git b -D deploy/cortex` then `git checkout deploy/cortex` to start with "clean" branch)
    -   git rebase -i feat/zzz ⚠️ you can drop all existing commit ; keep only the last commit (bump version)
    -   resolve conflit ⚠️ keep the deploy/cortex version
    -   git push -f
    -   go on AZDO pipeline : [deploy pipeline](https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_build?definitionId=311)
    -   click on "run pipeline" ; select `deploy/cortex` and click on "run"
    -   once the pipeline is done, go on AZDO release to ckeck the deployment is ok : [release state](https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_release?_a=releases&view=mine&definitionId=1)
        ![alt text](./images/azdo_release.png)
        👉 If the label is green, it's done, you can tell your QA to test your dev on **<https://test-gccolive-fr-fr.saasgroupe.com/gc-webapp-pre/cortex/>**
-   Step 5 : Once the QA validate your dev AND you dev team mate validate your PR : you can merge 🚀

**Keep the branches sync 👉 rebase**

The pixels team is responsible to keep its branches sync. This means the following last step need to be done :

-   Step 6 :
    -   If commit on latest version (develop) : nothing to do, you are done !
    -   If commit on intermediate version (release/yy.vv or hotfix/yy.vv) : then you need to make a "waterfall" rebase as follow.

Let's say we have the following branches : _develop_ based on ➡️ _release/25.15_ ➡️ based on _release/25.10_

Then you have to do locally:

-   git checkout release/25.10
-   git pull
-   git checkout release/25.15
-   git pull
-   git rebase -i release/25.10
-   resolve conflicts
-   git log ➡️ check that all commits of release/25.15 are there and that they are stacked on release/25.10 ones
-   git push -f ⚠️ be careful with that, if any doubt, ask tech lead or null ref to double check
-   then repeat process between develop and release/25.15

### CI settings (for developpers only)

Here is a sum up of CI settings :

| Branch                        | CI validation before merge required | CI validation after merge launched |
| :---------------------------- | :---------------------------------: | :--------------------------------: |
| develop                       |         :white_check_mark:          |                :x:                 |
| release/yy.vv                 |         :white_check_mark:          |                :x:                 |
| deploy/pirats & deploy/cortex |                 :x:                 |                :x:                 |
| master                        |                 :x:                 |                :x:                 |

## 🚀 Source code deployment (yml scripts and AZDO pipelines)

**When to deploy ?**

At any time, when developpement OR rebase process is done, we can deploy this code on a specific URL.

**How to deploy ? 🤔**

It's easy ! Just use **AZDO pipelines** :
👉 <https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_build>

And the following table to choose your pipeline :

| Phase / environment |    Branch     |                 Pipeline to launch                 | For developers : environnement files being used for build | Who launch pipeline ? |
| :------------------ | :-----------: | :------------------------------------------------: | :-------------------------------------------------------: | --------------------- |
| Development         |    develop    |    gc.webapp_publish_develop (⚠️select develop)    |                 environment.prerelease.ts                 | **Pixels developers** |
| Development         | release/yy.vv | gc.webapp_publish_develop (⚠️select release/yy.vv) |                 environment.prerelease.ts                 | **Pixels developers** |
| Features testing    | deploy/cortex |              gc.webapp_publish_deploy              |                 environment.prerelease.ts                 | **Pixels developers** |
| Preprod             | release/yy.vv |         gc.webapp_publish_staging_preprod          |                  environment.preprod.ts                   | **Null ref team**     |
| Production          |    master     |               gc.webapp_publish_prod               |                      environment.ts                       | **Null ref team**     |

### The yaml Scripts (for developpers only)

**Who is in charge ?**
👉 Frontend web dev team supported by Null Ref.

Yaml scripts are located in this Git repo. Here is the list :

-   CI script : azure-pipelines-ci.yml
-   Set production version script : azure-pipelines-set-prod-version.yml
-   Build and publish application script : azure-pipelines-publish_zzz.yml
-

### XLdeploy (for DevOps _null ref_ only)

Lorsqu'une nouvelle adresse de déploiement est demandée, je dois configurer la branche et paramétrer xldeploy pour une nouvelle application.

-   Créer une branche dans le namespace `deploy/` et publier la branche.
    La première publication échouera au moment du déploiement pour des substitutions manquantes, ce sera corrigé par l'étape qui suit.
-   Sur deploy : Modifier les restrictions des dictionnaires pour la nouvelle application :
    -   Dictionary-GCCO-GC-IIS
    -   millesime (Dictionary-GCCO-GC-2024-1 à l'heure actuelle)
-   Relancer la publication de la release

## 🔢 Source code versionning

App version is updated during deployment process :

-   either **automatically** during publish (azdo pipeline)
-   either **manually** with production version attribution pipeline (azure-pipelines-set-prod-version.yml)

| Branch                       |              Version updating rule              |                Script                |             Pipeline              |
| :--------------------------- | :---------------------------------------------: | :----------------------------------: | :-------------------------------: |
| develop                      |       patch (yy.(v+1).1, yy.(v+1).2 ...)        |     azure-pipelines-publish.yml      |     gc.webapp_publish_develop     |
| release/yy.vv (dev phase)    |          patch (yy.vv.1, yy.vv.2 ...)           |     azure-pipelines-publish.yml      |     gc.webapp_publish_develop     |
| deploy/cortex                | prerelease (x.y.z-cortex.0, x.y.z-cortex.1 ...) |     azure-pipelines-publish.yml      |     gc.webapp_publish_deploy      |
| release/yy.vv (prepod phase) |   patch (yy.vv-preprod0, yy.vv-preprod1 ...)    |     azure-pipelines-publish.yml      | gc.webapp_publish_staging_preprod |
| master                       |       manuellement (**by null ref team**)       | azure-pipelines-set-prod-version.yml |    gc.webapp_set_prod_version     |
