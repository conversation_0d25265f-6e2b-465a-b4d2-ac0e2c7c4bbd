import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { SubHeaderComponent } from './sub-header.component';
import { RouterLink } from '@angular/router';
import { MockDirectives } from 'ng-mocks';

describe('SubHeaderComponent', () => {
  let spectator: Spectator<SubHeaderComponent>;
  let subHeaderComponent: SubHeaderComponent;
  const createComponent = createComponentFactory({
    component: SubHeaderComponent,
    declarations: [MockDirectives(RouterLink)],
  });

  beforeEach(() => {
    spectator = createComponent();
    subHeaderComponent = spectator.component;
  });

  it('should create', () => {
    expect(subHeaderComponent).toBeTruthy();
  });
});
