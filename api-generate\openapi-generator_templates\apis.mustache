/**
    We don't use the APIS object which is in the original template https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/typescript-angular/apis.mustache
    To avoid conflit during export, we just remove it from generation.

*/

{{#apiInfo}}
{{#apis}}
{{#operations}}
export * from './{{ classFilename }}';
{{/operations}}
{{#withInterfaces}}
export * from './{{ classFilename }}Interface';
{{/withInterfaces}}
{{/apis}}
{{/apiInfo}}
