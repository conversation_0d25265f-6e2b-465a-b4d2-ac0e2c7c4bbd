<div
  *transloco="let t; read: 'businessReview.advanced-settings-tab'"
  class="container">
  <div class="master-detail-container">
    <gc-master-detail-panels-layout
      [masterPanelTitle]="t('panel-title.filters')"
      [detailPanelTitle]="detailPanelTitleKey ? t(detailPanelTitleKey) : ''">
      <mat-selection-list
        masterPanel
        #optionSelection
        (selectionChange)="onMatListOptionChange($event)"
        [multiple]="false">
        <mat-list-option selected [value]="'vat'" color="primary">{{
          t('option.vat')
        }}</mat-list-option>
        <mat-list-option [value]="'warehouses'" color="primary">{{
          t('option.warehouses')
        }}</mat-list-option>
      </mat-selection-list>

      <div detailPanel class="detail-panel-container">
        @if (optionSelection.selectedOptions.selected[0].value === 'vat') {
          <gc-vat-table
            [selectedVatRate]="pendingValidationVatRate$$ | ngrxPush"
            (selectionChange)="onVatRateSelectionChange($event)" />
        } @else if (
          optionSelection.selectedOptions.selected[0].value === 'warehouses'
        ) {
          <gc-warehouses-table
            [selectedIds]="pendingValidationWarehouseIds$$ | ngrxPush"
            (selectionChange)="onWarehouseSelectionChange($event)" />
        }
      </div>
    </gc-master-detail-panels-layout>
  </div>

  <div
    *transloco="let t"
    class="buttons-container"
    data-testid="actions-container">
    <span class="save-needed-notification-container" data-testid="notification">
      @if (hasPendingChanges()) {
        {{ t('businessReview.advanced-settings-tab.notification.save-needed') }}
      }
    </span>
    <button
      mat-raised-button
      (click)="onCancel()"
      [disabled]="(isLoading$ | ngrxPush) || !hasPendingChanges()">
      {{ t('sharedAction.cancel') | uppercase }}
    </button>
    <button
      mat-raised-button
      color="primary"
      (click)="onValidate()"
      [disabled]="(isLoading$ | ngrxPush) || !hasPendingChanges()">
      {{ t('sharedAction.save') | uppercase }}
    </button>
  </div>
</div>
