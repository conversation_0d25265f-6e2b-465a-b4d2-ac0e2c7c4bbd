import { Pipe, PipeTransform } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { RangeFormGroup, RangeFormGroupValue } from '@gc/accounting/models';
import { DateRangeErrors } from '@gc/shared/models';

@Pipe({
  name: 'toErrorKey',
  standalone: true,
})
export class ToErrorKeyPipe implements PipeTransform {
  transform(
    _value: Partial<RangeFormGroupValue> | undefined,
    rangeFormGroup: FormGroup<RangeFormGroup>
  ): string | undefined {
    const fgControlsStart = rangeFormGroup.controls.start;
    const fgControlsEnd = rangeFormGroup.controls.end;
    if (fgControlsEnd.hasError(DateRangeErrors.DIFFERENT_MONTH_OR_YEAR)) {
      return 'accounting.monthly-edition-tab.form.error.different-month-or-year';
    } else if (
      fgControlsEnd.hasError(DateRangeErrors.START_DATE_AFTER_END_DATE)
    ) {
      return 'accounting.monthly-edition-tab.form.error.start-date-after-end-date';
    } else if (fgControlsEnd.hasError(DateRangeErrors.MONTH_ALREADY_CLOSED)) {
      return 'accounting.monthly-edition-tab.form.error.open-history';
    } else if (
      fgControlsStart.hasError('matDatepickerParse') ||
      fgControlsEnd.hasError('matDatepickerParse')
    ) {
      return 'sharedForm.error.date.invalid';
    } else if (
      fgControlsStart.hasError('required') ||
      fgControlsEnd.hasError('required')
    ) {
      return 'sharedForm.error.required';
    }

    return undefined;
  }
}
