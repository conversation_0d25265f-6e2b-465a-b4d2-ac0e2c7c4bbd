/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Commission } from '../../domains/models';
import { CommissionResponse, CommissionDetailsResponse } from './response';

@Injectable()
export class CommissionsApiService {
  mockCommissions: CommissionResponse[] = [
    {
      documentId: 'FA123456789',
      documentDate: '2023-10-01',
      amount: 100,
      amountToBePaid: 50,
      status: 50,
    },
    {
      documentId: 'FA987654321',
      documentDate: '2023-10-02',
      amount: 200,
      amountToBePaid: 150,
      status: 25,
    },
    {
      documentId: 'FA322254321',
      documentDate: '2023-10-02',
      amount: 53.22,
      amountToBePaid: 12.33,
      status: 12,
    },
    {
      documentId: 'FA95234431',
      documentDate: '2023-10-02',
      amount: 122.43,
      amountToBePaid: 12.43,
      status: 82,
    },
  ];

  loadCommissions(): Observable<CommissionResponse[]> {
    return of(this.mockCommissions);
  }

  loadCommissionDetails(
    _commission: Commission
  ): Observable<CommissionDetailsResponse[]> {
    const mockDetails: CommissionDetailsResponse[] = [
      {
        productLabel: 'Bordeaux rouge 2023 75 cl',
        quantity: 5,
        amountWithoutTax: 100,
        amountWithTax: 120,
        commissionRate: 10,
        commissionAmount: 12,
      },
      {
        productLabel: 'Bordeaux rouge 2019 75 cl',
        quantity: 3,
        amountWithoutTax: 150,
        amountWithTax: 180,
        commissionRate: 10,
        commissionAmount: 18,
      },
    ];

    return of(mockDetails);
  }

  updateCommission(_: Commission): Observable<CommissionResponse[]> {
    // TODO: Replace with actual API call
    return of(this.mockCommissions as CommissionResponse[]);
  }
}
