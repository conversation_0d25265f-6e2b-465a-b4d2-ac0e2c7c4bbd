import { waitForAsync } from '@angular/core/testing';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentStoreModule,
  selectMonthlyTreatmentDateRange,
  selectMonthlyTreatmentDateRangeAndCompanyId,
} from '@gc/accounting/data-access';
import {
  AccountEntryListTreatmentIdRetrieverCardComponent,
  DebtListTreatmentIdRetrieverCardComponent,
  ReceiptListTreatmentIdRetrieverCardComponent,
  SalesByProductCategoryTreatmentIdRetrieverCardComponent,
  SalesDetailsTreatmentIdRetrieverCardComponent,
} from '@gc/accounting/feature';
import {
  MonthlyTreatmentRange,
  RangeFormGroup,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { NavigationService } from '@gc/core/navigation/feature';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { Store } from '@ngrx/store';
import { MockComponents, MockModule } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { MonthlyVisualizationComponent } from './monthly-visualization.component';
import { StimulsoftLauncherService } from './services/stimulsoft-launcher.service';
import { ProductCharacteristicEnum } from '@gc/product/models';
import {
  REPORT_ID,
  ReportFamilies,
  ReportId,
} from '@gc/shared/stimulsoft/models';
import { CompanyService } from '@gc/company/data-access';

describe('MonthlyVisualizationComponent', () => {
  let spectator: Spectator<MonthlyVisualizationComponent>;
  let component: MonthlyVisualizationComponent;
  let companyService: CompanyService;
  let store: Store;
  let stimulsoftLauncherService: StimulsoftLauncherService;

  let dispatchSpy: jest.SpyInstance<void>;

  const createComponent = createComponentFactory({
    component: MonthlyVisualizationComponent,
    declarations: [
      MockComponents(
        CompanySingleSelectComponent,
        SalesByProductCategoryTreatmentIdRetrieverCardComponent,
        SalesDetailsTreatmentIdRetrieverCardComponent,
        ReceiptListTreatmentIdRetrieverCardComponent,
        DebtListTreatmentIdRetrieverCardComponent,
        AccountEntryListTreatmentIdRetrieverCardComponent
      ),
      MockModule(MonthlyTreatmentStoreModule),
    ],
    mocks: [CompanyService, NavigationService, Store],
    componentMocks: [StimulsoftLauncherService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);

    companyService = spectator.inject(CompanyService);
    store = spectator.inject(Store);
    dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation();

    stimulsoftLauncherService = spectator.inject(
      StimulsoftLauncherService,
      true
    );
    jest
      .spyOn(
        stimulsoftLauncherService,
        'salesByProductTreatmentIdLoadingStatus$',
        'get'
      )
      .mockReturnValue(of('NOT_LOADED'));
    jest
      .spyOn(
        stimulsoftLauncherService,
        'salesDetailsTreatmentIdLoadingStatus$',
        'get'
      )
      .mockReturnValue(of('NOT_LOADED'));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should call handleSelectedCompanyId, initRangeFormGroup and handleEnclosureMonthLoading methods and dispatch loadDefaultFilters action', () => {
      const handleSelectedCompanyIdSpy = jest
        .spyOn(component, 'handleSelectedCompanyId')
        .mockImplementation();

      const initRangeFormGroupSpy = jest
        .spyOn(component, 'initRangeFormGroup')
        .mockImplementation();

      const handleEnclosureMonthLoadingSpy = jest
        .spyOn(component, 'handleEnclosureMonthLoading')
        .mockImplementation();

      component.ngOnInit();

      expect(dispatchSpy).toHaveBeenCalledWith(
        monthlyTreatmentActions.loadDefaultFilters()
      );

      expect(handleSelectedCompanyIdSpy).toHaveBeenCalled();
      expect(initRangeFormGroupSpy).toHaveBeenCalled();
      expect(handleEnclosureMonthLoadingSpy).toHaveBeenCalled();
    });
  });

  describe('handleSelectedCompanyId', () => {
    let getCompanyIdIfSingleCompanySpy: jest.SpyInstance<
      Observable<string | undefined>
    >;
    let initSelectCompanyFormControlSpy: jest.SpyInstance<Observable<void>>;

    beforeEach(() => {
      getCompanyIdIfSingleCompanySpy = jest.spyOn(
        companyService,
        'companyIdIfSingleCompany$',
        'get'
      );

      initSelectCompanyFormControlSpy = jest
        .spyOn(component, 'initSelectCompanyFormControl')
        .mockReturnValue(of(void 0));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return a company', () => {
      const companyId = 'companyId';
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should not call initSelectCompanyFormControl', waitForAsync(() => {
        component.handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(initSelectCompanyFormControlSpy).not.toHaveBeenCalled();
      }));
    });

    describe('given a state where companyIdIfSingleCompany$ getter return an undefined company', () => {
      const companyId = undefined;
      beforeEach(() => {
        getCompanyIdIfSingleCompanySpy.mockReturnValue(of(companyId));
      });
      it('should call initSelectCompanyFormControl', waitForAsync(() => {
        component.handleSelectedCompanyId();

        expect(getCompanyIdIfSingleCompanySpy).toHaveBeenCalled();
        expect(initSelectCompanyFormControlSpy).toHaveBeenCalled();
      }));
    });
  });

  describe('initSelectCompanyFormControl', () => {
    describe('given a state where selectMonthlyTreatmentCompanyId return a company id', () => {
      const companyId = 'companyId';

      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(companyId));
      });

      it('should init selectCompanyFC FormControl', waitForAsync(() => {
        component.initSelectCompanyFormControl().subscribe(() => {
          expect(component.selectCompanyFC?.value).toBe(companyId);
          expect(
            component.selectCompanyFC?.hasValidator(Validators.required)
          ).toBe(true);
        });
      }));
    });

    describe('given a state where selectMonthlyTreatmentCompanyId return an undefined company id', () => {
      const companyId = undefined;

      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(companyId));
      });

      it('should init selectCompanyFC FormControl', waitForAsync(() => {
        component.initSelectCompanyFormControl().subscribe(() => {
          expect(component.selectCompanyFC?.value).toBe('');
          expect(
            component.selectCompanyFC?.hasValidator(Validators.required)
          ).toBe(true);
        });
      }));
    });
  });

  describe('handleEnclosureMonthLoading', () => {
    describe('given a state where selectMonthlyTreatmentCompanyId return a company id', () => {
      const companyId = 'companyId';

      beforeEach(() => {
        jest.spyOn(store, 'select').mockReturnValue(of(companyId));
      });

      it('should dispatch loadEnclosureMonth action', waitForAsync(() => {
        component.handleEnclosureMonthLoading();

        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.loadEnclosureMonth({ companyId })
        );
      }));
    });
  });

  describe('initRangeFormGroup', () => {
    describe('given a state where selectMonthlyTreatmentDateRange return a MonthlyTreatmentRange', () => {
      const start = new Date('2024-02-01');
      const end = new Date('2024-02-30');

      let selectSpy: jest.SpyInstance<Observable<MonthlyTreatmentRange>>;

      beforeEach(() => {
        selectSpy = (
          jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<MonthlyTreatmentRange>
          >
        ).mockReturnValue(of({ start, end }));
      });

      it('should init rangeFormGroup', waitForAsync(() => {
        component.initRangeFormGroup();

        expect(selectSpy).toHaveBeenCalledWith(selectMonthlyTreatmentDateRange);

        expect(component.rangeFormGroup.value.start).toBe(start);
        expect(component.rangeFormGroup.value.end).toBe(end);
        expect(
          component.rangeFormGroup.controls.start.hasValidator(
            Validators.required
          )
        ).toBe(true);
        expect(
          component.rangeFormGroup.controls.end.hasValidator(
            Validators.required
          )
        ).toBe(true);
      }));
    });

    describe('given a state where selectMonthlyTreatmentDateRange return an undefined MonthlyTreatmentRange', () => {
      let selectSpy: jest.SpyInstance<
        Observable<MonthlyTreatmentRange | undefined>
      >;

      beforeEach(() => {
        selectSpy = (
          jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<MonthlyTreatmentRange | undefined>
          >
        ).mockReturnValue(of(undefined));
      });

      it('should init rangeFormGroup', waitForAsync(() => {
        component.initRangeFormGroup();

        expect(selectSpy).toHaveBeenCalledWith(selectMonthlyTreatmentDateRange);

        expect(component.rangeFormGroup.value.start).toBe(null);
        expect(component.rangeFormGroup.value.end).toBe(null);
        expect(
          component.rangeFormGroup.controls.start.hasValidator(
            Validators.required
          )
        ).toBe(true);
        expect(
          component.rangeFormGroup.controls.end.hasValidator(
            Validators.required
          )
        ).toBe(true);
      }));
    });

    describe('given a state where selectMonthlyTreatmentDateRange return a MonthlyTreatmentRange with start date equal to rangeFromGroup start date', () => {
      const start = new Date('2024-02-01');
      const end = new Date('2024-02-30');

      let selectSpy: jest.SpyInstance<Observable<MonthlyTreatmentRange>>;

      beforeEach(() => {
        selectSpy = (
          jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<MonthlyTreatmentRange>
          >
        ).mockReturnValue(of({ start, end }));

        component.rangeFormGroup = new FormGroup<RangeFormGroup>({
          start: new FormControl<Date>(start),
          end: new FormControl<Date>(end),
        });
      });

      it('should not init rangeFormGroup and not call dispatchChangeDateRangeActionWhenValueChange', waitForAsync(() => {
        const createRangeFormGroupSpy = jest.spyOn(
          component as any,
          '_createRangeFormGroup'
        );
        const dispatchChangeDateRangeActionWhenValueChange = jest.spyOn(
          component,
          'dispatchChangeDateRangeActionWhenValueChange'
        );
        component.initRangeFormGroup();

        expect(selectSpy).toHaveBeenCalledWith(selectMonthlyTreatmentDateRange);

        expect(createRangeFormGroupSpy).not.toHaveBeenCalled();
        expect(
          dispatchChangeDateRangeActionWhenValueChange
        ).not.toHaveBeenCalled();
      }));
    });
  });

  describe('dispatchChangeDateRangeActionWhenValueChange', () => {
    describe('given a state where rangeFormGroup is initialized', () => {
      describe('when rangeFormGroup value change', () => {
        it('should dispatch changeDateRange action with the new dates', waitForAsync(() => {
          expect.assertions(1);

          const start = new Date('2024-07-09');
          const end = new Date('2024-07-30');
          component.rangeFormGroup = new FormGroup<RangeFormGroup>({
            start: new FormControl<Date | null>(new Date(), {
              nonNullable: true,
              validators: [Validators.required],
            }),
            end: new FormControl<Date | null>(new Date(), {
              nonNullable: true,
              validators: [Validators.required],
            }),
          });

          component
            .dispatchChangeDateRangeActionWhenValueChange()
            .subscribe(() => {
              expect(dispatchSpy).toHaveBeenCalledWith(
                monthlyTreatmentActions.changeDateRange({
                  startDate: start,
                  endDate: end,
                })
              );
            });

          component.rangeFormGroup.setValue({ start, end });
        }));
      });
    });
  });

  describe('consultSalesByProductCategory', () => {
    let selectSpy: jest.SpyInstance;
    describe('given a state where selectMonthlyTreatmentDateRangeAndCompanyId return a range and a company id', () => {
      const range: MonthlyTreatmentRange = {
        start: new Date('2024-10-04'),
        end: new Date('2024-10-31'),
      };
      const companyId = 'a company id';

      beforeEach(() => {
        selectSpy = jest.spyOn(store, 'select');
        selectSpy.mockReturnValueOnce(of({ range, companyId }));
      });

      describe('when method is call with a list of ProductCharacteristicEnum', () => {
        const categories: ProductCharacteristicEnum[] = [
          ProductCharacteristicEnum.CUSTOM_0,
          ProductCharacteristicEnum.CUSTOM_11,
        ];

        it('should call retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft method of StimulsoftLauncherService', waitForAsync(() => {
          const spy = jest
            .spyOn(
              stimulsoftLauncherService,
              'retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft'
            )
            .mockReturnValue(of(void 0));

          spectator.component.consultSalesByProductCategory(categories);

          expect(spy).toHaveBeenCalledWith({
            categories,
            dateFrom: new Date('2024-10-04'),
            dateTo: new Date('2024-10-31'),
            companyId,
          });
          expect(selectSpy).toHaveBeenCalledWith(
            selectMonthlyTreatmentDateRangeAndCompanyId
          );
        }));
      });
    });
  });

  describe('consultSalesDetails', () => {
    let selectSpy: jest.SpyInstance;
    describe('given a state where selectMonthlyTreatmentDateRangeAndCompanyId return a range and a company id', () => {
      const range: MonthlyTreatmentRange = {
        start: new Date('2024-10-04'),
        end: new Date('2024-10-31'),
      };
      const companyId = 'a company id';

      beforeEach(() => {
        selectSpy = jest.spyOn(store, 'select');
        selectSpy.mockReturnValueOnce(of({ range, companyId }));
      });

      describe('when method is call with a SalesDetailsOrderByEnum', () => {
        const orderBy: SalesDetailsOrderByEnum =
          SalesDetailsOrderByEnum.BUSINESS_TYPE;

        it('should call retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft method of StimulsoftLauncherService', waitForAsync(() => {
          const spy = jest
            .spyOn(
              stimulsoftLauncherService,
              'retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft'
            )
            .mockReturnValue(of(void 0));

          spectator.component.consultSalesDetails(orderBy);

          expect(spy).toHaveBeenCalledWith({
            orderBy,
            dateFrom: new Date('2024-10-04'),
            dateTo: new Date('2024-10-31'),
            companyId,
          });
          expect(selectSpy).toHaveBeenCalledWith(
            selectMonthlyTreatmentDateRangeAndCompanyId
          );
        }));
      });
    });
  });

  describe('consult', () => {
    let selectSpy: jest.SpyInstance;
    describe('given a state where selectMonthlyTreatmentDateRangeAndCompanyId return a range and a company id', () => {
      const range: MonthlyTreatmentRange = {
        start: new Date('2024-10-04'),
        end: new Date('2024-10-31'),
      };
      const companyId = 'a company id';

      beforeEach(() => {
        selectSpy = jest.spyOn(store, 'select');
        selectSpy.mockReturnValueOnce(of({ range, companyId }));
      });

      describe('when method is call with a ReportId and a ReportFamilies', () => {
        const reportId: ReportId = REPORT_ID.debt;
        const reportFamily: ReportFamilies = ReportFamilies.DEBT;

        it('should call prepareParamsAndNavigateToStimulsoft method of StimulsoftLauncherService', waitForAsync(() => {
          const spy = jest
            .spyOn(
              stimulsoftLauncherService,
              'prepareParamsAndNavigateToStimulsoft'
            )
            .mockReturnValue(void 0);

          spectator.component.consult(reportId, reportFamily);

          expect(spy).toHaveBeenCalledWith(
            reportId,
            reportFamily,
            companyId,
            new Date('2024-10-31')
          );
          expect(selectSpy).toHaveBeenCalledWith(
            selectMonthlyTreatmentDateRangeAndCompanyId
          );
        }));
      });
    });
  });
});
