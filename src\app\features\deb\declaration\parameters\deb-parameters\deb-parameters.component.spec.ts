import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { DebParametersComponent } from './deb-parameters.component';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
describe('DebParametersComponent', () => {
  let spectator: Spectator<DebParametersComponent>;
  const createComponent = createComponentFactory({
    component: DebParametersComponent,
    declarations: [MockDirective(TranslocoDirective)],
  });
  beforeEach(() => {
    spectator = createComponent();
  });
  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });
});
