import { <PERSON>ing<PERSON><PERSON> } from '@gc/shared/api/data-access';
import { DunningInvoice, Dunn<PERSON> } from '@gc/dunning/models';
import { DunningCustomerInformationsAdapter } from '../adapters/dunning-customer-informations.adapter';
import { DunningInvoiceAdapter } from '../adapters/dunning-invoice.adapter';
import { InvoicesCountAdapter } from '../adapters/invoices-count.adapter';

export class DunningApiAdapter {
  public static fromApi(dunningApi: DunningApi): Dunning {
    const {
      enterpriseId: companyId,
      enterpriseCode: companyCode,
      customerId,
      customerCode,
      customerInformations,
      totalAmountOutstanding,
      dueDates,
    } = dunningApi;

    if (!customerCode) {
      throw new Error('customerCode cannot be undefined');
    }

    return {
      companyId,
      companyCode,
      customerId,
      customerCode,
      customerInformations:
        DunningCustomerInformationsAdapter.fromApi(customerInformations),
      totalAmountOutstanding,
      invoicesCount: InvoicesCountAdapter.fromDueDatesApi(dueDates),
      invoicesIds: DunningInvoiceAdapter.fromDueDatesApi(dueDates).map(
        (dunningInvoice: DunningInvoice) => dunningInvoice.id
      ),
    };
  }
}
