import { inject, Injectable } from '@angular/core';
import { SaveParametersPort } from '@gc/core/deb/domains/ports';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { DebParameters, DeclarationType } from '@gc/core/deb/domains/models';
import { SaveDebParamsRequest } from '../api/request';

@Injectable()
export class SaveParametersAdapter implements SaveParametersPort {
  private readonly api = inject(DebApiService);

  for(parameters: DebParameters, companyId: string) {
    const request = this.mapToRequest(parameters, companyId);
    return this.api.saveParameters(request);
  }

  private mapToRequest(
    parameters: DebParameters,
    companyId: string
  ): SaveDebParamsRequest {
    return {
      ...parameters,
      companyId,
      declarationType:
        parameters.declarationType === 'light'
          ? DeclarationType.LIGHT
          : DeclarationType.DETAILED,
    };
  }
}
