import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  forwardRef,
  OnInit,
  inject,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BlockReloadDirective } from '@gc/core/navigation/feature';
import {
  LOCKABLE_COMPONENT_TOKEN,
  LockableComponent,
} from '@gc/core/navigation/models';
import {
  ProductCharacteristicActions,
  ProductCharacteristicSelectionService,
  ProductCharacteristicsFormService,
  ProductCharacteristicsFormValidationService,
  ProductCharacteristicsReloadService,
  ProductCharacteristicsService,
  ProductCharacteristicStoreModule,
} from '@gc/product/data-access';
import {
  CharacteristicCodesListViewEditComponent,
  CharacteristicsListViewEditComponent,
  CodeStructureResultComponent,
  FixedCharacteristicEditInfoIconComponent,
} from '@gc/product/feature';
import { ProductCharacteristic } from '@gc/product/models';
import {
  CODE_SIZE_MAX,
  ProductCharacteristicsFormHelper,
} from '@gc/product/utils';
import {
  DialogService,
  MasterDetailPanelsLayoutComponent,
} from '@gc/shared/ui';
import {
  TRANSLOCO_SCOPE,
  TranslocoModule,
  TranslocoService,
} from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import {
  defer,
  EMPTY,
  filter,
  finalize,
  forkJoin,
  iif,
  Observable,
  of,
  switchMap,
  take,
  tap,
  combineLatest,
  map,
} from 'rxjs';

@Component({
  selector: 'gc-code-structure',
  templateUrl: './code-structure.component.html',
  styleUrls: [
    './code-structure.component.scss',
    './code-structure-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: LOCKABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => CodeStructureComponent),
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'product-parameter',
      multi: true,
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/action',
      multi: true,
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/dialog',
      multi: true,
    },
  ],
  standalone: true,
  imports: [
    TranslocoModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    PushPipe,
    BlockReloadDirective,
    CodeStructureResultComponent,
    MasterDetailPanelsLayoutComponent,
    FixedCharacteristicEditInfoIconComponent,
    CharacteristicsListViewEditComponent,
    CharacteristicCodesListViewEditComponent,
    ProductCharacteristicStoreModule,
  ],
})
export class CodeStructureComponent implements OnInit, LockableComponent {
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _dialogService = inject(DialogService);
  private readonly _productCharacteristicSelectionService = inject(
    ProductCharacteristicSelectionService
  );
  private readonly _productCharacteristicsFormHelper = inject(
    ProductCharacteristicsFormHelper
  );
  private readonly _productCharacteristicsReloadService = inject(
    ProductCharacteristicsReloadService
  );
  private readonly _productCharacteristicsService = inject(
    ProductCharacteristicsService
  );

  private readonly _productCharacteristicsFormValidationService = inject(
    ProductCharacteristicsFormValidationService
  );

  private readonly _translocoService = inject(TranslocoService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  readonly productCharacteristicsFormService = inject(
    ProductCharacteristicsFormService
  );

  CODE_SIZE_MAX = CODE_SIZE_MAX;
  saveInProgress = false;
  selectedCharacteristicCodesAreLocked$: Observable<boolean | undefined> =
    this._productCharacteristicSelectionService
      .selectedCharacteristicCodesAreLocked$;

  selectCharacteristicsLength$: Observable<number> =
    this.productCharacteristicsFormService.characteristicsCodeSizesSum$;

  productCharacteristicsCodeSizesUnderZero$ =
    this._productCharacteristicsFormValidationService.codeSizesUnderZero$;

  productCharacteristicsCodeSizesEmpty$ =
    this._productCharacteristicsFormValidationService.codeSizesEmpty$;

  canDisplayVisualizationPanel$ = combineLatest([
    this.selectCharacteristicsLength$,
    this.productCharacteristicsCodeSizesUnderZero$,
    this.productCharacteristicsCodeSizesEmpty$,
  ]).pipe(
    map(
      ([
        characteristicsLength,
        productCharacteristicsCodeSizesUnderZero,
        productCharacteristicsCodeSizesEmpty,
      ]) => {
        return (
          characteristicsLength <= CODE_SIZE_MAX &&
          !productCharacteristicsCodeSizesUnderZero &&
          !productCharacteristicsCodeSizesEmpty
        );
      }
    )
  );

  canDeactivate(): boolean {
    return this._productCharacteristicsReloadService.canReloadCodeStructure();
  }

  ngOnInit(): void {
    this._productCharacteristicsService
      .getCharacteristics$()
      .pipe(
        tap((characteristics: ProductCharacteristic[]) => {
          this.productCharacteristicsFormService.initFormGroup(characteristics);
          this._productCharacteristicsReloadService.initialCharacteristics =
            this.productCharacteristicsFormService.productCharacteristicsFormArray.getRawValue() as ProductCharacteristic[];
          this._cdr.markForCheck();
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  onSave(): void {
    of(this.saveInProgress)
      .pipe(
        filter((isInProgress: boolean) => !isInProgress),
        tap(() => (this.saveInProgress = true)),
        switchMap(() => this.confirmSave$()),
        switchMap((saveConfirmed: boolean) =>
          iif(
            () => saveConfirmed,
            defer(() => this.getSaveObservable$()),
            EMPTY
          )
        ),
        finalize(() => {
          this.saveInProgress = false;
          this._cdr.markForCheck();
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  getSaveObservable$(): Observable<void> {
    const save$ = this._productCharacteristicsService.saveCharacteristics$(
      this.productCharacteristicsFormService.productCharacteristicsFormArray.getRawValue() as Array<ProductCharacteristic>
    );

    return save$.pipe(
      tap(
        () =>
          (this._productCharacteristicsReloadService.initialCharacteristics =
            this.productCharacteristicsFormService.productCharacteristicsFormArray.getRawValue() as Array<ProductCharacteristic>)
      ),
      tap({
        next: () => {
          this.productCharacteristicsFormService.resetFormStatus();
          this._store.dispatch(
            ProductCharacteristicActions.characteristicsSaved()
          );
        },
      })
    );
  }

  confirmSave$(): Observable<boolean> {
    const codeSizeWarning = this.getCodeSizeWarnings();

    return forkJoin({
      confirmSaveMessage: this.getConfirmSaveMessage$(),
      characteristicsReorderWarningMessage:
        this.getCharacteristicsReorderWarningMessage$(),
    }).pipe(
      switchMap(
        (result: {
          confirmSaveMessage: string;
          characteristicsReorderWarningMessage: string;
        }) => {
          const messagesToShow: string[] = [...codeSizeWarning];
          if (
            this.productCharacteristicsFormService
              .characteristicsHaveBeenReorderedSnapshot
          ) {
            messagesToShow.push(result.characteristicsReorderWarningMessage);
          }
          messagesToShow.push(result.confirmSaveMessage);

          return this._dialogService.confirm(messagesToShow);
        }
      ),
      take(1)
    );
  }

  cancel(): void {
    this._translocoService
      .selectTranslate(
        'sharedDialog.default-messages.confirm-loss-unsaved-changes'
      )
      .pipe(
        switchMap((messageConfirm: string) =>
          this._dialogService.confirm(messageConfirm)
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((confirmCancel: boolean) => {
        if (confirmCancel) {
          this.productCharacteristicsFormService.resetForm(
            this._productCharacteristicsReloadService.initialCharacteristics
          );
        }
      });
  }

  getCodeSizeWarnings(): string[] {
    const codesWarning = this._productCharacteristicsFormHelper.checkCodesSize(
      this.productCharacteristicsFormService.productCharacteristicsFormArray
    );
    if (codesWarning.length === 0) return [];

    return codesWarning.map(({ expectedSize, characteristicLabel }) =>
      this._translocoService.translate(
        'code-structure-tab.validation.warnings.code-size',
        {
          expectedSize,
          characteristicLabel,
        },
        'product-parameter'
      )
    );
  }

  getConfirmSaveMessage$(): Observable<string> {
    return this._translocoService
      .selectTranslate(
        'code-structure-tab.save.confirm-message',
        {},
        'product-parameter'
      )
      .pipe(take(1));
  }

  getCharacteristicsReorderWarningMessage$(): Observable<string> {
    return this._translocoService
      .selectTranslate(
        'code-structure-tab.validation.characteristics-have-been-reordered',
        {},
        'product-parameter'
      )
      .pipe(take(1));
  }
}
