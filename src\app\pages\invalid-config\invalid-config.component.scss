.modal-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  width: 300px;
  height: 600px;
  padding: 32px 24px;
  gap: 24px;

  p {
    margin: 0;
  }

  .label {
    text-align: left;
    line-height: 1.4;
    margin-bottom: 16px;
  }

  .form {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;

    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;
    }

    .button-container {
      margin-top: auto;
      padding-top: 32px;
      border-top: 1px solid;
    }

    .mat-mdc-raised-button {
      border-radius: 12px;
      min-height: 52px;
      width: 100%;
    }
  }
}
