import { inject, Injectable } from '@angular/core';
import { DebStore } from '@gc/core/deb/application/store';
import { LoadDraftDeclarationsPort } from '@gc/core/deb/domains/ports';

@Injectable()
export class DraftDeclarationsUseCase {
  private readonly store = inject(DebStore);
  private readonly loadDraftDeclarations = inject(LoadDraftDeclarationsPort);

  with(filters: {
    includeDeliveryNotes?: boolean;
    includeInvoices?: boolean;
    declarationMonth?: Date;
    companyId?: string;
  }) {
    return this.loadDraftDeclarations.with(filters);
  }
}
