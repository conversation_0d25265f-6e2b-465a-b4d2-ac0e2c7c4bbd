import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { PushPipe } from '@ngrx/component';
import { TranslocoModule } from '@jsverse/transloco';
import { Commission } from '@gc/core/sales/commissions/domains/models';
import { CurrencyInputComponent, LoaderComponent } from '@gc/shared/ui';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CurrencyService } from '@gc/currency/data-access';
import { defaultIfEmpty } from 'rxjs';
import { Currency } from '@gc/shared/models';
import { commissionsInfrastructureProviders } from '@gc/core/sales/commissions/infrastructure';
import { commissionsServicesProviders } from '@gc/core/sales/commissions';
import { MatInputModule } from '@angular/material/input';
import { RepresentativesFacade } from '@gc/core/sales/representatives/application/facades';
import { representativesInfrastructureProviders } from '@gc/core/sales/representatives/infrastructure';
import { representativesServicesProviders } from '@gc/core/sales/representatives';

@Component({
  selector: 'gc-payout-table-form',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslocoModule,
    MatProgressSpinnerModule,
    LoaderComponent,
    CurrencyInputComponent,
    PushPipe,
  ],
  providers: [
    ...commissionsInfrastructureProviders(),
    ...commissionsServicesProviders(),
    ...representativesServicesProviders(),
    ...representativesInfrastructureProviders(),
  ],
  templateUrl: './payout-table-form.component.html',
  styleUrls: ['./payout-table-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PayoutTableFormComponent {
  private readonly commissionFacade = inject(CommissionsFacade);
  private readonly representativesFacade = inject(RepresentativesFacade);
  private readonly currencyService = inject(CurrencyService);
  formGroup!: FormGroup;
  commission: Commission;
  selectedRepresentative =
    this.representativesFacade.getSelectedRepresentative();
  currency$ = this.currencyService
    .getDefaultCurrency$()
    .pipe(defaultIfEmpty({} as Currency));

  constructor(
    private readonly fb: FormBuilder,
    private readonly dialogRef: MatDialogRef<PayoutTableFormComponent>,
    @Inject(MAT_DIALOG_DATA)
    private readonly dialogData: { commission: Commission }
  ) {
    this.commission = this.dialogData.commission;
    this.initializeForm(this.commission);
  }

  calculateCommissionAmount(formGroup: FormGroup): void {
    const amountToBePaid: number = formGroup.get('amountToBePaid')?.value || 0;
    const commissionAmount: number =
      this.commission.amount - this.commission.amountToBePaid;
    formGroup.patchValue({
      commissionAmount: amountToBePaid + commissionAmount,
    });
  }

  calculateAmountToBePaid(formGroup: FormGroup): void {
    const commissionAmount: number =
      formGroup.get('commissionAmount')?.value || 0;
    const amountToBePaid: number =
      this.commission.amount - this.commission.amountToBePaid;
    formGroup.patchValue({
      amountToBePaid: commissionAmount - amountToBePaid,
    });
  }

  onSubmit(): void {
    this.commissionFacade.updateCommissionWith({} as Commission);
    this.commissionFacade.getCommissionDetails();
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  get commissionAmountFC() {
    return this.formGroup.get('commissionAmount')!;
  }

  get amountToBePaidFC() {
    return this.formGroup.get('amountToBePaid')!;
  }

  private initializeForm(commission: Commission): void {
    this.formGroup = this.fb.group({
      amountToBePaid: [
        commission.amountToBePaid,
        [Validators.required, Validators.min(0.01)],
      ],
      commissionAmount: [
        commission.amount,
        [Validators.required, Validators.min(0.01)],
      ],
    });

    this.amountToBePaidFC.valueChanges.subscribe(() =>
      this.calculateCommissionAmount(this.formGroup)
    );

    this.commissionAmountFC.valueChanges.subscribe(() =>
      this.calculateAmountToBePaid(this.formGroup)
    );
  }
}
