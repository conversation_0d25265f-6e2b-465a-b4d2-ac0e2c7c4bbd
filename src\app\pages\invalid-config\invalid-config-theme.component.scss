@use '@angular/material' as mat;
@use 'gc-material-typography' as gc-material-typography;
@use 'gc-colors';

.modal-overlay {
  @include mat.m2-typography-level(
    gc-material-typography.$typography,
    'body-2'
  );

  .container {
    background: gc-colors.$main-surface-color;
    border-color: gc-colors.$main-surface-color-contrast;
    box-shadow: 0 4px 12px gc-colors.$box-shadow-color;

    .form {
      .button-container {
        border-color: gc-colors.$box-shadow-color;
      }

      .mat-mdc-raised-button {
        &:not(:disabled) {
          box-shadow: 0 4px 12px gc-colors.$box-shadow-color;
        }
      }
    }
  }
}
