import { TestBed, waitForAsync } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  RouterModule,
  RouterStateSnapshot,
} from '@angular/router';
import { MockProvider } from 'ng-mocks';
import { TranslocoModule } from '@jsverse/transloco';
import { setHeaderTitleGuard } from './header-title.guard';
import { HeaderTitleService } from '../../services/header-title.service';

describe('setHeaderTitleGuard', () => {
  let headerTitleService: HeaderTitleService;
  let headerTitleServiceSpy: jest.SpyInstance<void, [titleKey: string], any>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterModule, TranslocoModule],
      providers: [MockProvider(HeaderTitleService)],
    });

    headerTitleService = TestBed.inject(HeaderTitleService);
    headerTitleServiceSpy = jest.spyOn(
      headerTitleService,
      'updateCurrentTitle'
    );
  });

  it('should update the header title and return true', waitForAsync(() => {
    const titleKey = 'newTitle';
    const guard = setHeaderTitleGuard(titleKey);

    const route: ActivatedRouteSnapshot = {} as ActivatedRouteSnapshot;
    const state: RouterStateSnapshot = {} as RouterStateSnapshot;

    const canActivate = TestBed.runInInjectionContext(() => {
      return guard(route, state);
    });

    expect(headerTitleServiceSpy).toHaveBeenCalledWith(titleKey);
    expect(canActivate).toBe(true);
  }));
});
