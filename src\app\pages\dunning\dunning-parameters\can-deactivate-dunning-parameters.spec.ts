import { waitForAsync } from '@angular/core/testing';
import {
  dunningParametersInitialState,
  DunningParametersState,
  selectCurrentLetterHasUnsavedChanges,
} from '@gc/dunning/data-access';
import { DialogService } from '@gc/shared/ui';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { Observable, of } from 'rxjs';
import { CanDeactivateDunningParametersGuard } from './can-deactivate-dunning-parameters.guard';

describe('CanDeactivateDunningParametersGuard', () => {
  let spectator: SpectatorService<CanDeactivateDunningParametersGuard>;
  let store: MockStore<DunningParametersState>;

  let translocoService: TranslocoService;
  let dialogService: DialogService;

  let confirmSpy: jest.SpyInstance<Observable<boolean>>;

  const initialState = dunningParametersInitialState;

  const createService = createServiceFactory({
    service: CanDeactivateDunningParametersGuard,
    providers: [provideMockStore({ initialState })],
    mocks: [TranslocoService, DialogService],
  });

  beforeEach(() => {
    spectator = createService();
    store = spectator.inject(MockStore);
    translocoService = spectator.inject(TranslocoService);
    dialogService = spectator.inject(DialogService);

    confirmSpy = jest.spyOn(dialogService, 'confirm');

    store.overrideSelector(selectCurrentLetterHasUnsavedChanges, false);
    store.refreshState();
  });

  it('should be created', () => {
    expect(spectator).toBeTruthy();
    expect(spectator.service).toBeTruthy();
  });

  describe('canDeactivate method', () => {
    describe('Given a state where the selectTranslate method returns a confirm message', () => {
      const confirmMessage = 'confirmMessage';
      beforeEach(() => {
        jest
          .spyOn(translocoService, 'selectTranslate')
          .mockReturnValue(of(confirmMessage));
      });

      describe('given a state where the selectCurrentLetterHasUnsavedChanges is set to false', () => {
        beforeEach(() => {
          store.overrideSelector(selectCurrentLetterHasUnsavedChanges, false);
          store.refreshState();
        });

        describe('when canDeactivate is called', () => {
          it('should return an observable with the value true', waitForAsync(() => {
            const result$ = spectator.service.canDeactivate();

            result$.subscribe((canDeactivate: boolean) => {
              expect(canDeactivate).toBe(true);
            });
          }));
        });
      });

      describe('given a state where the selectCurrentLetterHasUnsavedChanges is set to true', () => {
        beforeEach(() => {
          store.overrideSelector(selectCurrentLetterHasUnsavedChanges, true);
          store.refreshState();
        });

        describe('and when the user confirms the loss of unsaved changes', () => {
          beforeEach(() => {
            confirmSpy.mockReturnValue(of(true));
          });

          describe('when canDeactivate is called', () => {
            it('should return an observable with the value true', waitForAsync(() => {
              const result$ = spectator.service.canDeactivate();

              result$.subscribe((canDeactivate: boolean) => {
                expect(canDeactivate).toBe(true);
              });
            }));
          });
        });

        describe('and when the user cancel the navigation to keep the unsaved changes', () => {
          beforeEach(() => {
            confirmSpy.mockReturnValue(of(false));
          });

          describe('when canDeactivate is called', () => {
            it('should return an observable with the value true', waitForAsync(() => {
              const result$ = spectator.service.canDeactivate();

              result$.subscribe((canDeactivate: boolean) => {
                expect(canDeactivate).toBe(false);
              });
            }));
          });
        });
      });
    });
  });
});
