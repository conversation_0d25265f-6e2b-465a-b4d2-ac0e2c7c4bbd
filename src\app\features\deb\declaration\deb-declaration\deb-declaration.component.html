<ng-container *transloco="let t">
  <div class="flex items-center">
    @if ((singleCompanyId$ | ngrxPush) === undefined) {
      <gc-company-single-select
        [formControl]="companyIdControl"
        [inputId]="'companies-select'"
        [label]="t('company.companies-select.label')"
        [invalid]="
          companyIdControl.invalid &&
          (companyIdControl.dirty || companyIdControl.touched)
        "
        [errorText]="t('sharedForm.error.required')" />
    }

    @if (closedMonths().data?.length) {
      <button
        mat-raised-button
        color="primary"
        type="button"
        class="ml-auto"
        (click)="onParametersButtonClick($event)">
        {{ t('sharedAction.parameters') }}
        <mat-icon>settings</mat-icon>
      </button>
    }
  </div>

  <mat-card class="mt-4 grow">
    <mat-card-content class="h-full flex flex-column gap-05">
      @if (closedMonths().status && declarationMonth()) {
        <div class="flex gap-05 items-center">
          @if (parameters().isLoading) {
            <gc-loader
              [label]="t('deb.statement-page.parameters.is-loading')" />
          } @else {
            <gc-deb-parameters
              [parameters]="parameters().data"
              class="h-full" />
          }
          <gc-deb-filters
            [declarationMonth]="declarationMonth()!"
            (applyFilters)="onApplyFilters($event)"
            class="h-full" />
        </div>
        <gc-deb-declaration-documents-overview
          *ngrxLet="{
            currency: currency$,
          } as x"
          [inputDraftDeclarationsState]="draftDeclarations()"
          [currency]="x.currency"
          [companyId]="companyId()"
          [filters]="filters()"
          [areFiltersApplied]="areFiltersApplied()"
          class="grow"
          (declarations)="onDeclarationsChange($event)" />
        <div class="actions">
          <button
            mat-stroked-button
            color="primary"
            type="button"
            (click)="onHistoryButtonClick()">
            {{ t('deb.statement-page.actions.history') }}
          </button>
          <button
            [disabled]="!hasMissingParameters() && !areFiltersApplied()"
            mat-raised-button
            color="primary"
            type="button"
            (click)="onSaveClosureButtonClick()">
            {{
              t('deb.statement-page.actions.close-month', {
                month:
                  declarationMonth()!
                  | translocoDate
                    : {
                        year: 'numeric',
                        month: 'long',
                      }
                  | capitalize,
              })
            }}
          </button>
        </div>
      } @else if (closedMonths().isLoading) {
        <div class="closed-month-loading-container">
          <gc-loader
            [label]="t('deb.statement-page.closed-month.is-loading')" />
        </div>
      } @else {
        <div class="no-enclose-month-container">
          <p>
            {{ t('deb.statement-page.no-closed-month.label') }}
          </p>
          <button
            mat-stroked-button
            color="primary"
            (click)="onNoClosedMonthButtonClick()">
            {{ t('deb.statement-page.no-closed-month.action') }}
          </button>
        </div>
      }
    </mat-card-content>
  </mat-card>
</ng-container>
