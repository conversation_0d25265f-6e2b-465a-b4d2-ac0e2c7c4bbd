<div class="container">
  <quill-editor
    #quillComponent
    [ngModel]="getValue$() | async"
    (ngModelChange)="setValue($event)"
    [customOptions]="customOptions"
    class="quill-editor"
    format="html"
    placeholder="{{ placeholder }}"
    defaultEmptyValue=""
    source="user">
    <div quill-editor-toolbar>
      <span class="ql-formats">
        <select class="ql-font">
          @for (font of customFonts; track fontTrackBy($index, font)) {
            <option [value]="font">
              {{ font }}
            </option>
          }
        </select>
      </span>

      <span class="ql-formats">
        <select class="ql-size">
          @for (
            fontSize of customFontSizes;
            track fontSizeTrackBy($index, fontSize)
          ) {
            <option [value]="fontSize">
              {{ fontSize }}
            </option>
          }
        </select>
      </span>

      <span class="ql-formats">
        <button aria-label="Bold" type="button" class="ql-bold"></button>
        <button aria-label="Italic" type="button" class="ql-italic"></button>
        <button
          aria-label="Underline"
          type="button"
          class="ql-underline"></button>
      </span>

      <span class="ql-formats">
        <select class="ql-color"></select>

        <select class="ql-background"></select>
      </span>

      <span class="ql-formats">
        <button
          value="ordered"
          aria-label="Ordered List"
          type="button"
          class="ql-list"></button>
        <button
          value="bullet"
          aria-label="Unordered List"
          type="button"
          class="ql-list"></button>
      </span>

      <span class="ql-formats">
        <select class="ql-align"></select>
      </span>
    </div>
  </quill-editor>
</div>
