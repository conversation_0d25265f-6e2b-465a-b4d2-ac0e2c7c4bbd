import { Injectable } from '@angular/core';
import { BaseStore, ResourceState } from '@gc/core/shared/store';
import { Commission, CommissionDetails } from '../../domains/models';

export enum CommissionsStoreEnum {
  FILTERS = 'FILTERS',
  COMMISSIONS = 'COMMISSIONS',
  COMMISSION_DETAILS = 'COMMISSION_DETAILS',
}

export type CommissionsState = {
  [CommissionsStoreEnum.FILTERS]: ResourceState<unknown>;
  [CommissionsStoreEnum.COMMISSIONS]: ResourceState<Commission[]>;
  [CommissionsStoreEnum.COMMISSION_DETAILS]: ResourceState<CommissionDetails[]>;
};

@Injectable({
  providedIn: 'root',
})
export class CommissionsStore extends BaseStore<
  typeof CommissionsStoreEnum,
  CommissionsState
> {
  constructor() {
    super(CommissionsStoreEnum);
  }
}
