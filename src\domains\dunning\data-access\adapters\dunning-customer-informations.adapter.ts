import { DunningCustomerInformationsApi } from '@gc/shared/api/data-access';
import { DunningCustomerInformations } from '@gc/dunning/models';
import { DunningCustomerTypeAdapter } from './dunning-customer-type.adapter';

export class DunningCustomerInformationsAdapter {
  public static fromApi(
    dunningCustomerInformationsApi: DunningCustomerInformationsApi
  ): DunningCustomerInformations {
    const {
      phoneNumber,
      eMail: email,
      postCode,
      townOrVillage,
      lastName,
      firstName,
      companyName,
    } = dunningCustomerInformationsApi;

    return {
      phoneNumber: phoneNumber ?? undefined,
      email: email ?? undefined,
      postCode: postCode ?? undefined,
      townOrVillage: townOrVillage ?? undefined,
      lastName: lastName ?? undefined,
      firstName: firstName ?? undefined,
      companyName: companyName ?? undefined,
      customerType: DunningCustomerTypeAdapter.fromApi(
        dunningCustomerInformationsApi.customerType
      ),
    };
  }
}
