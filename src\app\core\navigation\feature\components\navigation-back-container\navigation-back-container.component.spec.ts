import { Spectator } from '@ngneat/spectator';
import { createComponentFactory } from '@ngneat/spectator/jest';
import { MockComponents } from 'ng-mocks';
import { NavigationBackContainerComponent } from './navigation-back-container.component';
import { HeaderComponent } from '../header/header.component';
import { SubHeaderComponent } from '@gc/core/navigation/ui';

describe('NavigationBackContainerComponent', () => {
  let spectator: Spectator<NavigationBackContainerComponent>;
  const createComponent = createComponentFactory({
    component: NavigationBackContainerComponent,
    declarations: [MockComponents(HeaderComponent, SubHeaderComponent)],
  });

  beforeEach(async () => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
  });
});
