import { formatCurrency } from '@angular/common';
import { Pipe, PipeTransform, inject } from '@angular/core';
import { Currency } from '@gc/shared/models';
import { TranslocoLocaleService } from '@jsverse/transloco-locale';

@Pipe({
  name: 'amountWithCurrency',
  standalone: true,
})
export class AmountWithCurrencyPipe implements PipeTransform {
  private readonly _transloco = inject(TranslocoLocaleService);

  transform(amount: number, currency: Currency): string {
    /* All amount of the application have the same currency which is the one of the user domain.
        The currency is not the one of the local. */
    return this._format(amount, currency);
  }

  private _format(amount: number, currency: Currency): string {
    const locale = this._transloco.getLocale();
    return formatCurrency(
      amount,
      locale,
      currency?.symbol ?? '',
      currency?.code ?? ''
    );
  }
}
