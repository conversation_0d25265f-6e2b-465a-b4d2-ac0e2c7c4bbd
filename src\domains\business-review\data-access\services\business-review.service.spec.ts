import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';

import { Observable, of } from 'rxjs';

import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { VatService } from '@gc/vat/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { Warehouse, warehousesFixture } from '@gc/warehouse/models';
import { GuidHelper } from '@isagri-ng/core';
import { BusinessReviewService } from './business-review.service';

describe('BusinessReviewService', () => {
  let spectator: SpectatorService<BusinessReviewService>;
  let vatService: VatService;
  let warehouseService: WarehouseService;
  let fiscalYearService: FiscalYearService;
  let getVatRatesSpy: jest.SpyInstance<Observable<number[]>>;
  let getWarehousesSpy: jest.SpyInstance<Observable<Warehouse[]>>;
  let getSharedFiscalYearsDateRangesSpy: jest.SpyInstance<
    Observable<FiscalYearDateRange[]>
  >;

  const createService = createServiceFactory({
    service: BusinessReviewService,
    mocks: [VatService, WarehouseService, FiscalYearService],
  });

  beforeEach(() => {
    spectator = createService();
    fiscalYearService = spectator.inject(FiscalYearService);
    vatService = spectator.inject(VatService);
    warehouseService = spectator.inject(WarehouseService);
  });

  it('should create', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('getData method', () => {
    beforeEach(() => {
      getVatRatesSpy = jest.spyOn(
        vatService,
        'getDeduplicatedRatesValuesByCompanies'
      );

      getWarehousesSpy = jest.spyOn(warehouseService, 'getWarehouses');
    });

    describe('when the method is called with an empty array of companyIds', () => {
      const expectedData = {
        vatRates: [],
        warehouses: [],
      };

      describe('given any state', () => {
        it('should not call the vat service', () => {
          expect.assertions(1);
          spectator.service.getData([]).subscribe(() => {
            expect(getVatRatesSpy).not.toHaveBeenCalled();
          });
        });
        it('should not call the warehouse service', () => {
          expect.assertions(1);
          spectator.service.getData([]).subscribe(() => {
            expect(getWarehousesSpy).not.toHaveBeenCalled();
          });
        });

        it('should return an observable with the expected data', () => {
          expect.assertions(1);
          spectator.service.getData([]).subscribe((data) => {
            expect(data).toEqual(expectedData);
          });
        });

        it('should set the warehouses$$ behavior subject with an empty array', () => {
          expect.assertions(1);
          spectator.service.getData([]).subscribe(() => {
            expect(spectator.service.warehouses$$.value).toEqual([]);
          });
        });

        it('should set the vatRates$$ behavior subject with an empty array', () => {
          expect.assertions(1);
          spectator.service.getData([]).subscribe(() => {
            expect(spectator.service.vatRates$$.value).toEqual([]);
          });
        });
      });
    });

    describe('when the method is called with a non-empty array of companyIds', () => {
      const companyIds = [GuidHelper.newGuid(), GuidHelper.newGuid()];

      describe('and given a state where the vat and warehouse services returns a value', () => {
        const vatRates = [8.2, 20, 12.5, 35.5];
        const warehouses = warehousesFixture();

        beforeEach(() => {
          getVatRatesSpy = jest
            .spyOn(vatService, 'getDeduplicatedRatesValuesByCompanies')
            .mockReturnValue(of(vatRates));

          getWarehousesSpy = jest
            .spyOn(warehouseService, 'getWarehouses')
            .mockReturnValue(of(warehouses));
        });

        it('should call the vat service with the companyIds', () => {
          expect.assertions(1);
          spectator.service.getData(companyIds).subscribe(() => {
            expect(getVatRatesSpy).toHaveBeenCalledWith(companyIds);
          });
        });

        it('should call the warehouse service with the companyIds', () => {
          expect.assertions(1);
          spectator.service.getData(companyIds).subscribe(() => {
            expect(getWarehousesSpy).toHaveBeenCalledWith(companyIds);
          });
        });

        it('should return an observable with the expected data', () => {
          const expectedData = {
            vatRates: vatRates,
            warehouses: warehouses,
          };

          expect.assertions(1);
          spectator.service.getData(companyIds).subscribe((data) => {
            expect(data).toEqual(expectedData);
          });
        });

        it('should set the warehouses$$ behavior subject with the warehouses', () => {
          expect.assertions(1);
          spectator.service.getData(companyIds).subscribe(() => {
            expect(spectator.service.warehouses$$.value).toEqual(warehouses);
          });
        });

        it('should set the vatRates$$ behavior subject with the vat rates', () => {
          expect.assertions(1);
          spectator.service.getData(companyIds).subscribe(() => {
            expect(spectator.service.vatRates$$.value).toEqual(vatRates);
          });
        });
      });
    });
  });

  describe('getFiscalYears method', () => {
    beforeEach(() => {
      getSharedFiscalYearsDateRangesSpy = jest.spyOn(
        fiscalYearService,
        'getSharedFiscalYearsDateRanges'
      );
    });

    describe('when the method is called with an empty array of companyIds', () => {
      it('should not call the fiscal year service', () => {
        expect.assertions(1);
        spectator.service.getFiscalYears([]).subscribe(() => {
          expect(getSharedFiscalYearsDateRangesSpy).not.toHaveBeenCalled();
        });
      });

      it('should return an observable with an empty array', () => {
        expect.assertions(1);
        spectator.service.getFiscalYears([]).subscribe((data) => {
          expect(data).toEqual([]);
        });
      });
    });

    describe('when the method is called with a non-empty array of companyIds', () => {
      const companyIds = [GuidHelper.newGuid(), GuidHelper.newGuid()];

      describe('and given a state where the fiscal year service returns a value', () => {
        const fiscalYearDateRanges: FiscalYearDateRange[] = [
          {
            dateFrom: new Date('2021-01-01'),
            dateTo: new Date('2021-12-31'),
          },
          {
            dateFrom: new Date('2022-01-01'),
            dateTo: new Date('2022-12-31'),
          },
        ];
        beforeEach(() => {
          getSharedFiscalYearsDateRangesSpy.mockReturnValue(
            of(fiscalYearDateRanges)
          );
        });

        it('should call the fiscal year service with the companyIds', () => {
          expect.assertions(1);
          spectator.service.getFiscalYears(companyIds).subscribe(() => {
            expect(getSharedFiscalYearsDateRangesSpy).toHaveBeenCalledWith(
              companyIds
            );
          });
        });

        it('should return an observable with an empty array', () => {
          expect.assertions(1);
          spectator.service.getFiscalYears(companyIds).subscribe((data) => {
            expect(data).toEqual(fiscalYearDateRanges);
          });
        });
      });
    });
  });
});
