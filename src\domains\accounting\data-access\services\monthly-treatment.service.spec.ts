import { waitForAsync } from '@angular/core/testing';
import {
  EnclosingMonthInformations,
  MonthlyTreatmentEditionsEnum,
  MonthlyTreatmentEditionsFiltersModel,
  SalesByProductFilters,
  SalesDetailsFilters,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';

import {
  BusinessResultAPIApi,
  BooleanBusinessResultAPIApi,
  GuidBusinessResultAPIApi,
  MonthlyAccountingReportsAvailabilityBusinessResultAPIApi,
  MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi,
  MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi,
  MonthlyAccountingReportsDocumentsBusinessResultAPIApi,
  MonthlyAccountingReportsMonthsBusinessResultAPIApi,
  MonthlyAccountingReportsStartMonthsBusinessResultAPIApi,
  MonthlyAccountingReportsYearsBusinessResultAPIApi,
  MonthlyAccountingReportsDefaultParameter<PERSON>pi,
  MonthlyAccountingReportsDocumentsApi,
  MonthlyAccountingReportsCategorySalesKindApi,
  LegacyApiService,
} from '@gc/shared/api/data-access';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { Observable, of } from 'rxjs';
import { MonthlyTreatmentService } from './monthly-treatment.service';
import { DateAdapter } from '@gc/shared/utils';

describe('MonthlyTreatmentService', () => {
  let spectator: SpectatorService<MonthlyTreatmentService>;
  let service: MonthlyTreatmentService;
  let legacyApiService: LegacyApiService;

  const monthlyFilters: MonthlyTreatmentEditionsFiltersModel = {
    salesDetailOrder: SalesDetailsOrderByEnum.BUSINESS_TYPE,
    productCategories: [ProductCharacteristicEnum.PRODUCT_FAMILY],
  };

  const createService = createServiceFactory({
    service: MonthlyTreatmentService,
    mocks: [LegacyApiService],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
    legacyApiService = spectator.inject(LegacyApiService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getCompanyEnclosedYears', () => {
    describe('given any state', () => {
      describe('when method is called with a company id', () => {
        it('should return a list of years', waitForAsync(() => {
          const br: MonthlyAccountingReportsYearsBusinessResultAPIApi = {
            result: {
              years: [2020, 2021],
            },
          };
          const monthlyAccountingReportsGetClosedMonthlyTreatmentYearsSpy =
            jest.spyOn(
              legacyApiService,
              'monthlyAccountingReportsGetClosedMonthlyTreatmentYears'
            ) as unknown as jest.SpyInstance<
              Observable<MonthlyAccountingReportsYearsBusinessResultAPIApi>
            >;

          monthlyAccountingReportsGetClosedMonthlyTreatmentYearsSpy.mockReturnValue(
            of(br)
          );

          service
            .getCompanyEnclosedYears('anyCompanyId')
            .subscribe((result) => {
              expect(result).toStrictEqual([2020, 2021]);
            });
        }));
      });
    });
  });

  describe('getCompanyYearEnclosedMonths', () => {
    describe('given any state', () => {
      describe('when method is called with a company id and a year', () => {
        it('should return a list of months', waitForAsync(() => {
          const br: MonthlyAccountingReportsMonthsBusinessResultAPIApi = {
            result: {
              months: [1, 2],
            },
          };
          const monthlyAccountingReportsGetClosedMonthlyTreatmentMonthsSpy =
            jest.spyOn(
              legacyApiService,
              'monthlyAccountingReportsGetClosedMonthlyTreatmentMonths'
            ) as unknown as jest.SpyInstance<
              Observable<MonthlyAccountingReportsMonthsBusinessResultAPIApi>
            >;

          monthlyAccountingReportsGetClosedMonthlyTreatmentMonthsSpy.mockReturnValue(
            of(br)
          );

          service
            .getCompanyYearEnclosedMonths('anyCompanyId', 2024)
            .subscribe((result) => {
              expect(result[0].getMonth()).toBe(0);
              expect(result[0].getFullYear()).toBe(2024);
              expect(result[1].getMonth()).toBe(1);
              expect(result[1].getFullYear()).toBe(2024);
            });
        }));
      });
    });
  });

  describe('getAvailableEditions', () => {
    describe('given any state', () => {
      describe('when method is called with a company id and a month', () => {
        it('should return a MonthlyTreatmentAvailableEditions value', waitForAsync(() => {
          const br: MonthlyAccountingReportsAvailabilityBusinessResultAPIApi = {
            result: {
              categorySalesExist: true,
              saleDetailsExist: false,
              unpaidListingExist: true,
              paymentListingExist: true,
            },
          };
          const monthlyAccountingReportsGetMonthlyTreatmentDocumentsExistanceSpy =
            jest.spyOn(
              legacyApiService,
              'monthlyAccountingReportsGetMonthlyTreatmentDocumentsExistance'
            ) as unknown as jest.SpyInstance<
              Observable<MonthlyAccountingReportsAvailabilityBusinessResultAPIApi>
            >;

          monthlyAccountingReportsGetMonthlyTreatmentDocumentsExistanceSpy.mockReturnValue(
            of(br)
          );

          service
            .getAvailableEditions('anyCompanyId', new Date('2024-02-01'))
            .subscribe((result) => {
              expect(result).toStrictEqual([
                {
                  id: MonthlyTreatmentEditionsEnum.SALES,
                  isAvailable: true,
                },
                {
                  id: MonthlyTreatmentEditionsEnum.DETAILS,
                  isAvailable: false,
                },
                {
                  id: MonthlyTreatmentEditionsEnum.RECEIPTS,
                  isAvailable: true,
                },
                {
                  id: MonthlyTreatmentEditionsEnum.DEBT,
                  isAvailable: true,
                },
                {
                  id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
                  isAvailable: false,
                },
              ]);
            });
        }));
      });
    });
  });

  describe('getSalesByProductTreatmentId', () => {
    let stimulsoftApiSalesByProductCategorySpy: jest.SpyInstance;

    beforeEach(() => {
      stimulsoftApiSalesByProductCategorySpy = jest.spyOn(
        legacyApiService,
        'stimulsoftTreatmentsGenerateTreatmentSalesByProductCategory'
      ) as unknown as jest.SpyInstance<Observable<GuidBusinessResultAPIApi>>;
    });

    describe('given a state where the API return a treatment id', () => {
      const expectedTreatmentId = 'treatmentID';

      const productCategorySalesFilterApi: GuidBusinessResultAPIApi = {
        result: expectedTreatmentId,
      };

      it('should return treatment id', waitForAsync(() => {
        const filters: SalesByProductFilters = {
          categories: [ProductCharacteristicEnum.PRODUCT_FAMILY],
          dateFrom: new Date(),
          dateTo: new Date(),
          companyId: 'companyId',
        };

        stimulsoftApiSalesByProductCategorySpy.mockReturnValue(
          of(productCategorySalesFilterApi)
        );

        service.getSalesByProductTreatmentId(filters).subscribe((value) => {
          expect(value).toEqual(expectedTreatmentId);
        });
      }));
    });
  });

  describe('getSalesDetailsTreatmentId', () => {
    let stimulsoftApiSalesDetailsCategorySpy: jest.SpyInstance;

    beforeEach(() => {
      stimulsoftApiSalesDetailsCategorySpy = jest.spyOn(
        legacyApiService,
        'stimulsoftTreatmentsGenerateTreatmentSalesDetails'
      ) as unknown as jest.SpyInstance<Observable<GuidBusinessResultAPIApi>>;
    });
    describe('given a state where the API return a treatment id', () => {
      const expectedTreatmentId = 'treatmentID';

      const productCategorySalesFilterApi: GuidBusinessResultAPIApi = {
        result: expectedTreatmentId,
      };

      it('should return treatment id', waitForAsync(() => {
        const filters: SalesDetailsFilters = {
          orderBy: SalesDetailsOrderByEnum.BUSINESS_TYPE,
          dateFrom: new Date(),
          dateTo: new Date(),
          companyId: 'companyId',
        };

        stimulsoftApiSalesDetailsCategorySpy.mockReturnValue(
          of(productCategorySalesFilterApi)
        );

        service.getSalesDetailsTreatmentId(filters).subscribe((value) => {
          expect(value).toEqual(expectedTreatmentId);
        });
      }));
    });
  });

  describe('getAccountingDefaultMonth', () => {
    let monthlyAccountingReportsLoadDefaultMonthSpy: jest.SpyInstance;

    beforeEach(() => {
      monthlyAccountingReportsLoadDefaultMonthSpy = jest.spyOn(
        legacyApiService,
        'monthlyAccountingReportsLoadDefaultMonth'
      ) as unknown as jest.SpyInstance<Observable<GuidBusinessResultAPIApi>>;
    });

    describe('given a state where the API return a month and noClosedMonths to false', () => {
      const expectedDate = new Date('2024-06-30T22:00:00.000Z');

      const productCategorySalesFilterApi: MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi =
        { result: { month: '2024-07', noClosedMonths: false } };

      it('should return enclosure month date', waitForAsync(() => {
        const companyId = 'companyId';
        const expectedEnclosingMonthInformations: EnclosingMonthInformations = {
          enclosureMonth: expectedDate,
          hasAlreadyEnclosedMonths: true,
        };
        monthlyAccountingReportsLoadDefaultMonthSpy.mockReturnValue(
          of(productCategorySalesFilterApi)
        );

        service.getAccountingDefaultMonth(companyId).subscribe((value) => {
          expect(value).toEqual(expectedEnclosingMonthInformations);
        });
      }));
    });

    describe('given a state where the API return a month and noClosedMonths to true', () => {
      const expectedDate = new Date('2024-06-30T22:00:00.000Z');

      const productCategorySalesFilterApi: MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi =
        { result: { month: '2024-07', noClosedMonths: true } };

      it('should return enclosure month date', waitForAsync(() => {
        const companyId = 'companyId';
        const expectedEnclosingMonthInformations: EnclosingMonthInformations = {
          enclosureMonth: expectedDate,
          hasAlreadyEnclosedMonths: false,
        };
        monthlyAccountingReportsLoadDefaultMonthSpy.mockReturnValue(
          of(productCategorySalesFilterApi)
        );

        service.getAccountingDefaultMonth(companyId).subscribe((value) => {
          expect(value).toEqual(expectedEnclosingMonthInformations);
        });
      }));
    });

    describe('given a state where the API return an undefined result', () => {
      const productCategorySalesFilterApi: MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi =
        { result: undefined };

      it('should return enclosure month date', waitForAsync(() => {
        const companyId = 'companyId';
        monthlyAccountingReportsLoadDefaultMonthSpy.mockReturnValue(
          of(productCategorySalesFilterApi)
        );

        service.getAccountingDefaultMonth(companyId).subscribe((value) => {
          expect(value).toBeNull();
        });
      }));
    });
  });
  describe('getAvailableStartMonths', () => {
    let monthlyAccountingReportsGetAvailableStartMonthsSpy: jest.SpyInstance<
      Observable<MonthlyAccountingReportsStartMonthsBusinessResultAPIApi>
    >;

    describe('given a state where the API return multiples dates of closure month', () => {
      const dateStartClosureMonth1 = '2024-06-28T22:00:00.000Z';
      const dateStartClosureMonth2 = '2024-06-29T22:00:00.000Z';
      const datesStartClosureMonth = [
        dateStartClosureMonth1,
        dateStartClosureMonth2,
      ];

      const datesStartClosureMonthApi: MonthlyAccountingReportsStartMonthsBusinessResultAPIApi =
        { result: { months: datesStartClosureMonth } };

      beforeEach(() => {
        monthlyAccountingReportsGetAvailableStartMonthsSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsGetAvailableStartMonths'
        ) as unknown as jest.SpyInstance<
          Observable<MonthlyAccountingReportsStartMonthsBusinessResultAPIApi>
        >;

        monthlyAccountingReportsGetAvailableStartMonthsSpy.mockReturnValue(
          of(datesStartClosureMonthApi)
        );
      });

      it('should return the available enclosure month dates and manage the loading status', waitForAsync(() => {
        const companyId = 'companyId';
        const expectedDatesStartClosureMonth: (Date | null)[] =
          datesStartClosureMonth.map((dateMonth) =>
            DateAdapter.dateFromStringAPI(dateMonth)
          );

        service.getAvailableStartMonths(companyId).subscribe((value) => {
          expect(value).toEqual(expectedDatesStartClosureMonth);
        });

        service.loadingStatusStartMonths$.subscribe((loadingStatus) => {
          expect(loadingStatus).toEqual('LOADED');
        });
      }));
    });
  });

  describe('saveFilters', () => {
    describe('given any state with filters as arguments', () => {
      const expectedFilters = {
        productCategorySales: [
          MonthlyAccountingReportsCategorySalesKindApi.ProductFamily,
        ],
        salesDetailOrderByBusinessType: true,
      };

      let monthlyAccountingReportsSaveDefaultParametersSpy: jest.SpyInstance;

      beforeEach(() => {
        monthlyAccountingReportsSaveDefaultParametersSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsSaveDefaultParameters'
        ) as unknown as jest.SpyInstance<Observable<BusinessResultAPIApi>>;
      });

      it('should save filters', () => {
        const apiResult = {} as BusinessResultAPIApi;
        monthlyAccountingReportsSaveDefaultParametersSpy.mockReturnValue(
          of(apiResult)
        );
        service.saveFilters(monthlyFilters);

        expect(
          monthlyAccountingReportsSaveDefaultParametersSpy
        ).toHaveBeenCalledWith(expectedFilters);
      });
    });
  });

  describe('getDefaultFilters', () => {
    describe('given any state where monthlyAccountingReportsLoadDefaultParameters return  a MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi', () => {
      const apiResult: MonthlyAccountingReportsDefaultParameterApi = {
        salesDetailOrderByBusinessType: true,
        productCategorySales: [
          MonthlyAccountingReportsCategorySalesKindApi.CustomCharacteristic1,
          MonthlyAccountingReportsCategorySalesKindApi.CustomCharacteristic2,
        ],
      };

      let monthlyAccountingReportsLoadDefaultParametersSpy: jest.SpyInstance<
        Observable<MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi>
      >;

      beforeEach(() => {
        monthlyAccountingReportsLoadDefaultParametersSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsLoadDefaultParameters'
        ) as unknown as jest.SpyInstance<
          Observable<MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi>
        >;

        monthlyAccountingReportsLoadDefaultParametersSpy.mockReturnValue(
          of({ result: apiResult })
        );
      });

      it('should call monthlyAccountingReportsLoadDefaultParameters and return MonthlyTreatmentEditionsFiltersModel', waitForAsync(() => {
        const expectedResult: MonthlyTreatmentEditionsFiltersModel = {
          salesDetailOrder: SalesDetailsOrderByEnum.BUSINESS_TYPE,
          productCategories: [
            ProductCharacteristicEnum.CUSTOM_0,
            ProductCharacteristicEnum.CUSTOM_1,
          ],
        };

        service.getDefaultFilters().subscribe((result) => {
          expect(
            monthlyAccountingReportsLoadDefaultParametersSpy
          ).toHaveBeenCalledWith();
          expect(result).toStrictEqual(expectedResult);
        });
      }));
    });
  });

  describe('checkEmptyData', () => {
    describe('given any state where monthlyAccountingReportsEmptyData return a BooleanBusinessResultAPIApi', () => {
      const apiResult = true;
      let monthlyAccountingReportsEmptyDataSpy: jest.SpyInstance<
        Observable<BooleanBusinessResultAPIApi>
      >;

      beforeEach(() => {
        monthlyAccountingReportsEmptyDataSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsEmptyData'
        ) as unknown as jest.SpyInstance<
          Observable<BooleanBusinessResultAPIApi>
        >;

        monthlyAccountingReportsEmptyDataSpy.mockReturnValue(
          of({ result: apiResult })
        );
      });

      it('should call monthlyAccountingReportsEmptyData and return BooleanBusinessResultAPIApi', waitForAsync(() => {
        const expectedResult = true;
        const companyId = 'companyId';
        const dateFrom = new Date('2024-06-01');
        const dateTo = new Date('2024-06-30');
        service
          .checkEmptyData(companyId, dateFrom, dateTo)
          .subscribe((result) => {
            expect(monthlyAccountingReportsEmptyDataSpy).toHaveBeenCalledWith(
              companyId,
              '2024-06-01',
              '2024-06-30'
            );
            expect(result).toStrictEqual(expectedResult);
          });
      }));
    });
  });

  describe('encloseMonth', () => {
    describe('given any state where monthlyAccountingReportsCloseMonth return no error', () => {
      let monthlyAccountingReportsCloseMonthSpy: jest.SpyInstance<
        Observable<void>
      >;

      beforeEach(() => {
        monthlyAccountingReportsCloseMonthSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsCloseMonth'
        ) as unknown as jest.SpyInstance<Observable<void>>;

        monthlyAccountingReportsCloseMonthSpy.mockReturnValue(of(void 0));
      });

      it('should call monthlyAccountingReportsCloseMonth and return void', waitForAsync(() => {
        const expectedResult = void 0;
        const companyId = 'companyId';
        const month = new Date('2024-06-01');

        service.encloseMonth({ companyId, month }).subscribe((result) => {
          expect(monthlyAccountingReportsCloseMonthSpy).toHaveBeenCalledWith({
            companyId,
            month: '2024-06',
          });
          expect(result).toStrictEqual(expectedResult);
        });
      }));
    });
  });
  describe('saveDefaultEncloseMonth', () => {
    describe('given any state where monthlyAccountingReportsCloseUntilMonth return no error', () => {
      let monthlyAccountingReportsCloseUntilMonthSpy: jest.SpyInstance<
        Observable<void>
      >;

      beforeEach(() => {
        monthlyAccountingReportsCloseUntilMonthSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsCloseUntilMonth'
        ) as unknown as jest.SpyInstance<Observable<void>>;

        monthlyAccountingReportsCloseUntilMonthSpy.mockReturnValue(of(void 0));
      });

      it('should call monthlyAccountingReportsCloseUntilMonth and return void', waitForAsync(() => {
        const expectedResult = void 0;
        const companyId = 'companyId';
        const month = new Date('2024-06-01');

        service
          .saveDefaultEncloseMonth({ companyId, month })
          .subscribe((result) => {
            expect(
              monthlyAccountingReportsCloseUntilMonthSpy
            ).toHaveBeenCalledWith({
              companyId,
              month: '2024-06',
            });
            expect(result).toStrictEqual(expectedResult);
          });
      }));
    });
  });

  describe('uncloseMonth', () => {
    describe('given any state where', () => {
      let monthlyAccountingReportsUndoCloseMonthSpy: jest.SpyInstance<
        Observable<void>
      >;

      beforeEach(() => {
        monthlyAccountingReportsUndoCloseMonthSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsUndoCloseMonth'
        ) as unknown as jest.SpyInstance<Observable<void>>;

        monthlyAccountingReportsUndoCloseMonthSpy.mockReturnValue(of(void 0));
      });

      it('should call monthlyAccountingReportsUndoCloseMonth and success', waitForAsync(() => {
        const expectedResult = void 0;
        const companyId = 'companyId';
        const month = new Date('2024-06-01');

        service.uncloseMonth({ month, companyId }).subscribe((result) => {
          expect(
            monthlyAccountingReportsUndoCloseMonthSpy
          ).toHaveBeenCalledWith(companyId, '2024-06');
          expect(result).toStrictEqual(expectedResult);
        });
      }));
    });
  });

  describe('getExistingReportsIds', () => {
    describe('given any state where monthlyAccountingReportsGetMonthlyTreatmentDocuments return value', () => {
      let monthlyAccountingReportsGetMonthlyTreatmentDocumentsSpy: jest.SpyInstance<
        Observable<MonthlyAccountingReportsDocumentsBusinessResultAPIApi>
      >;
      let reportIds: MonthlyAccountingReportsDocumentsApi;

      beforeEach(() => {
        monthlyAccountingReportsGetMonthlyTreatmentDocumentsSpy = jest.spyOn(
          legacyApiService,
          'monthlyAccountingReportsGetMonthlyTreatmentDocuments'
        ) as unknown as jest.SpyInstance<
          Observable<MonthlyAccountingReportsDocumentsBusinessResultAPIApi>
        >;

        reportIds = {
          saleDetailsId: '',
          categorySalesId: '',
          unpaidListingId: '',
          payementListingId: '',
        };

        const result = {
          result: reportIds,
        } as MonthlyAccountingReportsDocumentsBusinessResultAPIApi;

        monthlyAccountingReportsGetMonthlyTreatmentDocumentsSpy.mockReturnValue(
          of(result)
        );
      });

      it('should call getExistingReportsIds and return existing report ids', waitForAsync(() => {
        const companyId = 'companyId';
        const month = new Date('2024-06-01');

        service.getExistingReportsIds(companyId, month).subscribe((result) => {
          expect(
            monthlyAccountingReportsGetMonthlyTreatmentDocumentsSpy
          ).toHaveBeenCalledWith(companyId, '2024-06');
          expect(result).toStrictEqual(reportIds);
        });
      }));
    });
  });
});
