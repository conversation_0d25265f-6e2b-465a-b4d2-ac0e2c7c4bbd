import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import {
  MonthlyTreatmentService,
  selectMonthlyTreatmentCompanyId,
} from '@gc/accounting/data-access';
import { LoadingStatus } from '@gc/shared/models';
import { CapitalizePipe, LoaderComponent } from '@gc/shared/ui';
import { getPreviousMonth } from '@gc/shared/utils';
import { TranslocoModule } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { LetDirective } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { filter, Observable, switchMap } from 'rxjs';

@Component({
  selector: 'gc-default-enclosure-month-selection-dialog',
  standalone: true,
  imports: [
    TranslocoModule,
    MatButtonModule,
    MatDialogModule,
    LoaderComponent,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatInputModule,
    LetDirective,
    TranslocoDatePipe,
    CapitalizePipe,
  ],
  templateUrl: './default-enclosure-month-selection-dialog.component.html',
  styleUrls: ['./default-enclosure-month-selection-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DefaultEnclosureMonthSelectionDialogComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _dialogRef =
    inject<MatDialogRef<DefaultEnclosureMonthSelectionDialogComponent>>(
      MatDialogRef
    );
  private readonly _destroyRef = inject(DestroyRef);

  availableStartMonths!: Date[];
  selectedDefaultMonthsFC!: FormControl<Date>;
  loadingStatusStartMonths$!: Observable<LoadingStatus>;

  ngOnInit(): void {
    this._init();
    this.loadingStatusStartMonths$ =
      this._monthlyTreatmentService.loadingStatusStartMonths$;
  }

  onSelect(): void {
    if (this.selectedDefaultMonthsFC.value) {
      const previousMonthDate = getPreviousMonth(
        this.selectedDefaultMonthsFC.value
      );
      this._dialogRef.close(previousMonthDate);
    }
  }
  private _init(): void {
    this._store
      .select(selectMonthlyTreatmentCompanyId)
      .pipe(
        filter(Boolean),
        switchMap((companyId: string) =>
          this._monthlyTreatmentService.getAvailableStartMonths(companyId)
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((availableStartMonths) => {
        this.availableStartMonths = availableStartMonths;
        this.selectedDefaultMonthsFC = new FormControl(
          availableStartMonths[0],
          { nonNullable: true }
        );
        this._cdr.detectChanges();
      });
  }
}
