import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { UserModulesService } from './user-modules.service';
import { HttpClient } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { CodeModuleCommercial, UserModule } from '../../models';
import { SnackbarService } from '@gc/shared/ui';
import { UrlBuilder } from '@gc/core/shared/infrastructure';

describe('UserModulesService', () => {
  let spectator: SpectatorService<UserModulesService>;
  let service: UserModulesService;
  let httpClient: HttpClient;

  const mockUrlBuilder = {
    withRouteParam: jest.fn().mockReturnThis(),
    build: jest.fn().mockReturnValue('/legacy/v1/common/common-protection'),
  };

  const createService = createServiceFactory({
    service: UserModulesService,
    mocks: [HttpClient, SnackbarService],
  });

  beforeEach(() => {
    // Mock UrlBuilder.create to return our mock builder
    jest.spyOn(UrlBuilder, 'create').mockReturnValue(mockUrlBuilder as any);

    spectator = createService();
    service = spectator.service;
    httpClient = spectator.inject(HttpClient);

    // Reset mocks
    jest.clearAllMocks();
    mockUrlBuilder.withRouteParam.mockReturnThis();
    mockUrlBuilder.build.mockReturnValue('/legacy/v1/common/common-protection');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getUserModules', () => {
    it('should return user modules from the API', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
        {
          code: CodeModuleCommercial.DematerializedWineAdministration,
          label: 'Accounting Module',
          isEnabled: false,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.getUserModules().subscribe((modules) => {
        expect(modules).toEqual(mockModules);
      });

      expect(UrlBuilder.create).toHaveBeenCalledWith('/legacy/v1');
      expect(mockUrlBuilder.withRouteParam).toHaveBeenCalledWith('common');
      expect(mockUrlBuilder.withRouteParam).toHaveBeenCalledWith(
        'common-protection'
      );
      expect(mockUrlBuilder.build).toHaveBeenCalled();
      expect(httpClient.get).toHaveBeenCalledWith(
        '/legacy/v1/common/common-protection'
      );
    });

    it('should handle API errors gracefully', () => {
      jest.spyOn(httpClient, 'get').mockReturnValue(of([]));

      service.getUserModules().subscribe((modules) => {
        expect(modules).toEqual([]);
      });
    });

    it('should call snackbar.failure when API returns an error', () => {
      const errorResponse = new Error('API Error');
      const snackbarService = spectator.inject(SnackbarService);

      jest
        .spyOn(httpClient, 'get')
        .mockReturnValue(throwError(() => errorResponse));

      service.getUserModules().subscribe({
        next: (modules) => {
          expect(modules).toEqual([]);
        },
        error: () => {
          fail('Error should be caught and not propagated');
        },
      });

      expect(snackbarService.failure).toHaveBeenCalledWith({
        key: errorResponse.message,
      });
    });

    it('should cache the API response and not make multiple HTTP requests', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
        {
          code: CodeModuleCommercial.DematerializedWineAdministration,
          label: 'Accounting Module',
          isEnabled: false,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.getUserModules().subscribe();
      service.getUserModules().subscribe();

      expect(httpClient.get).toHaveBeenCalledTimes(1);
      expect(UrlBuilder.create).toHaveBeenCalledTimes(1);
    });
  });

  describe('canAccessModule', () => {
    it('should return true when user has access to the module', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
        {
          code: CodeModuleCommercial.DematerializedWineAdministration,
          label: 'Accounting Module',
          isEnabled: false,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.canAccessModule(CodeModuleCommercial.Deb).subscribe((result) => {
        expect(result).toBe(true);
      });
    });

    it('should return false when user does not have access to the module', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
        {
          code: CodeModuleCommercial.DematerializedWineAdministration,
          label: 'Accounting Module',
          isEnabled: false,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service
        .canAccessModule(CodeModuleCommercial.DematerializedWineAdministration)
        .subscribe((result) => {
          expect(result).toBe(false);
        });
    });

    it('should make an API call if called directly without getUserModules', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
        {
          code: CodeModuleCommercial.DematerializedWineAdministration,
          label: 'Accounting Module',
          isEnabled: false,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.canAccessModule(CodeModuleCommercial.Deb).subscribe((result) => {
        expect(result).toBe(true);
      });

      expect(httpClient.get).toHaveBeenCalledTimes(1);
    });

    it('should call snackbar.failure when API returns an error during canAccessModule', () => {
      const errorResponse = new Error('API Error');
      const snackbarService = spectator.inject(SnackbarService);

      jest
        .spyOn(httpClient, 'get')
        .mockReturnValue(throwError(() => errorResponse));

      service.canAccessModule(CodeModuleCommercial.Deb).subscribe((result) => {
        expect(result).toBe(false);
      });

      expect(snackbarService.failure).toHaveBeenCalledWith({
        key: errorResponse.message,
      });
    });

    it('should use cached data for canAccessModule when getUserModules was called first', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.getUserModules().subscribe();

      service.canAccessModule(CodeModuleCommercial.Deb).subscribe((result) => {
        expect(result).toBe(true);
      });

      expect(httpClient.get).toHaveBeenCalledTimes(1);
      expect(UrlBuilder.create).toHaveBeenCalledTimes(1);
    });

    it('should return false for modules that are not purchased', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: false,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service.canAccessModule(CodeModuleCommercial.Deb).subscribe((result) => {
        expect(result).toBe(true);
      });
    });

    it('should return false for modules that do not exist in the response', () => {
      const mockModules: UserModule[] = [
        {
          code: CodeModuleCommercial.Deb,
          label: 'DEB Module',
          isEnabled: true,
          isPurchased: true,
        },
      ];
      jest.spyOn(httpClient, 'get').mockReturnValue(of(mockModules));

      service
        .canAccessModule(CodeModuleCommercial.SalesQuote)
        .subscribe((result) => {
          expect(result).toBe(false);
        });
    });
  });
});
