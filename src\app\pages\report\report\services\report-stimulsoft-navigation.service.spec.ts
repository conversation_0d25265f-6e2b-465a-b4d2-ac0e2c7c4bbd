import {
  SpectatorService,
  SpyObject,
  createServiceFactory,
} from '@ngneat/spectator/jest';
import { ReportStimulsoftNavigationService } from './report-stimulsoft-navigation.service';
import { ActivatedRoute } from '@angular/router';
import { ActivatedRouteStub } from '@gc/shared/tests';
import { companiesIdFixture } from '@gc/company/models';
import { waitForAsync } from '@angular/core/testing';
import { QueryParamsKeys } from '@gc/shared/models';

describe('ReportStimulsoftNavigationService', () => {
  let spectator: SpectatorService<ReportStimulsoftNavigationService>;
  let activatedRouteStub: ActivatedRouteStub;

  const mockComaniesIds = companiesIdFixture();

  const createService = createServiceFactory({
    service: ReportStimulsoftNavigationService,
    providers: [{ provide: ActivatedRoute, useClass: ActivatedRouteStub }],
  });

  beforeEach(() => {
    spectator = createService();
    activatedRouteStub = spectator.inject(
      ActivatedRoute
    ) as unknown as SpyObject<ActivatedRouteStub>;
  });

  describe('getReportStimulsoftPropertiesFromQueryParams method', () => {
    describe(`given a state where ActivatedRoute has ${QueryParamsKeys.COMPANIES_IDS} in its query params`, () => {
      describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} is set with value a list of 1 company id`, () => {
        const [singleId] = mockComaniesIds;

        beforeEach(() => {
          activatedRouteStub.addQueryParam(
            QueryParamsKeys.COMPANIES_IDS,
            singleId
          );
        });

        it('should returns the params', waitForAsync(() => {
          const expectedParams = {
            companiesIds: singleId,
          };

          spectator.service
            .getReportStimulsoftPropertiesFromQueryParams$()
            .subscribe((value) => {
              expect(value).toEqual(expectedParams);
            });
        }));
      });

      describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} have for value a list of more than 1 company id`, () => {
        beforeEach(() => {
          activatedRouteStub.addQueryParam(
            QueryParamsKeys.COMPANIES_IDS,
            mockComaniesIds
          );
        });

        it('should returns the params', waitForAsync(() => {
          const expectedParams = {
            companiesIds: mockComaniesIds,
          };

          spectator.service
            .getReportStimulsoftPropertiesFromQueryParams$()
            .subscribe((value) => {
              expect(value).toEqual(expectedParams);
            });
        }));
      });

      describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} have for value an empty array`, () => {
        beforeEach(() => {
          activatedRouteStub.addQueryParam(QueryParamsKeys.COMPANIES_IDS, []);
        });

        it(`should returns the params`, waitForAsync(() => {
          const expectedParams = {
            companiesIds: [],
          };

          spectator.service
            .getReportStimulsoftPropertiesFromQueryParams$()
            .subscribe((value) => {
              expect(value).toEqual(expectedParams);
            });
        }));
      });

      describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} is set with value undefined`, () => {
        beforeEach(() => {
          activatedRouteStub.addQueryParam(
            QueryParamsKeys.COMPANIES_IDS,
            undefined
          );
        });

        it(`should returns an object with the property ${QueryParamsKeys.COMPANIES_IDS} set to undefined`, waitForAsync(() => {
          const expectedParams = {
            companies: undefined,
          };

          spectator.service
            .getReportStimulsoftPropertiesFromQueryParams$()
            .subscribe((value) => {
              expect(value).toEqual(expectedParams);
            });
        }));
      });
    });

    describe(`given a state where ActivatedRoute has ${QueryParamsKeys.DEFAULT_COMPANY_ID} in its query params`, () => {
      const [singleId] = mockComaniesIds;

      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.DEFAULT_COMPANY_ID,
          singleId
        );
      });

      it('should returns the params', waitForAsync(() => {
        const expectedParams = {
          currentCompanyId: singleId,
        };

        spectator.service
          .getReportStimulsoftPropertiesFromQueryParams$()
          .subscribe((value) => {
            expect(value).toEqual(expectedParams);
          });
      }));
    });

    describe(`given a state where ActivatedRoute has ${QueryParamsKeys.CLIENT_CODE} in its query params`, () => {
      const clientCode = 'Any_Client_Code';

      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.CLIENT_CODE,
          clientCode
        );
      });

      it('should returns the params', waitForAsync(() => {
        const expectedParams = {
          clientCode: clientCode,
        };

        spectator.service
          .getReportStimulsoftPropertiesFromQueryParams$()
          .subscribe((value) => {
            expect(value).toEqual(expectedParams);
          });
      }));
    });
  });
});
