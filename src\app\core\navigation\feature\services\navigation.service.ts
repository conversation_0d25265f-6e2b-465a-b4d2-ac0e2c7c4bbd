import { LocationStrategy } from '@angular/common';
import { Injectable, inject } from '@angular/core';
import { Params, Router } from '@angular/router';
import { UrlPaths } from '@gc/core/navigation/models';

@Injectable({
  providedIn: 'root',
})
export class NavigationService {
  private readonly _router = inject(Router);
  private readonly _location = inject(LocationStrategy);

  openInNewTab(urlPath: UrlPaths, queryParams: Params): void {
    const href: string = this._location.getBaseHref();

    const url = this._router.serializeUrl(
      this._router.createUrlTree([href + urlPath], {
        queryParams: queryParams,
      })
    );

    window.open(url, '_blank');
  }
}
