import {
  SalesByProductFilters,
  SalesDetailsFilters,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { StimulsoftLauncherService } from './stimulsoft-launcher.service';
import { MonthlyTreatmentService } from '@gc/accounting/data-access';
import { NavigationService } from '@gc/core/navigation/feature';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { Observable, of, take, tap, throwError } from 'rxjs';
import { waitForAsync } from '@angular/core/testing';
import { LoadingStatus, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { REPORT_ID, ReportFamilies } from '@gc/shared/stimulsoft/models';
import { Params } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';

describe('StimulsoftLauncherService', () => {
  let spectator: SpectatorService<StimulsoftLauncherService>;
  let service: StimulsoftLauncherService;

  let monthlyTreatmentService: MonthlyTreatmentService;

  let _navigateToStimulsoftSpy: jest.SpyInstance<void>;

  const createService = createServiceFactory({
    service: StimulsoftLauncherService,
    mocks: [NavigationService, MonthlyTreatmentService],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);

    monthlyTreatmentService = spectator.inject(MonthlyTreatmentService);

    _navigateToStimulsoftSpy = jest.spyOn(
      service as any,
      '_navigateToStimulsoft'
    );
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft', () => {
    const treatmentId = 'a treatment id';
    let getSalesByProductTreatmentIdSpy: jest.SpyInstance<Observable<string>>;

    beforeEach(() => {
      getSalesByProductTreatmentIdSpy = jest.spyOn(
        monthlyTreatmentService,
        'getSalesByProductTreatmentId'
      );
    });

    describe('given a state where getSalesByProductTreatmentId of MonthlyTreatmentService return a treatmentId', () => {
      beforeEach(() => {
        getSalesByProductTreatmentIdSpy.mockReturnValueOnce(of(treatmentId));
      });

      describe('when method is called with a SalesByProductFilters', () => {
        const filters: SalesByProductFilters = {
          companyId: 'fake company id',
          categories: [0, 1, 15],
          dateFrom: new Date('2024-01-01'),
          dateTo: new Date('2024-23-01'),
        };

        it('should call getSalesByProductTreatmentId with SalesByProductFilters and _navigateToStimulsoft method with stimulsoft query params', waitForAsync(() => {
          service
            .retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
              filters
            )
            .subscribe(() => {
              expect(getSalesByProductTreatmentIdSpy).toHaveBeenCalledWith(
                filters
              );
              expect(_navigateToStimulsoftSpy).toHaveBeenCalledWith({
                [StimulsoftQueryParamsKeys.TREATMENT_ID]: treatmentId,
                [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.sales,
                [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.SALES,
              });
            });
        }));

        it('should salesByProductTreatmentIdLoadingStatus$ Observable emit NOT_LOADED, IN_PROGRESS, then LOADED', async () => {
          await new Promise<void>((done) => {
            const emittedStatus: LoadingStatus[] = [];

            service.salesByProductTreatmentIdLoadingStatus$
              .pipe(
                take(3),
                tap((status) => emittedStatus.push(status))
              )
              .subscribe({
                complete: () => {
                  expect(emittedStatus).toEqual([
                    'NOT_LOADED',
                    'IN_PROGRESS',
                    'LOADED',
                  ]);
                  done();
                },
              });

            service
              .retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
                filters
              )
              .subscribe();
          }).then();
        });
      });
    });

    describe('given a state where getSalesByProductTreatmentId of MonthlyTreatmentService return an error flow', () => {
      beforeEach(() => {
        getSalesByProductTreatmentIdSpy.mockImplementation(() => {
          return throwError(
            () => new Error('error in getSalesByProductTreatmentId')
          );
        });
      });

      describe('when method is called with a SalesByProductFilters', () => {
        const filters: SalesByProductFilters = {
          companyId: 'fake company id',
          categories: [0, 1, 15],
          dateFrom: new Date('2024-01-01'),
          dateTo: new Date('2024-23-01'),
        };

        it('should not call _navigateToStimulsoft method', waitForAsync(() => {
          service
            .retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
              filters
            )
            .subscribe(() => {
              expect(_navigateToStimulsoftSpy).not.toHaveBeenCalled();
            });
        }));

        it('should salesByProductTreatmentIdLoadingStatus$ Observable emit NOT_LOADED, IN_PROGRESS, then ERROR', async () => {
          await new Promise<void>((done) => {
            const emittedStatus: LoadingStatus[] = [];

            service.salesByProductTreatmentIdLoadingStatus$
              .pipe(
                take(3),
                tap((status) => emittedStatus.push(status))
              )
              .subscribe({
                complete: () => {
                  expect(emittedStatus).toEqual([
                    'NOT_LOADED',
                    'IN_PROGRESS',
                    'ERROR',
                  ]);
                  done();
                },
              });

            service
              .retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
                filters
              )
              .subscribe();
          }).then();
        });
      });
    });
  });

  describe('retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft', () => {
    const treatmentId = 'a treatment id';
    let getSalesDetailsTreatmentIdSpy: jest.SpyInstance<Observable<string>>;

    beforeEach(() => {
      getSalesDetailsTreatmentIdSpy = jest.spyOn(
        monthlyTreatmentService,
        'getSalesDetailsTreatmentId'
      );
    });

    describe('given a state where getSalesDetailsTreatmentId of MonthlyTreatmentService return a treatmentId', () => {
      beforeEach(() => {
        getSalesDetailsTreatmentIdSpy.mockReturnValueOnce(of(treatmentId));
      });

      describe('when method is called with a SalesDetailsFilters', () => {
        const filters: SalesDetailsFilters = {
          companyId: 'fake company id',
          dateFrom: new Date('2024-01-01'),
          dateTo: new Date('2024-23-01'),
          orderBy: SalesDetailsOrderByEnum.BUSINESS_TYPE,
        };

        it('should call getSalesDetailsTreatmentId with SalesDetailsFilters and _navigateToStimulsoft method with stimulsoft query params', waitForAsync(() => {
          service
            .retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(filters)
            .subscribe(() => {
              expect(getSalesDetailsTreatmentIdSpy).toHaveBeenCalledWith(
                filters
              );
              expect(_navigateToStimulsoftSpy).toHaveBeenCalledWith({
                [StimulsoftQueryParamsKeys.TREATMENT_ID]: treatmentId,
                [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.details,
                [StimulsoftQueryParamsKeys.REPORT_FAMILY]:
                  ReportFamilies.DETAILS,
              });
            });
        }));

        it('should salesDetailsTreatmentIdLoadingStatus$ Observable emit NOT_LOADED, IN_PROGRESS, then LOADED', async () => {
          await new Promise<void>((done) => {
            const emittedStatus: LoadingStatus[] = [];

            service.salesDetailsTreatmentIdLoadingStatus$
              .pipe(
                take(3),
                tap((status) => emittedStatus.push(status))
              )
              .subscribe({
                complete: () => {
                  expect(emittedStatus).toEqual([
                    'NOT_LOADED',
                    'IN_PROGRESS',
                    'LOADED',
                  ]);
                  done();
                },
              });

            service
              .retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(filters)
              .subscribe();
          }).then();
        });
      });
    });

    describe('given a state where getSalesDetailsTreatmentId of MonthlyTreatmentService return an error flow', () => {
      beforeEach(() => {
        getSalesDetailsTreatmentIdSpy.mockImplementation(() => {
          return throwError(
            () => new Error('error in getSalesDetailsTreatmentId')
          );
        });
      });

      describe('when method is called with a SalesDetailsFilters', () => {
        const filters: SalesDetailsFilters = {
          companyId: 'fake company id',
          dateFrom: new Date('2024-01-01'),
          dateTo: new Date('2024-23-01'),
          orderBy: SalesDetailsOrderByEnum.BUSINESS_TYPE,
        };

        it('should not call navigateToStimulsoft method', waitForAsync(() => {
          service
            .retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(filters)
            .subscribe(() => {
              expect(_navigateToStimulsoftSpy).not.toHaveBeenCalled();
            });
        }));

        it('should salesDetailsTreatmentIdLoadingStatus$ Observable emit NOT_LOADED, IN_PROGRESS, then ERROR', async () => {
          await new Promise<void>((done) => {
            const emittedStatus: LoadingStatus[] = [];

            service.salesDetailsTreatmentIdLoadingStatus$
              .pipe(
                take(3),
                tap((status) => emittedStatus.push(status))
              )
              .subscribe({
                complete: () => {
                  expect(emittedStatus).toEqual([
                    'NOT_LOADED',
                    'IN_PROGRESS',
                    'ERROR',
                  ]);
                  done();
                },
              });

            service
              .retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(filters)
              .subscribe();
          }).then();
        });
      });
    });
  });

  describe('prepareParamsAndNavigateToStimulsoft', () => {
    describe('given any state', () => {
      describe('when method is called with a reportId, a reportFamily companyId and an endDate', () => {
        const companyId = 'a company id';
        const endDate = new Date('2024-01-31');

        it('should call _navigateToStimulsoft method with stimulsoft query params', waitForAsync(() => {
          service.prepareParamsAndNavigateToStimulsoft(
            REPORT_ID.debt,
            ReportFamilies.DEBT,
            companyId,
            endDate
          );
          expect(_navigateToStimulsoftSpy).toHaveBeenCalledWith({
            [StimulsoftQueryParamsKeys.COMPANY_ID]: companyId,
            [StimulsoftQueryParamsKeys.END_DATE]: endDate.toISOString(),
            [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.debt,
            [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.DEBT,
          });
        }));
      });
    });
  });

  describe('_navigateToStimulsoft', () => {
    let openInNewTabSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      const navigationService = spectator.inject(NavigationService);
      openInNewTabSpy = jest.spyOn(navigationService, 'openInNewTab');
    });

    describe('given any state', () => {
      describe('when method is called with stimulsoft query params', () => {
        const stimulsoftQueryParams: Params = {
          [StimulsoftQueryParamsKeys.TREATMENT_ID]: 'treatmentId',
          [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.details,
          [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.DETAILS,
        };

        it('should call openInNewTab method of NavigationService', () => {
          (service as any)._navigateToStimulsoft(stimulsoftQueryParams);
          expect(openInNewTabSpy).toHaveBeenCalledWith(
            URL_PATHS.stimulsoftViewer,
            stimulsoftQueryParams
          );
        });
      });
    });
  });
});
