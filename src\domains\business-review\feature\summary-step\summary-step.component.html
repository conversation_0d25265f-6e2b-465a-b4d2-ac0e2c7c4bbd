<ng-container *transloco="let t">
  <ng-container
    *ngrxLet="{
      companiesCode: companiesCodes$$,
      dateRange: selectedDateRange$,
      vatRate: vatRate$,
    } as vm">
    <h3 class="text-lg">
      {{ t('businessReview.main-settings-tab.summary.title') }}
    </h3>

    <p>
      {{ t('businessReview.main-settings-tab.summary.description') }}
    </p>

    <mat-list>
      <mat-list-item>
        <mat-icon matListItemIcon>looks_one</mat-icon>
        <strong>
          {{ t('businessReview.main-settings-tab.summary.selected-companies') }}
        </strong>
        : {{ vm.companiesCode }}
      </mat-list-item>
      <mat-list-item>
        <mat-icon matListItemIcon>looks_two</mat-icon>
        <strong>
          {{ t('businessReview.main-settings-tab.summary.periodicity') }}
        </strong>
        :
        @if (periodicityKind === 'calendarYear') {
          {{
            t(
              'businessReview.main-settings-tab.periodicity.calendar-year.title'
            )
          }}
        } @else if (periodicityKind === 'fiscalYear') {
          {{
            t('businessReview.main-settings-tab.periodicity.fiscal-year.title')
          }}
        }

        @if (vm.dateRange.startDate && vm.dateRange.endDate) {
          {{
            t('businessReview.main-settings-tab.from-to', {
              startDate: vm.dateRange.startDate | translocoDate,
              endDate: vm.dateRange.endDate | translocoDate,
            })
          }}
        }
      </mat-list-item>
      @if (vm.vatRate) {
        <mat-list-item>
          <mat-icon matListItemIcon>info</mat-icon>
          <strong>{{
            t('businessReview.main-settings-tab.summary.vat.title')
          }}</strong>
          : {{ vm.vatRate | translocoDecimal }} %
          <br />
          <small>
            {{ t('businessReview.main-settings-tab.summary.vat.description') }}
          </small>
        </mat-list-item>
      }
    </mat-list>
  </ng-container>
</ng-container>
