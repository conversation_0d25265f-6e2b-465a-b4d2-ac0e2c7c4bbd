@use 'sass:map';
@use '@angular/material' as mat;
@use 'gc-material-theme' as gc-material-theme;
@use 'gc-variables';

$color-config: mat.m2-get-color-config(gc-material-theme.$theme);
$primary-palette: map.get($color-config, 'primary');

.container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: gc-variables.$page-max-width;
    margin: auto;

    .left-container,
    .right-container {
        flex: 1;
        display: flex;
        align-items: center;
    }

    .left-container {
        gap: 5px;
    }

    .right-container {
        justify-content: flex-end;
    }
}
