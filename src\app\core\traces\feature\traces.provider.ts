import {
  APP_INITIALIZER,
  EnvironmentProviders,
  makeEnvironmentProviders,
} from '@angular/core';
import {
  provideDiagnostic,
  withConsoleTraceListener,
  withRxJSUnhandledError,
} from '@isagri-ng/core/diagnostics';
import {
  IAppInsightActivatorService,
  withApplicationInsightTraceListener,
} from '@isagri-ng/monitoring';
import { AppInsightActivatorService } from './services-isagri-ng-interfaces-implementation/app-insight-activator.service';
import { UserNavigationTracerService } from './service/user-navigation-tracer.service';

export function initializeNavigationListener(
  userNavigationTracerService: UserNavigationTracerService
) {
  return (): void => {
    return userNavigationTracerService.init();
  };
}

export function provideTraces(): EnvironmentProviders {
  return makeEnvironmentProviders([
    provideDiagnostic(
      withRxJSUnhandledError(),
      withApplicationInsightTraceListener(),
      withConsoleTraceListener()
    ),
    {
      provide: IAppInsightActivatorService,
      useExisting: AppInsightActivatorService,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeNavigationListener,
      deps: [UserNavigationTracerService],
      multi: true,
    },
  ]);
}
