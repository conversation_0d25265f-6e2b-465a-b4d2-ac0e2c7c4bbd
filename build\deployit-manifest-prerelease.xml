<?xml version="1.0" encoding="UTF-8"?>
<udm.DeploymentPackage version="1.0" application="Isagri/GC/IsaGCLive_FullWeb/IsaGCLive_FullWeb_pre/$(AppName)">
    <deployables>
        <iis.WebContent name="/IsaGCLive_FullWeb_pre-$(AppName)_WebContent" file="WebContent.zip">
            <tags />
            <scanPlaceholders>false</scanPlaceholders>
            <preScannedPlaceholders>false</preScannedPlaceholders>
            <placeholders />
            <checksum></checksum>
            <targetPath>{{IS-GC-FullWeb_SitePath}}-pre/$(AppName)</targetPath>
        </iis.WebContent>
        <!-- L'application n'a pour le moment pas de domaine dédié alors elle cohabite sur le site IS-GC -->
        <iis.ApplicationSpec name="/IsaGCLive_FullWeb_pre-$(AppName)_ApplicationSpec">
            <tags />
            <directoryBrowse_enabled>false</directoryBrowse_enabled>
            <directoryBrowse_showFlags>Date, Time, Size, Extension</directoryBrowse_showFlags>
            <applicationPath>{{IS-GC-FullWeb_WebsiteName}}-pre/$(AppName)</applicationPath>
            <websiteName>{{IS-GC_WebsiteName}}</websiteName>
            <physicalPath>{{IS-GC-FullWeb_SitePath}}-pre\$(AppName)</physicalPath>
            <protocols>http</protocols>
            <applicationPoolName>{{IS-GC-FullWeb_WebsiteName}}</applicationPoolName>
            <authentication />
            <preloadEnabled>false</preloadEnabled>
        </iis.ApplicationSpec>
    </deployables>
</udm.DeploymentPackage>