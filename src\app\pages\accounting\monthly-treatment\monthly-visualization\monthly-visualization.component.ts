import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentStoreModule,
  selectEnclosingStatus,
  selectEnclosureMonth,
  selectMonthlyTreatmentCompanyId,
  selectMonthlyTreatmentDateRange,
  selectMonthlyTreatmentDateRangeAndCompanyId,
} from '@gc/accounting/data-access';
import {
  AccountEntryListTreatmentIdRetrieverCardComponent,
  DebtListTreatmentIdRetrieverCardComponent,
  EncloseMonthButtonComponent,
  ReceiptListTreatmentIdRetrieverCardComponent,
  SalesByProductCategoryTreatmentIdRetrieverCardComponent,
  SalesDetailsTreatmentIdRetrieverCardComponent,
} from '@gc/accounting/feature';
import {
  MonthlyTreatmentRange,
  RangeFormGroup,
  SalesByProductFilters,
  SalesDetailsFilters,
  SalesDetailsOrderByEnum,
} from '@gc/accounting/models';
import { MonthlyTreatmentRangeComponent } from '@gc/accounting/ui';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import {
  REPORT_ID,
  ReportFamilies,
  ReportId,
} from '@gc/shared/stimulsoft/models';
import {
  CapitalizePipe,
  LoaderComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { LetDirective, PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { EMPTY, filter, Observable, switchMap } from 'rxjs';
import { map, take, tap } from 'rxjs/operators';
import { StimulsoftLauncherService } from './services/stimulsoft-launcher.service';
import { delayInProgress } from '@gc/shared/utils';
import { LoadingStatus } from '@gc/shared/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { CompanyService } from '@gc/company/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-monthly-visualization',
  templateUrl: './monthly-visualization.component.html',
  standalone: true,
  styleUrls: [
    './monthly-visualization.component.scss',
    './monthly-visualization-theme.component.scss',
  ],
  imports: [
    CompanySingleSelectComponent,
    MonthlyTreatmentRangeComponent,
    ReactiveFormsModule,
    TranslocoModule,
    SalesByProductCategoryTreatmentIdRetrieverCardComponent,
    SalesDetailsTreatmentIdRetrieverCardComponent,
    ReceiptListTreatmentIdRetrieverCardComponent,
    DebtListTreatmentIdRetrieverCardComponent,
    AccountEntryListTreatmentIdRetrieverCardComponent,
    MatButtonModule,
    MatIconModule,
    MonthlyTreatmentStoreModule,
    CapitalizePipe,
    TranslocoDatePipe,
    LetDirective,
    PushPipe,
    LoaderComponent,
    EncloseMonthButtonComponent,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/form',
      multi: true,
    },
    StimulsoftLauncherService,
  ],
})
export class MonthlyVisualizationComponent implements OnInit {
  private readonly _companyService = inject(CompanyService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  readonly stimulsoftLauncherService = inject(StimulsoftLauncherService);

  selectCompanyFC?: FormControl<string>;
  rangeFormGroup!: FormGroup<RangeFormGroup>;

  companyId$ = this._store.select(selectMonthlyTreatmentCompanyId);
  enclosureMonth$ = this._store.select(selectEnclosureMonth);
  enclosingStatus$ = this._store.select(selectEnclosingStatus);

  isLoadingSalesByProductTreatmentId$!: Observable<boolean>;
  isLoadingSalesDetailsTreatmentId$!: Observable<boolean>;

  readonly reportId = REPORT_ID;
  readonly reportFamilies = ReportFamilies;

  ngOnInit(): void {
    this._store.dispatch(monthlyTreatmentActions.loadDefaultFilters());

    this.isLoadingSalesByProductTreatmentId$ =
      this.stimulsoftLauncherService.salesByProductTreatmentIdLoadingStatus$.pipe(
        delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING),
        map((loadingStatus: LoadingStatus) => loadingStatus === 'IN_PROGRESS')
      );

    this.isLoadingSalesDetailsTreatmentId$ =
      this.stimulsoftLauncherService.salesDetailsTreatmentIdLoadingStatus$.pipe(
        delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING),
        map((loadingStatus: LoadingStatus) => loadingStatus === 'IN_PROGRESS')
      );

    this.handleSelectedCompanyId();
    this.initRangeFormGroup();
    this.handleEnclosureMonthLoading();
  }

  handleSelectedCompanyId(): void {
    this._companyService.companyIdIfSingleCompany$
      .pipe(
        switchMap((companyId?: string) =>
          companyId ? EMPTY : this.initSelectCompanyFormControl()
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  initSelectCompanyFormControl(): Observable<void> {
    return this._store.select(selectMonthlyTreatmentCompanyId).pipe(
      tap((companyId?: string) => {
        this.selectCompanyFC = new FormControl<string>(companyId ?? '', {
          nonNullable: true,
          validators: [Validators.required],
        });
      }),
      switchMap(() => {
        return this.selectCompanyFC!.valueChanges.pipe(
          tap((companyId) =>
            this._store.dispatch(
              monthlyTreatmentActions.changeCompanyId({
                companyId,
              })
            )
          )
        );
      }),
      map(() => void 0)
    );
  }

  handleEnclosureMonthLoading(): void {
    this._store
      .select(selectMonthlyTreatmentCompanyId)
      .pipe(filter(Boolean), takeUntilDestroyed(this._destroyRef))
      .subscribe((companyId: string) => {
        this._store.dispatch(
          monthlyTreatmentActions.loadEnclosureMonth({ companyId })
        );
      });
  }

  initRangeFormGroup(): void {
    this._store
      .select(selectMonthlyTreatmentDateRange)
      .pipe(
        filter(
          (range: MonthlyTreatmentRange | undefined) =>
            !range ||
            range.start !== this.rangeFormGroup?.controls.start.value ||
            range.end !== this.rangeFormGroup?.controls.end.value
        ),
        tap((range: MonthlyTreatmentRange | undefined) => {
          this.rangeFormGroup = this._createRangeFormGroup(range);
        }),
        switchMap(() => this.dispatchChangeDateRangeActionWhenValueChange()),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  dispatchChangeDateRangeActionWhenValueChange(): Observable<void> {
    return this.rangeFormGroup.valueChanges.pipe(
      tap(({ start, end }) => {
        if (start && end) {
          this._store.dispatch(
            monthlyTreatmentActions.changeDateRange({
              startDate: start,
              endDate: end,
            })
          );
        }
      }),
      map(() => void 0)
    );
  }

  consultSalesByProductCategory(
    categories: (ProductCharacteristicEnum | null)[]
  ): void {
    this._store
      .select(selectMonthlyTreatmentDateRangeAndCompanyId)
      .pipe(
        map(
          ({ range, companyId }) =>
            ({
              categories,
              dateFrom: range!.start,
              dateTo: range!.end,
              companyId,
            }) as SalesByProductFilters
        ),
        take(1),
        switchMap((filters) =>
          this.stimulsoftLauncherService.retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
            filters
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  consultSalesDetails(orderBy: SalesDetailsOrderByEnum): void {
    this._store
      .select(selectMonthlyTreatmentDateRangeAndCompanyId)
      .pipe(
        map(
          ({ range, companyId }) =>
            ({
              orderBy,
              dateFrom: range!.start,
              dateTo: range!.end,
              companyId,
            }) as SalesDetailsFilters
        ),
        take(1),
        switchMap((filters) =>
          this.stimulsoftLauncherService.retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(
            filters
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  consult(reportId: ReportId, reportFamily: ReportFamilies): void {
    this._store
      .select(selectMonthlyTreatmentDateRangeAndCompanyId)
      .pipe(
        tap(({ range, companyId }) =>
          this.stimulsoftLauncherService.prepareParamsAndNavigateToStimulsoft(
            reportId,
            reportFamily,
            companyId!,
            range!.end
          )
        ),
        take(1)
      )
      .subscribe();
  }

  private _createRangeFormGroup(
    range: MonthlyTreatmentRange | undefined
  ): FormGroup<RangeFormGroup> {
    return new FormGroup<RangeFormGroup>({
      start: new FormControl<Date | null>(range?.start ?? null, {
        nonNullable: true,
        validators: [Validators.required],
      }),
      end: new FormControl<Date | null>(range?.end ?? null, {
        nonNullable: true,
        validators: [Validators.required],
      }),
    });
  }
}
