<?xml version="1.0" encoding="UTF-8"?>
<udm.DeploymentPackage version="25.20.1" application="IsaGCLive_FullWeb_V2520_fr-FR">
    <deployables>
        <iis.WebContent name="/IS-GC-FullWeb_25.20_WebContent" file="WebContent.zip">
            <tags />
            <scanPlaceholders>false</scanPlaceholders>
            <preScannedPlaceholders>false</preScannedPlaceholders>
            <placeholders />
            <checksum></checksum>
            <targetPath>{{IS-GC-FullWeb_SitePath}}</targetPath>
        </iis.WebContent>
        <iis.ApplicationPoolSpec name="/IS-GC-FullWeb_25.20_IISApplicationPoolSpec">
            <tags />
            <applicationPoolName>{{IS-GC-FullWeb_WebsiteName}}</applicationPoolName>
            <managedRuntimeVersion>v4.0</managedRuntimeVersion>
            <recyclingPeriodicRestartSchedule />
            <serviceAccount>ApplicationPoolIdentity</serviceAccount>
        </iis.ApplicationPoolSpec>
        <!-- L'application n'a pour le moment pas de domaine dédié alors elle cohabite sur le site IS-GC -->
        <iis.WebsiteSpec name="/IS-GC-FullWeb_25.20_SiteRacine_IISWebsiteSpec">
            <tags />
            <directoryBrowse_showFlags>Date, Time, Size, Extension</directoryBrowse_showFlags>
            <websiteName>{{IS-GC-FullWeb_WebsiteName}}</websiteName>
            <physicalPath>{{IS-GC-FullWeb_SitePath}}</physicalPath>
            <applicationPoolName>{{IS-GC-FullWeb_WebsiteName}}</applicationPoolName>
            <logFileDirectory>{{GC_FullWeb_LogFileDirectory}}\{{IS-GC-FullWeb_WebsiteName}}</logFileDirectory>
            <stopStartOnNoop>false</stopStartOnNoop>
            <bindings>
                <iis.WebsiteBindingSpec name="/IS-GC-FullWeb_25.20_SiteRacine_IISWebsiteSpec/IS_SiteRacine_IISWebsiteBindingSpec">
                <protocol>https</protocol>
                <ipAddress>{{IS-GC-FullWeb_LiaisonIP}}</ipAddress>
                <port>{{IS-GC-FullWeb_LiaisonPort}}</port>
                <hostHeader>{{IS-GC-FullWeb_ServerAddress}}</hostHeader>
                <certificateName>{{IS-GC-FullWeb_LiaisonCertificateThumprint}}</certificateName>
                <enableSni>true</enableSni>
                <certRequired>Ignore</certRequired>
                </iis.WebsiteBindingSpec>
            </bindings>
            <authentication />
        <isapiFilters />
        </iis.WebsiteSpec>
    </deployables>
</udm.DeploymentPackage>
