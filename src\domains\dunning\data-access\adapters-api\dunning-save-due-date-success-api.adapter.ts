import { DueDateSaveSuccessApi } from '@gc/shared/api/data-access';
import { DunningSaveDueDateSuccess } from '@gc/dunning/models';
import { DunningLevelAdapter } from '../adapters/dunning-level.adapter';

export class DunningSaveDueDateSuccessApiAdapter {
  static fromApi(
    dueDateSaveSuccessApi: DueDateSaveSuccessApi[]
  ): DunningSaveDueDateSuccess[] {
    const result: DunningSaveDueDateSuccess[] = [];
    dueDateSaveSuccessApi.forEach((s) =>
      result.push({
        dueDateId: s.dueDateId,
        dunningLevelId: DunningLevelAdapter.fromApi(s.dunningLevelId),
      })
    );
    return result;
  }
}
