import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { LoadingStatus, ProcessStatus } from '@gc/shared/models';

export interface MonthlyTreatmentHistoryState {
  enclosedYears?: number[];
  enclosedYearsLoadingStatus: LoadingStatus;
  selectedEnclosedYear?: number | null;
  enclosedMonthsOfSelectedYear?: Date[];
  enclosedMonthsLoadingStatus: LoadingStatus;
  selectedEnclosedMonth?: Date | null;
  availableMonthlyEditions?: MonthlyTreatmentAvailableEdition[] | null;
  availableMonthlyEditionsLoadingStatus: LoadingStatus;
  selectedMonthlyEditions: MonthlyTreatmentEditionsEnum[];
  uncloseSelectedMonthStatus: ProcessStatus;
}
