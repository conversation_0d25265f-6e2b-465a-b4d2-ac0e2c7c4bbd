import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { ProductCharacteristicFiltersFormGroup } from '../models/product-characteristic-filters-form-group.model';

@Injectable({
  providedIn: 'root',
})
export class FormGroupManagerService {
  private _fb = inject(FormBuilder);

  init(
    defaultCharacteristicsEnum?: (ProductCharacteristicEnum | null)[]
  ): ProductCharacteristicFiltersFormGroup {
    const amountOfProductCharacteristicFilters = 3;

    const fg = this._fb.group({
      characteristics: this._fb.array(
        Array.from({ length: amountOfProductCharacteristicFilters }, (_, i) =>
          this._fb.control<ProductCharacteristicEnum | null>(
            defaultCharacteristicsEnum?.[i] ?? null,
            {
              validators: [Validators.required],
            }
          )
        )
      ),
    });
    this.switchRequiredValidationState(fg);
    return fg;
  }

  switchRequiredValidationState(
    fg: ProductCharacteristicFiltersFormGroup
  ): void {
    const atLeastOneControlIsValued = fg.value.characteristics?.some(
      (characteristic: ProductCharacteristicEnum | null) =>
        characteristic !== null
    );
    fg.controls.characteristics.controls.forEach(
      (
        control: FormControl<ProductCharacteristicEnum | null>,
        index: number
      ) => {
        atLeastOneControlIsValued
          ? control.removeValidators(Validators.required)
          : control.addValidators(Validators.required);

        const { controls } = fg.controls.characteristics;
        const oneNextControlValueIsNotNull = controls
          .filter((_, i) => i > index)
          .some(
            (fc: FormControl<ProductCharacteristicEnum | null>) => !!fc.value
          );
        if (oneNextControlValueIsNotNull) {
          control.addValidators(Validators.required);
        }
        control.updateValueAndValidity({ emitEvent: false });
      }
    );
  }
}
