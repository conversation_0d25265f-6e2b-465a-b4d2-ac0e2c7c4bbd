import {
  SpectatorService,
  SpyObject,
  createServiceFactory,
} from '@ngneat/spectator/jest';
import { ActivatedRoute } from '@angular/router';
import { ActivatedRouteStub } from '@gc/shared/tests';
import { companiesIdFixture } from '@gc/company/models';
import { waitForAsync } from '@angular/core/testing';
import { QueryParamsKeys, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { BusinessReviewStimulsoftNavigationService } from './business-review-stimulsoft-navigation.service';

describe('BusinessReviewStimulsoftNavigationService', () => {
  let spectator: SpectatorService<BusinessReviewStimulsoftNavigationService>;
  let activatedRouteStub: ActivatedRouteStub;

  const mockComaniesIds = companiesIdFixture();

  const createService = createServiceFactory({
    service: BusinessReviewStimulsoftNavigationService,
    providers: [{ provide: ActivatedRoute, useClass: ActivatedRouteStub }],
  });

  beforeEach(() => {
    spectator = createService();
    activatedRouteStub = spectator.inject(
      ActivatedRoute
    ) as unknown as SpyObject<ActivatedRouteStub>;
  });

  describe('getBusinessReviewStimulsoftPropertiesFromQueryParams$ method', () => {
    describe(`Given a state where ActivatedRoute has not params ${QueryParamsKeys.DATES} set`, () => {
      it('should throw an error', () => {
        expect.assertions(1);
        spectator.service
          .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
          .subscribe({
            next: () => {
              throw new Error('Expected an error, but got a value');
            },
            error: (err: unknown) => {
              expect(err).toEqual(
                new Error(
                  'Missing dates in query parameters: at least one date range is required.'
                )
              );
            },
          });
      });
    });

    describe(`Given a state where ActivatedRoute has ${QueryParamsKeys.DATES} set with value an empty array`, () => {
      const dates = [] as string[];

      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.DATES,
          JSON.stringify(dates)
        );
      });

      it('should throw an error', () => {
        expect.assertions(1);
        spectator.service
          .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
          .subscribe({
            next: () => {
              throw new Error('Expected an error, but got a value');
            },
            error: (err: unknown) => {
              expect(err).toEqual(
                new Error(
                  'Missing dates in query parameters: at least one date range is required.'
                )
              );
            },
          });
      });
    });

    describe(`Given a state where ActivatedRoute has ${QueryParamsKeys.DATES} is set with undefined`, () => {
      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.DATES,
          JSON.stringify(undefined)
        );
      });

      it('should throw an error', () => {
        expect.assertions(1);
        spectator.service
          .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
          .subscribe({
            next: () => {
              throw new Error('Expected an error, but got a value');
            },
            error: (err: unknown) => {
              expect(err).toEqual(
                new Error(
                  'Missing dates in query parameters: at least one date range is required.'
                )
              );
            },
          });
      });
    });

    describe(`Given a state where ActivatedRoute has ${QueryParamsKeys.DATES} set with a value matching the the minimum ranges required`, () => {
      const dates = ['2024-01-01', '2024-12-31'];

      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.DATES,
          JSON.stringify(dates)
        );
      });

      it('should returns the params', waitForAsync(() => {
        const expectedParams = {
          dateRange1startDate: dates[0],
          dateRange1endDate: dates[1],
          dateRange2startDate: null,
          dateRange2endDate: null,
          dateRange3startDate: null,
          dateRange3endDate: null,
          dateRange4startDate: null,
          dateRange4endDate: null,
          dateRange5startDate: null,
          dateRange5endDate: null,
        };

        spectator.service
          .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
          .subscribe((value) => {
            expect(value).toEqual(expectedParams);
          });
      }));
    });

    describe(`Given a state where ActivatedRoute has ${QueryParamsKeys.DATES} set with all the dates`, () => {
      const dates = [
        '2024-01-01',
        '2024-12-31',
        '2023-01-01',
        '2023-12-31',
        '2022-01-01',
        '2022-12-31',
        '2021-01-01',
        '2021-12-31',
        '2020-01-01',
        '2020-12-31',
      ];

      const expectedDateRangesParams = {
        dateRange1startDate: dates[0],
        dateRange1endDate: dates[1],
        dateRange2startDate: dates[2],
        dateRange2endDate: dates[3],
        dateRange3startDate: dates[4],
        dateRange3endDate: dates[5],
        dateRange4startDate: dates[6],
        dateRange4endDate: dates[7],
        dateRange5startDate: dates[8],
        dateRange5endDate: dates[9],
      };

      beforeEach(() => {
        activatedRouteStub.addQueryParam(
          QueryParamsKeys.DATES,
          JSON.stringify(dates)
        );
      });

      it('should returns the params', waitForAsync(() => {
        spectator.service
          .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
          .subscribe((value) => {
            expect(value).toEqual(expectedDateRangesParams);
          });
      }));

      describe(`and given a state where ActivatedRoute has ${QueryParamsKeys.COMPANIES_IDS} in its query params`, () => {
        describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} is set with value a list of 1 company id`, () => {
          const [singleId] = mockComaniesIds;

          beforeEach(() => {
            activatedRouteStub.addQueryParam(
              QueryParamsKeys.COMPANIES_IDS,
              singleId
            );
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              companyIds: singleId,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} have for value a list of more than 1 company id`, () => {
          beforeEach(() => {
            activatedRouteStub.addQueryParam(
              QueryParamsKeys.COMPANIES_IDS,
              mockComaniesIds
            );
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              companyIds: mockComaniesIds,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} have for value an empty array`, () => {
          beforeEach(() => {
            activatedRouteStub.addQueryParam(QueryParamsKeys.COMPANIES_IDS, []);
          });

          it(`should returns the params`, waitForAsync(() => {
            const expectedParams = {
              companyIds: [],
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.COMPANIES_IDS} is set with value undefined`, () => {
          beforeEach(() => {
            activatedRouteStub.addQueryParam(
              QueryParamsKeys.COMPANIES_IDS,
              undefined
            );
          });

          it(`should returns an object with the property ${QueryParamsKeys.COMPANIES_IDS} set to undefined`, waitForAsync(() => {
            const expectedParams = {
              companies: undefined,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });
      });

      describe(`and given a state where ActivatedRoute has ${StimulsoftQueryParamsKeys.TREATMENT_ID} in its query params`, () => {
        describe(`and query params ${StimulsoftQueryParamsKeys.TREATMENT_ID} is set with an id`, () => {
          const fakeTreatmentId = '467885652';

          beforeEach(() => {
            activatedRouteStub.addQueryParam(
              StimulsoftQueryParamsKeys.TREATMENT_ID,
              fakeTreatmentId
            );
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              treatmentId: fakeTreatmentId,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${StimulsoftQueryParamsKeys.TREATMENT_ID} is set with undefined`, () => {
          beforeEach(() => {
            activatedRouteStub.addQueryParam(
              StimulsoftQueryParamsKeys.TREATMENT_ID,
              undefined
            );
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              treatmentId: undefined,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });
      });

      describe(`and given a state where ActivatedRoute has ${QueryParamsKeys.VAT_RATE} in its query params`, () => {
        describe(`and query params ${QueryParamsKeys.VAT_RATE} is set with valid value`, () => {
          const vatRate = '12.65';

          beforeEach(() => {
            activatedRouteStub.addQueryParam(QueryParamsKeys.VAT_RATE, vatRate);
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              vatRate: Number(vatRate),
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.VAT_RATE} is set with an invalid value`, () => {
          const vatRate = 'INVALID_VALUE';

          beforeEach(() => {
            activatedRouteStub.addQueryParam(QueryParamsKeys.VAT_RATE, vatRate);
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              vatRate: undefined,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.VAT_RATE} is set with 0`, () => {
          const vatRate = '0';

          beforeEach(() => {
            activatedRouteStub.addQueryParam(QueryParamsKeys.VAT_RATE, vatRate);
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              vatRate: Number(vatRate),
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });

        describe(`and query params ${QueryParamsKeys.VAT_RATE} is set with undefined`, () => {
          const vatRate = undefined;

          beforeEach(() => {
            activatedRouteStub.addQueryParam(QueryParamsKeys.VAT_RATE, vatRate);
          });

          it('should returns the params', waitForAsync(() => {
            const expectedParams = {
              vatRate: undefined,
              ...expectedDateRangesParams,
            };

            spectator.service
              .getBusinessReviewStimulsoftPropertiesFromQueryParams$()
              .subscribe((value) => {
                expect(value).toEqual(expectedParams);
              });
          }));
        });
      });
    });
  });
});
