const { execSync } = require('child_process');

const changedFiles = execSync('git diff --cached --name-only', { encoding: 'utf-8' })
  .split('\n')
  .filter(f => f.endsWith('.ts') && !f.includes('.spec.ts'));

const testFiles = changedFiles
  .map(file => {
    const testFile = file.replace(/\.ts$/, '.spec.ts');
    return require('fs').existsSync(testFile) ? testFile : null;
  })
  .filter(Boolean);

if (testFiles.length === 0) {
  console.log('✅ No associated test found.');
  process.exit(0);
}

console.log('▶️  Executed test files:', testFiles.join(', '));
execSync(`npx jest ${testFiles.join(' ')}`, { stdio: 'inherit' });
