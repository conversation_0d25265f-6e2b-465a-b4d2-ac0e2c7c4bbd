@if (rangeFormGroup) {
  <mat-form-field
    appearance="outline"
    *transloco="let t"
    subscriptSizing="dynamic">
    <mat-label>{{
      t('accounting.monthly-edition-tab.form.label.select-date-range')
    }}</mat-label>
    <mat-date-range-input [formGroup]="rangeFormGroup" [rangePicker]="picker">
      <input
        matInput
        ingMatDatepickerDirective
        matStartDate
        formControlName="start"
        [placeholder]="t('sharedForm.placeholder.start-date')" />
      <input
        matInput
        ingMatDatepickerDirective
        matEndDate
        formControlName="end"
        [placeholder]="t('sharedForm.placeholder.end-date')"
        (dateInput)="rangeFormGroup.controls.end.updateValueAndValidity()" />
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker" />
    <mat-date-range-picker #picker />
    @if (
      rangeFormGroup &&
        (rangeFormGroup.valueChanges | ngrxPush | toErrorKey: rangeFormGroup);
      as errorKey
    ) {
      <mat-error> {{ t(errorKey) }}</mat-error>
    }
  </mat-form-field>
}
