import { FiltersComponent } from './filters.component';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { MatDatepickerDirective } from '@isagri-ng/ui/angular-material';
import { TranslocoDirective } from '@jsverse/transloco';
import { CompanyService } from '@gc/company/data-access';
import { SharedUserFacade } from '@gc/core/shared/user/application/facades';
import { RepresentativesFacade } from '@gc/core/sales/representatives/application/facades';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { of } from 'rxjs';
import { ResourceState } from '@gc/core/shared/store';
import { Representative } from '@gc/core/sales/representatives/domains/models';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { signal } from '@angular/core';

describe('FiltersComponent', () => {
  let fixture: ComponentFixture<FiltersComponent>;
  let component: FiltersComponent;
  let companyService: CompanyService;
  let sharedUserFacade: SharedUserFacade;
  let representativesFacade: RepresentativesFacade;
  let commissionsFacade: CommissionsFacade;

  const mockRepresentatives: Representative[] = [
    { id: '1', fullname: 'John Doe' } as Representative,
    { id: '2', fullname: 'Jane Smith' } as Representative,
  ];

  const mockResourceState: ResourceState<Representative[]> = {
    data: mockRepresentatives,
    isLoading: false,
    status: 'Success',
    errors: undefined,
  };

  const mockSelectedRepresentativeState: ResourceState<Representative> = {
    data: mockRepresentatives[0],
    isLoading: false,
    status: 'Success',
    errors: undefined,
  };

  beforeEach(() => {
    // Create mock facades
    const representativesFacadeMock = {
      getRepresentativesForCurrentCompany: jest
        .fn()
        .mockReturnValue(signal(mockResourceState)),
      getSelectedRepresentative: jest
        .fn()
        .mockReturnValue(signal(mockSelectedRepresentativeState)),
      loadRepresentativesForCompany: jest.fn(),
      setSelectedRepresentative: jest.fn(),
      clearRepresentative: jest.fn(),
    };

    const commissionsFacadeMock = {
      clearCommissions: jest.fn(),
      loadCommissionsForFilter: jest.fn(),
    };

    const sharedUserFacadeMock = {
      getCompanyForCurrentUser: jest
        .fn()
        .mockReturnValue(signal({ data: 'company-1' })),
    };

    const companyServiceMock = {
      companyIdIfSingleCompany$: of('company-1'),
    };

    TestBed.configureTestingModule({
      declarations: [
        FiltersComponent,
        MockDirectives(TranslocoDirective, MatDatepickerDirective),
        MockComponents(CompanySingleSelectComponent),
      ],
      imports: [ReactiveFormsModule, MatDatepickerModule, MatNativeDateModule],
      providers: [
        { provide: RepresentativesFacade, useValue: representativesFacadeMock },
        { provide: CommissionsFacade, useValue: commissionsFacadeMock },
        { provide: SharedUserFacade, useValue: sharedUserFacadeMock },
        { provide: CompanyService, useValue: companyServiceMock },
      ],
    });

    fixture = TestBed.createComponent(FiltersComponent);
    component = fixture.componentInstance;

    // Get injected services
    companyService = TestBed.inject(CompanyService);
    sharedUserFacade = TestBed.inject(SharedUserFacade);
    representativesFacade = TestBed.inject(RepresentativesFacade);
    commissionsFacade = TestBed.inject(CommissionsFacade);

    // Initialize component's signals
    component.filteredRepresentatives = signal(mockRepresentatives);
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('form', () => {
    it('should initialize form controls', () => {
      expect(component.companyIdFC).toBeDefined();
      expect(component.representativeFC).toBeDefined();
      expect(component.dateCommissionsDueFC).toBeDefined();
    });

    it('should initialize filterFG with correct form controls', () => {
      expect(component.filterFG.get('companyIdFC')).toBe(component.companyIdFC);
      expect(component.filterFG.get('representativeFC')).toBe(
        component.representativeFC
      );
      expect(component.filterFG.get('dateCommissionsDueFC')).toBe(
        component.dateCommissionsDueFC
      );
      expect(component.filterFG.valid).toBeFalsy();
    });
  });

  describe('ngOnInit', () => {
    it('should clear commissions when form is invalid', fakeAsync(() => {
      // Spy on the clearCommissions method
      const clearCommissionsSpy = jest.spyOn(
        commissionsFacade,
        'clearCommissions'
      );

      // Initialize component
      fixture.detectChanges();

      // Trigger the form validation by setting an invalid value
      component.representativeFC.setValue('');
      component.filterFG.updateValueAndValidity();

      // Simulate the effect running
      tick(300); // Account for debounceTime

      // Verify clearCommissions was called
      expect(clearCommissionsSpy).toHaveBeenCalled();
    }));

    it('should load commissions when form is valid', fakeAsync(() => {
      // Spy on the loadCommissionsForFilter method
      const loadCommissionsSpy = jest.spyOn(
        commissionsFacade,
        'loadCommissionsForFilter'
      );

      // Initialize component
      fixture.detectChanges();

      // Set valid form values
      component.companyIdFC.setValue('company-1');
      component.representativeFC.setValue(mockRepresentatives[0]);
      component.dateCommissionsDueFC.setValue(new Date());
      component.filterFG.updateValueAndValidity();

      // Simulate the effect running
      tick(300); // Account for debounceTime

      // Verify loadCommissionsForFilter was called with correct params
      expect(loadCommissionsSpy).toHaveBeenCalledWith({
        companyId: 'company-1',
        representativeId: '1',
        date: expect.any(String),
      });
    }));
  });

  describe('private methods', () => {
    it('should call loadRepresentativesForCompany when company changes', () => {
      // Spy on loadRepresentativesForCompany
      const loadRepresentativesSpy = jest.spyOn(
        representativesFacade,
        'loadRepresentativesForCompany'
      );

      // Initialize component
      fixture.detectChanges();

      // Clear previous calls
      loadRepresentativesSpy.mockClear();

      // Change company value
      component.companyIdFC.setValue('new-company-id');

      // Verify loadRepresentativesForCompany was called with the new company ID
      expect(loadRepresentativesSpy).toHaveBeenCalledWith('new-company-id');
    });

    it('should filter representatives when search input changes', fakeAsync(() => {
      // Spy on the signal's set method
      const setFilteredRepresentativesSpy = jest.spyOn(
        component.filteredRepresentatives,
        'set'
      );

      // Initialize component
      fixture.detectChanges();

      // Trigger the search input change
      component.representativeFC.setValue('John');
      tick(300); // Account for debounceTime

      // Verify filteredRepresentatives was updated with filtered results
      expect(setFilteredRepresentativesSpy).toHaveBeenCalled();
    }));
  });
});
