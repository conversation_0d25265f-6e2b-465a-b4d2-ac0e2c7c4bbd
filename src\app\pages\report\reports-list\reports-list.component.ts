// Ticket de refacto: https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_workitems/edit/81104
/* eslint-disable max-lines */
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  ViewChild,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import {
  CompanyMultiSelectComponent,
  CompanyMultiSelectDefaultSelectionMode,
  IsSingleCompanyDirective,
} from '@gc/company/feature';
import {
  MainContainerComponent,
  NavigationService,
} from '@gc/core/navigation/feature';
import {
  ReportCategoryService,
  ReportStoreModule,
  selectCustomReportsCount,
  selectReports,
  selectReportsCategories,
  reportActions,
} from '@gc/report/data-access';
import { ReportCategory, ReportInformation } from '@gc/report/models';
import { SearchBarComponent } from '@gc/report/ui';
import {
  ListEmptyComponent,
  ToggleLayoutComponent,
  ToggleableLayoutButtonComponent,
  ToggleLayoutOptions,
} from '@gc/shared/ui';
import { Store } from '@ngrx/store';
import {
  BehaviorSubject,
  combineLatest,
  concat,
  filter,
  first,
  map,
  Observable,
  Subject,
  switchMap,
  tap,
} from 'rxjs';
import { PushPipe } from '@ngrx/component';
import {
  CustomReportCardsComponent,
  ReportCardGridComponent,
  ReportCardRowComponent,
  ReportCardsViewerComponent,
  ReportCategoryChipAutocompleteComponent,
} from '@gc/report/feature';
import { AsyncPipe, UpperCasePipe } from '@angular/common';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { MatTabsModule } from '@angular/material/tabs';
import { QueryParamsKeys, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { URL_PATHS } from '@gc/core/navigation/models';
import { UserStoreService } from '@gc/user/data-access';
import { CompanyService } from '@gc/company/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-reports-list',
  templateUrl: './reports-list.component.html',
  styleUrls: ['./reports-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    TranslocoModule,
    MainContainerComponent,
    IsSingleCompanyDirective,
    ReactiveFormsModule,
    CompanyMultiSelectComponent,
    ReportCategoryChipAutocompleteComponent,
    SearchBarComponent,
    ToggleableLayoutButtonComponent,
    ListEmptyComponent,
    ToggleLayoutComponent,
    ReportCardGridComponent,
    ReportCardRowComponent,
    AsyncPipe,
    PushPipe,
    ReportStoreModule,
    MatTabsModule,
    UpperCasePipe,
    ReportCardsViewerComponent,
    CustomReportCardsComponent,
  ],
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'reports', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'company', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/form', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
  ],
})
export class ReportsListComponent implements OnInit {
  private readonly _activatedRoute = inject(ActivatedRoute);
  private readonly _categoryService = inject(ReportCategoryService);
  private readonly _store = inject(Store);
  private readonly _navigationService = inject(NavigationService);
  private readonly _userStoreService = inject(UserStoreService);
  private readonly _companyService = inject(CompanyService);
  private readonly _destroyRef = inject(DestroyRef);

  @ViewChild(SearchBarComponent) searchBarComponent!: SearchBarComponent;

  useView: ToggleLayoutOptions = ToggleLayoutOptions.GRID;
  gridView = ToggleLayoutOptions.GRID;

  companySelectDefaultSelectionMode =
    CompanyMultiSelectDefaultSelectionMode.ALL;

  compagniesListbyId$!: Observable<string[]>;

  companiesFC: FormControl<string[]> = new FormControl<string[]>([], {
    validators: [Validators.required],
    nonNullable: true,
  });

  formGroup: FormGroup = new FormGroup({
    companiesFC: this.companiesFC,
  });

  filteredReports$$ = new BehaviorSubject<ReportInformation[] | null>(null);
  filteredReports$ = this.filteredReports$$.asObservable();

  customReportsCount$: Observable<number> = this._store.select(
    selectCustomReportsCount
  );

  categoryFilterCtrl: FormControl<ReportCategory[]> = new FormControl<
    ReportCategory[]
  >([], {
    nonNullable: true,
  });

  _categories$ = this._store
    .select(selectReportsCategories)
    .pipe(filter(Boolean));

  /**
   * Whole reports (page init) + reports by category
   *
   * @type {(Observable<ReportInformation[] | null>)}
   * @memberof ReportsListComponent
   */
  filteredReportsByCategory$!: Observable<ReportInformation[] | null>;

  private _selectedCategoriesIdsFromAutocompleteFilter$ =
    this.categoryFilterCtrl.valueChanges.pipe(
      map((categories: ReportCategory[]) => categories.map(({ id }) => id))
    );

  private _selectedCategoriesIdsFromUrl$ = combineLatest([
    this._activatedRoute.queryParamMap,
    this._categories$,
  ]).pipe(
    filter(([_params, categories]) => categories.length > 0),
    map(([params, categories]) => {
      const categoryKey = 'category';
      const queryParamsCategory = params.get(categoryKey);
      const categoryIdFromQueryParam =
        this._categoryService.getCategoryIdFromQueryParamsValue(
          queryParamsCategory
        );
      if (categoryIdFromQueryParam) {
        return categories.find(
          (category) =>
            category.id.toLowerCase() === categoryIdFromQueryParam.toLowerCase()
        );
      }
      return null;
    }),
    map((category: ReportCategory | null | undefined) => {
      return category !== null && category !== undefined ? [category] : [];
    }),
    tap((selectedCategories: ReportCategory[]) => {
      this.categoryFilterCtrl.setValue(selectedCategories);
    }),
    map((categories: ReportCategory[]) => categories.map(({ id }) => id))
  );

  private _reportsSortedByTitle$: Observable<ReportInformation[]> = this._store
    .select(selectReports)
    .pipe(filter(Boolean));

  private _filteredReportsWithCategoriesFromUrl$ = combineLatest([
    this._reportsSortedByTitle$,
    this._selectedCategoriesIdsFromUrl$,
  ]).pipe(
    filter(([reports, _categoryFilterIds]) => reports?.length > 0),
    map(([reports, categoryFilterIds]) =>
      this._filter(reports, categoryFilterIds)
    )
  );

  private _filteredReportsWithCategoriesFromSearchbar$$ = new Subject<
    ReportInformation[]
  >();

  private _filteredReportsWithCategoriesFromAutocompleteFilter$ = combineLatest(
    [
      this._reportsSortedByTitle$,
      this._selectedCategoriesIdsFromAutocompleteFilter$,
    ]
  ).pipe(
    filter(
      ([reports, _categoryFilterIds]) =>
        reports.length > 0 && reports !== undefined
    ),
    map(([reports, categoryFilterIds]) =>
      this._filter(reports, categoryFilterIds)
    )
  );

  ngOnInit(): void {
    this._store.dispatch(reportActions.reportsListComponentLoaded());

    this._selectAllCompanies();

    this._filteredReportsWithCategoriesFromUrl$
      .pipe(
        tap((filteredReports: ReportInformation[]) => {
          this.filteredReports$$.next(filteredReports);
        }),
        switchMap(() => this._filteredReportsWithCategoriesFromSearchbar$$),
        tap((filteredReports: ReportInformation[]) => {
          this.filteredReports$$.next(filteredReports);
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();

    this.filteredReportsByCategory$ = concat(
      this.filteredReports$.pipe(first()),
      this._filteredReportsWithCategoriesFromAutocompleteFilter$
    );
  }

  toggleView(event: ToggleLayoutOptions): void {
    this.useView = event;
  }

  /**
   * Apply new list after search event
   *
   * @param {ReportInformation[]} filteredReports
   * @memberof ReportsListComponent
   */
  onSearch(filteredReports: ReportInformation[]): void {
    if (!(filteredReports instanceof Event)) {
      this._filteredReportsWithCategoriesFromSearchbar$$.next(filteredReports);
    }
  }

  openStimulsoftReport(reportInformation: ReportInformation): void {
    const queryParams: Params = {
      [StimulsoftQueryParamsKeys.REPORT_ID]: reportInformation.id,
      [StimulsoftQueryParamsKeys.REPORT_FAMILY]: reportInformation.reportFamily,
      [QueryParamsKeys.DEFAULT_COMPANY_ID]:
        this._userStoreService.getUserDefaultCompanyId(),
      // TODO A retravailler avec Ticket de refacto: https://dev.azure.com/Isagri-Prod-Progiciels/Isagri_Dev_GC_GestionCommerciale/_workitems/edit/81104 (Obtenir this.companiesFC.value actuellement dans ReportListComponent)
      [QueryParamsKeys.COMPANIES_IDS]: [],
      title: reportInformation.title,
    };

    this._navigationService.openInNewTab(URL_PATHS.reportsReport, queryParams);
  }

  private _filter(
    reports: ReportInformation[],
    categoriesIds: string[]
  ): ReportInformation[] {
    if (!categoriesIds || categoriesIds.length === 0) {
      return reports;
    }

    return reports.reduce(
      (filteredReports: ReportInformation[], report: ReportInformation) => {
        if (categoriesIds.every((catId) => report.categories.includes(catId))) {
          filteredReports.push(report);
        }
        return filteredReports;
      },
      []
    );
  }

  private _selectAllCompanies(): void {
    this._companyService.companiesIds$
      .pipe(
        filter(Boolean),
        tap((companiesId) => {
          this.companiesFC.setValue(companiesId);
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }
}
