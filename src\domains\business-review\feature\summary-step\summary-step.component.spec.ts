import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { provideMockStore } from '@ngrx/store/testing';
import { SummaryStepComponent } from './summary-step.component';
import { FiscalYearService } from '@gc/fiscal-year/data-access';
import { WarehouseService } from '@gc/warehouse/data-access';
import { VatService } from '@gc/vat/data-access';
import { CompanyService } from '@gc/company/data-access';
import { of } from 'rxjs';

describe('SummaryStepComponent', () => {
  let spectator: Spectator<SummaryStepComponent>;
  const createComponent = createComponentFactory({
    component: SummaryStepComponent,
    declarations: [MockDirective(TranslocoDirective)],
    providers: [provideMockStore()],
    mocks: [FiscalYearService, WarehouseService, VatService, CompanyService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();

    const companyService = spectator.inject(CompanyService);
    jest.spyOn(companyService, 'companies$', 'get').mockReturnValue(of([]));

    spectator.detectChanges();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });
});
