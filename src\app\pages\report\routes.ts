import { Routes } from '@angular/router';
import {
  getTitleReport,
  setHeaderTitleGuard,
} from '@gc/core/navigation/feature';
import { TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';

export const ROUTES: Routes = [
  {
    path: '',
    loadChildren: () => import('./reports-list/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.REPORTS,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.REPORTS)],
  },
  {
    path: 'report',
    loadChildren: () => import('./report/routes').then((m) => m.ROUTES),
    title: getTitleReport,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.REPORT)],
  },
];
