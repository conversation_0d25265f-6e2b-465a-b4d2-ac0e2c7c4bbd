import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { DebFacade } from './deb.facade';
import { DebStore, DebStoreEnum } from '@gc/core/deb/application/store';
import {
  ClosedMonthsUseCase,
  ClosureUseCase,
  DestinationCountryCodesUseCase,
  DraftDeclarationsUseCase,
  EuNomenclaturesUseCase,
  LoadDetailsUseCase,
  ParametersUseCase,
  StatisticalProceduresUseCase,
  UncloseMonthUseCase,
} from '../use-cases';
import { of, throwError } from 'rxjs';
import { signal } from '@angular/core';
import {
  DebParameters,
  DeclarationType,
  LoadDetailsParameters,
} from '@gc/core/deb/domains/models';
import { ResourceState } from '@gc/core/shared/store';
import { SnackbarService } from '@gc/shared/ui';
import { HttpErrorResponse } from '@angular/common/http';

describe('DebFacade', () => {
  let spectator: SpectatorService<DebFacade>;
  let facade: DebFacade;
  let debStore: DebStore;
  let uncloseMonthUseCase: UncloseMonthUseCase;
  let closedMonthsUseCase: ClosedMonthsUseCase;
  let parametersUseCase: ParametersUseCase;
  let closureUseCase: ClosureUseCase;
  let snackbarService: SnackbarService;

  const createService = createServiceFactory({
    service: DebFacade,
    mocks: [
      DebStore,
      UncloseMonthUseCase,
      ClosedMonthsUseCase,
      ParametersUseCase,
      DraftDeclarationsUseCase,
      ClosureUseCase,
      LoadDetailsUseCase,
      EuNomenclaturesUseCase,
      StatisticalProceduresUseCase,
      DestinationCountryCodesUseCase,
      SnackbarService,
    ],
  });

  beforeEach(() => {
    spectator = createService();
    facade = spectator.service;
    debStore = spectator.inject(DebStore);
    uncloseMonthUseCase = spectator.inject(UncloseMonthUseCase);
    closedMonthsUseCase = spectator.inject(ClosedMonthsUseCase);
    parametersUseCase = spectator.inject(ParametersUseCase);
    closureUseCase = spectator.inject(ClosureUseCase);
    snackbarService = spectator.inject(SnackbarService);
  });

  describe('uncloseMonthFor', () => {
    const companyId = 'company-123';
    const month = new Date('2024-05-01');
    const mockParameters: ResourceState<DebParameters> = {
      data: {
        declarantName: 'Test Declarant',
        authorizationNumber: 'AUTH123',
        declarationNumber: 5,
        declarationType: 'full',
      },
      isLoading: false,
      status: 'Success',
      errors: undefined,
    };

    beforeEach(() => {
      (debStore.get as jest.Mock).mockImplementation((key) => {
        if (key === DebStoreEnum.PARAMETERS) {
          return signal(mockParameters);
        }
        return signal({});
      });

      jest
        .spyOn(facade as any, 'shouldDecreaseDeclarationNumber')
        .mockReturnValue(of(true));

      (uncloseMonthUseCase.for as jest.Mock).mockReturnValue(of(undefined));
      (closedMonthsUseCase.for as jest.Mock).mockReturnValue(of([]));
      (parametersUseCase.saveFor as jest.Mock).mockReturnValue(of(undefined));
    });

    it('should call uncloseMonth.for with the correct parameters', () => {
      facade.uncloseMonthFor(companyId, month);

      expect(uncloseMonthUseCase.for).toHaveBeenCalledWith(companyId, month);
    });

    it('should call getClosedMonths.for after successful unclose', () => {
      facade.uncloseMonthFor(companyId, month);

      expect(closedMonthsUseCase.for).toHaveBeenCalledWith(companyId);
    });

    it('should update the store with success status after successful unclose', () => {
      jest
        .spyOn(facade as any, 'shouldDecreaseDeclarationNumber')
        .mockReturnValue(of(false));

      facade.uncloseMonthFor(companyId, month);

      expect(debStore.update).toHaveBeenCalledWith(DebStoreEnum.UNCLOSE_MONTH, {
        isLoading: false,
        status: 'Success',
      });

      expect(debStore.update).toHaveBeenCalledWith(DebStoreEnum.CLOSURE, {
        status: 'Success',
      });
    });

    it('should clear declarations from the store after successful unclose', () => {
      jest
        .spyOn(facade as any, 'shouldDecreaseDeclarationNumber')
        .mockReturnValue(of(false));

      facade.uncloseMonthFor(companyId, month);

      expect(debStore.clear).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS);
    });

    it('should call saveParametersFor with decremented declarationNumber when shouldDecreaseDeclarationNumber returns true', () => {
      const saveParametersSpy = jest.spyOn(facade, 'saveParametersFor');
      jest
        .spyOn(facade as any, 'shouldDecreaseDeclarationNumber')
        .mockReturnValue(of(true));

      facade.uncloseMonthFor(companyId, month);

      expect(saveParametersSpy).toHaveBeenCalledWith(
        {
          ...mockParameters.data,
          declarationNumber: mockParameters.data!.declarationNumber! - 1,
        },
        companyId
      );
    });

    it('should not call saveParametersFor when shouldDecreaseDeclarationNumber returns false', () => {
      const saveParametersSpy = jest.spyOn(facade, 'saveParametersFor');
      jest
        .spyOn(facade as any, 'shouldDecreaseDeclarationNumber')
        .mockReturnValue(of(false));

      facade.uncloseMonthFor(companyId, month);

      expect(saveParametersSpy).not.toHaveBeenCalled();
    });

    it('should update the store with error status if unclose fails', () => {
      (uncloseMonthUseCase.for as jest.Mock).mockReturnValue(
        throwError(() => new Error('Failed to unclose'))
      );

      (debStore.update as jest.Mock).mockClear();

      facade.uncloseMonthFor(companyId, month);

      expect(debStore.update).toHaveBeenCalledWith(DebStoreEnum.UNCLOSE_MONTH, {
        isLoading: false,
        status: 'Error',
      });
    });
  });

  describe('saveClosureFor', () => {
    const monthToClose = {
      companyId: 'company-123',
      month: new Date('2024-05-01'),
      declarationNumber: 5,
      invoicesIncluded: true,
      deliveryNotesIncluded: true,
      archiveOnly: false,
      declarations: [],
      declarationType: DeclarationType.LIGHT as DeclarationType.LIGHT,
    };
    const closureResponse = {
      data: new ArrayBuffer(0),
      filename: 'deb-file.xml',
    };

    beforeEach(() => {
      (closureUseCase.for as jest.Mock).mockReturnValue(of(closureResponse));
      (parametersUseCase.saveFor as jest.Mock).mockReturnValue(of(undefined));
      (parametersUseCase.getFor as jest.Mock).mockReturnValue(of({}));
      (closedMonthsUseCase.for as jest.Mock).mockReturnValue(of([]));

      // Mock the downloadXmlFile method to prevent actual DOM manipulation
      jest.spyOn(facade as any, 'downloadXmlFile').mockImplementation(() => {});
    });

    it('should call saveClosure.for with the correct parameters', () => {
      facade.saveClosureFor(monthToClose);

      expect(closureUseCase.for).toHaveBeenCalledWith(monthToClose);
    });

    it('should display a success message when XML file is downloaded', () => {
      const successSpy = jest.spyOn(snackbarService, 'success');

      facade.saveClosureFor(monthToClose);

      expect(successSpy).toHaveBeenCalledWith({
        key: 'deb.statement-page.closed-month.file-downloaded',
      });
    });

    it('should not display a success message for first declaration', () => {
      const successSpy = jest.spyOn(snackbarService, 'success');

      facade.saveClosureFor(monthToClose, { firstDeclaration: true });

      expect(successSpy).not.toHaveBeenCalled();
    });

    it('should not download XML file when both filename and data are missing', () => {
      const downloadXmlFileSpy = jest.spyOn(facade as any, 'downloadXmlFile');
      const successSpy = jest.spyOn(snackbarService, 'success');

      const emptyResponse = {
        data: undefined,
        filename: undefined,
      };
      (closureUseCase.for as jest.Mock).mockReturnValue(of(emptyResponse));

      facade.saveClosureFor(monthToClose);

      expect(downloadXmlFileSpy).not.toHaveBeenCalled();
      expect(successSpy).not.toHaveBeenCalled();
    });

    it('should use filename from the response when downloading XML file', () => {
      const downloadXmlFileSpy = jest.spyOn(facade as any, 'downloadXmlFile');
      const successSpy = jest.spyOn(snackbarService, 'success');
      const responseWithFilename = {
        data: new Uint8Array([1, 2, 3]).buffer,
        filename: 'deb-file-from-response.xml',
      };
      const mockArrayBuffer = new Uint8Array([1, 2, 3]).buffer;
      (closureUseCase.for as jest.Mock).mockReturnValue(
        of({ file: mockArrayBuffer, filename: responseWithFilename.filename })
      );

      facade.saveClosureFor(monthToClose);

      expect(downloadXmlFileSpy).toHaveBeenCalledWith(
        mockArrayBuffer,
        responseWithFilename.filename
      );
      expect(successSpy).toHaveBeenCalled();
    });

    it('should automatically download XML file when not in firstDeclaration mode', () => {
      const downloadXmlFileSpy = jest.spyOn(facade as any, 'downloadXmlFile');

      facade.saveClosureFor(monthToClose);

      expect(downloadXmlFileSpy).toHaveBeenCalled();
    });

    it('should not download XML file in firstDeclaration mode', () => {
      const downloadXmlFileSpy = jest.spyOn(facade as any, 'downloadXmlFile');

      facade.saveClosureFor(monthToClose, { firstDeclaration: true });

      expect(downloadXmlFileSpy).not.toHaveBeenCalled();
    });

    it('should clear declarations and closed months from store after successful closure', () => {
      facade.saveClosureFor(monthToClose);

      expect(debStore.clear).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS);
      expect(debStore.clear).toHaveBeenCalledWith(DebStoreEnum.CLOSED_MONTHS);
    });

    it('should update closure status in store after successful closure', () => {
      facade.saveClosureFor(monthToClose);

      expect(debStore.update).toHaveBeenCalledWith(DebStoreEnum.CLOSURE, {
        data: closureResponse,
        errors: undefined,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should reload parameters and closed months after successful closure', () => {
      const loadParametersSpy = jest.spyOn(facade, 'loadParametersForCompany');
      const loadClosedMonthsSpy = jest.spyOn(
        facade,
        'loadClosedMonthsForCompany'
      );

      facade.saveClosureFor(monthToClose);

      expect(loadParametersSpy).toHaveBeenCalledWith(monthToClose.companyId);
      expect(loadClosedMonthsSpy).toHaveBeenCalledWith(monthToClose.companyId);
    });
  });

  describe('saveParametersFor', () => {
    const companyId = 'company-123';
    const mockParameters: DebParameters = {
      declarantName: 'Test Declarant',
      authorizationNumber: 'AUTH123',
      declarationNumber: 5,
      declarationType: 'full',
    };

    beforeEach(() => {
      (parametersUseCase.saveFor as jest.Mock).mockReturnValue(of(undefined));
      (parametersUseCase.getFor as jest.Mock).mockReturnValue(
        of(mockParameters)
      );
    });

    it('should call parametersUseCase.saveFor with correct parameters', () => {
      facade.saveParametersFor(mockParameters, companyId);

      expect(parametersUseCase.saveFor).toHaveBeenCalledWith(
        mockParameters,
        companyId
      );
    });

    it('should call parametersUseCase.getFor after saving parameters', () => {
      facade.saveParametersFor(mockParameters, companyId);

      expect(parametersUseCase.getFor).toHaveBeenCalledWith(companyId);
    });

    it('should update store with parameters data after saving', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.saveParametersFor(mockParameters, companyId);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS, {
        data: mockParameters,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.saveParametersFor(mockParameters, companyId);

      // The decorator should call startLoading instead of update
      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS);
    });
  });
  describe('loadParametersForCompany', () => {
    const companyId = 'company-123';
    const mockParameters: DebParameters = {
      declarantName: 'Test Declarant',
      authorizationNumber: 'AUTH123',
      declarationNumber: 5,
      declarationType: 'full',
    };

    beforeEach(() => {
      (parametersUseCase.getFor as jest.Mock).mockReturnValue(
        of(mockParameters)
      );
    });

    it('should call parametersUseCase.getFor with correct companyId', () => {
      facade.loadParametersForCompany(companyId);

      expect(parametersUseCase.getFor).toHaveBeenCalledWith(companyId);
    });

    it('should update store with parameters data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadParametersForCompany(companyId);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS, {
        data: mockParameters,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadParametersForCompany(companyId);

      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS);
    });

    it('should update store with error state when parametersUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (parametersUseCase.getFor as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadParametersForCompany(companyId);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS, {
        data: undefined,
        isLoading: false,
        status: 'Error',
        errors: [
          {
            code: '0',
            message:
              'Http failure response for (unknown url): undefined undefined',
          },
        ],
      });
    });
  });

  describe('loadClosedMonthsForCompany', () => {
    const companyId = 'company-123';
    const mockClosedMonths = [new Date('2023-01-01'), new Date('2023-02-01')];

    beforeEach(() => {
      (closedMonthsUseCase.for as jest.Mock).mockReturnValue(
        of(mockClosedMonths)
      );
    });

    it('should call closedMonthsUseCase.for with correct companyId', () => {
      facade.loadClosedMonthsForCompany(companyId);

      expect(closedMonthsUseCase.for).toHaveBeenCalledWith(companyId);
    });

    it('should update store with closed months data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadClosedMonthsForCompany(companyId);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.CLOSED_MONTHS, {
        data: mockClosedMonths,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadClosedMonthsForCompany(companyId);

      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.CLOSED_MONTHS);
    });

    it('should update store with error state when closedMonthsUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (closedMonthsUseCase.for as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadClosedMonthsForCompany(companyId);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.CLOSED_MONTHS, {
        data: undefined,
        isLoading: false,
        status: 'Error',
        errors: [
          {
            code: '0',
            message:
              'Http failure response for (unknown url): undefined undefined',
          },
        ],
      });
    });
  });

  describe('loadDraftDeclarationsWithFilters', () => {
    const mockFilters = {
      includeDeliveryNotes: true,
      includeInvoices: false,
      declarationMonth: new Date('2023-05-01'),
      companyId: 'company-123',
    };

    const mockDraftDeclarations = [
      {
        id: 'draft-1',
        destinationCountryCode: 'FR',
        euNomenclature: 'EU123',
        invoicedAmount: 1000,
        statisticalProcedureCode: 'SPC1',
        vatNumber: 'VAT123',
        state: 'initial',
      },
      {
        id: 'draft-2',
        destinationCountryCode: 'DE',
        euNomenclature: 'EU456',
        invoicedAmount: 2000,
        statisticalProcedureCode: 'SPC2',
        vatNumber: 'VAT456',
        state: 'initial',
      },
    ];

    let draftDeclarationsUseCase: DraftDeclarationsUseCase;

    beforeEach(() => {
      draftDeclarationsUseCase = spectator.inject(DraftDeclarationsUseCase);
      (draftDeclarationsUseCase.with as jest.Mock).mockReturnValue(
        of(mockDraftDeclarations)
      );
    });

    it('should call draftDeclarationsUseCase.with with correct filters', () => {
      facade.loadDraftDeclarationsWithFilters(mockFilters);

      expect(draftDeclarationsUseCase.with).toHaveBeenCalledWith(mockFilters);
    });

    it('should update store with draft declarations data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadDraftDeclarationsWithFilters(mockFilters);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS, {
        data: mockDraftDeclarations,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadDraftDeclarationsWithFilters(mockFilters);

      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS);
    });

    it('should update store with error state when draftDeclarationsUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (draftDeclarationsUseCase.with as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadDraftDeclarationsWithFilters(mockFilters);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS, {
        data: undefined,
        isLoading: false,
        status: 'Error',
        errors: [
          {
            code: '0',
            message:
              'Http failure response for (unknown url): undefined undefined',
          },
        ],
      });
    });
  });

  describe('loadDetailsFor', () => {
    const mockDetailsParameters: LoadDetailsParameters = {
      companyId: 'company-123',
      deliveryNotesIncluded: true,
      destinationCountryCode: 'FR',
      invoicesIncluded: false,
      month: new Date('2023-05-01'),
      statisticalProcedureCode: 'SPC1',
      vatNumber: 'VAT123',
    };

    const mockDetails = [
      {
        documentType: 'Invoice',
        documentNumber: 'INV123',
        customerCode: 'CUST123',
        deliveryDate: '2023-05-01',
        productCode: 'PROD123',
        productLabel: 'Product 123',
        lineAmount: 1000,
      },
      {
        documentType: 'CreditNote',
        documentNumber: 'CN123',
        customerCode: 'CUST123',
        deliveryDate: '2023-05-01',
        productCode: 'PROD123',
        productLabel: 'Product 123',
        lineAmount: 2000,
      },
    ];

    let loadDetailsUseCase: LoadDetailsUseCase;

    beforeEach(() => {
      loadDetailsUseCase = spectator.inject(LoadDetailsUseCase);
      (loadDetailsUseCase.for as jest.Mock).mockReturnValue(of(mockDetails));
    });

    it('should call loadDetailsUseCase.for with correct parameters', () => {
      facade.loadDetailsFor(mockDetailsParameters);

      expect(loadDetailsUseCase.for).toHaveBeenCalledWith(
        mockDetailsParameters
      );
    });

    it('should update store with details data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadDetailsFor(mockDetailsParameters);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.DETAILS, {
        data: mockDetails,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadDetailsFor(mockDetailsParameters);

      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.DETAILS);
    });

    it('should update store with error state when loadDetailsUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (loadDetailsUseCase.for as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadDetailsFor(mockDetailsParameters);

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.DETAILS, {
        isLoading: false,
        status: 'Error',
        data: undefined,
        errors: [
          {
            code: '0',
            message:
              'Http failure response for (unknown url): undefined undefined',
          },
        ],
      });
    });
  });

  describe('loadEuNomenclatures', () => {
    const mockEuNomenclatures = ['EU123', 'EU456', 'EU789'];

    let euNomenclaturesUseCase: EuNomenclaturesUseCase;

    beforeEach(() => {
      euNomenclaturesUseCase = spectator.inject(EuNomenclaturesUseCase);
      (euNomenclaturesUseCase.get as jest.Mock).mockReturnValue(
        of(mockEuNomenclatures)
      );
    });

    it('should call euNomenclaturesUseCase.get', () => {
      facade.loadEuNomenclatures();

      expect(euNomenclaturesUseCase.get).toHaveBeenCalled();
    });

    it('should update store with eu nomenclatures data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadEuNomenclatures();

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.NOMENCLATURES, {
        data: mockEuNomenclatures,
        isLoading: false,
        status: 'Success',
      });
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadEuNomenclatures();

      expect(startLoadingSpy).toHaveBeenCalledWith(DebStoreEnum.NOMENCLATURES);
    });

    it('should update store with error state when euNomenclaturesUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (euNomenclaturesUseCase.get as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadEuNomenclatures();

      expect(storeSpy).toHaveBeenCalledWith(DebStoreEnum.NOMENCLATURES, {
        isLoading: false,
        status: 'Error',
        data: undefined,
        errors: [
          {
            code: '0',
            message:
              'Http failure response for (unknown url): undefined undefined',
          },
        ],
      });
    });
  });

  describe('loadStatisticalProceduresFor', () => {
    const companyId = 'company-123';
    const mockStatisticalProcedures = ['SPC1', 'SPC2', 'SPC3'];

    let statisticalProceduresUseCase: StatisticalProceduresUseCase;

    beforeEach(() => {
      statisticalProceduresUseCase = spectator.inject(
        StatisticalProceduresUseCase
      );
      (statisticalProceduresUseCase.for as jest.Mock).mockReturnValue(
        of(mockStatisticalProcedures)
      );
    });

    it('should call statisticalProceduresUseCase.for with correct companyId', () => {
      facade.loadStatisticalProceduresFor(companyId);

      expect(statisticalProceduresUseCase.for).toHaveBeenCalledWith(companyId);
    });

    it('should update store with statistical procedures data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadStatisticalProceduresFor(companyId);

      expect(storeSpy).toHaveBeenCalledWith(
        DebStoreEnum.STATISTICAL_PROCEDURES,
        {
          data: mockStatisticalProcedures,
          isLoading: false,
          status: 'Success',
        }
      );
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadStatisticalProceduresFor(companyId);

      expect(startLoadingSpy).toHaveBeenCalledWith(
        DebStoreEnum.STATISTICAL_PROCEDURES
      );
    });

    it('should update store with error state when statisticalProceduresUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (statisticalProceduresUseCase.for as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadStatisticalProceduresFor(companyId);

      expect(storeSpy).toHaveBeenCalledWith(
        DebStoreEnum.STATISTICAL_PROCEDURES,
        {
          isLoading: false,
          status: 'Error',
          data: undefined,
          errors: [
            {
              code: '0',
              message:
                'Http failure response for (unknown url): undefined undefined',
            },
          ],
        }
      );
    });
  });

  describe('loadDestinationCountryCodes', () => {
    const companyId = 'company-123';
    const month = new Date('2023-05-01');
    const mockDestinationCountryCodes = ['FR', 'DE', 'US'];

    let destinationCountryCodesUseCase: DestinationCountryCodesUseCase;

    beforeEach(() => {
      destinationCountryCodesUseCase = spectator.inject(
        DestinationCountryCodesUseCase
      );
      (destinationCountryCodesUseCase.for as jest.Mock).mockReturnValue(
        of(mockDestinationCountryCodes)
      );
    });

    it('should call destinationCountryCodesUseCase.for with correct companyId and month', () => {
      facade.loadDestinationCountryCodes(companyId, month);

      expect(destinationCountryCodesUseCase.for).toHaveBeenCalledWith(
        companyId,
        month
      );
    });

    it('should update store with destination country codes data', () => {
      const storeSpy = jest.spyOn(debStore, 'update');

      facade.loadDestinationCountryCodes(companyId, month);

      expect(storeSpy).toHaveBeenCalledWith(
        DebStoreEnum.DESTINATION_COUNTRY_CODES,
        {
          data: mockDestinationCountryCodes,
          isLoading: false,
          status: 'Success',
        }
      );
    });

    it('should handle loading state with AutoStartLoading decorator', () => {
      const startLoadingSpy = jest.spyOn(debStore, 'startLoading');

      facade.loadDestinationCountryCodes(companyId, month);

      expect(startLoadingSpy).toHaveBeenCalledWith(
        DebStoreEnum.DESTINATION_COUNTRY_CODES
      );
    });

    it('should update store with error state when destinationCountryCodesUseCase fails', () => {
      const error = new HttpErrorResponse({ error: new Error('Test error') });
      const storeSpy = jest.spyOn(debStore, 'update');

      (destinationCountryCodesUseCase.for as jest.Mock).mockReturnValue(
        throwError(() => error)
      );

      facade.loadDestinationCountryCodes(companyId, month);

      expect(storeSpy).toHaveBeenCalledWith(
        DebStoreEnum.DESTINATION_COUNTRY_CODES,
        {
          isLoading: false,
          status: 'Error',
          data: undefined,
          errors: [
            {
              code: '0',
              message:
                'Http failure response for (unknown url): undefined undefined',
            },
          ],
        }
      );
    });
  });

  describe('getParametersForCurrentCompany', () => {
    it('should return the parameters from the store', () => {
      const parameters = {
        declarantName: 'Test Declarant',
        authorizationNumber: 'AUTH123',
        declarationNumber: 5,
        declarationType: DeclarationType.LIGHT,
      };

      (debStore.get as jest.Mock).mockReturnValue(signal(parameters));

      expect(facade.getParametersForCurrentCompany()()).toBe(parameters);
    });
  });

  describe('getClosedMonthsForCurrentCompany', () => {
    it('should return the closed months from the store', () => {
      const closedMonths = [
        {
          companyId: 'company-123',
          declarationMonth: new Date('2023-05-01'),
          declarationNumber: 5,
        },
      ];

      (debStore.get as jest.Mock).mockReturnValue(signal(closedMonths));

      expect(facade.getClosedMonthsForCurrentCompany()()).toBe(closedMonths);
    });
  });

  describe('getDraftDeclarationsForCurrentFilters', () => {
    it('should return the draft declarations from the store', () => {
      const draftDeclarations = [
        {
          companyId: 'company-123',
          declarationMonth: new Date('2023-05-01'),
          declarationNumber: 5,
        },
      ];

      (debStore.get as jest.Mock).mockReturnValue(signal(draftDeclarations));

      expect(facade.getDraftDeclarationsForCurrentFilters()()).toBe(
        draftDeclarations
      );
    });
  });

  describe('getUncloseMonth', () => {
    it('should return the unclose month from the store', () => {
      const uncloseMonth = {
        isLoading: false,
        status: 'Success',
      };

      (debStore.get as jest.Mock).mockReturnValue(signal(uncloseMonth));

      expect(facade.getUncloseMonth()()).toBe(uncloseMonth);
    });
  });

  describe('getDetails', () => {
    it('should return the details from the store', () => {
      const details = [
        {
          documentType: 'Invoice',
          documentNumber: 'INV123',
          customerCode: 'CUST123',
          deliveryDate: '2023-05-01',
          productCode: 'PROD123',
          productLabel: 'Product 123',
          lineAmount: 1000,
        },
      ];

      (debStore.get as jest.Mock).mockReturnValue(signal(details));

      expect(facade.getDetails()()).toBe(details);
    });
  });

  describe('getEuNomenclatures', () => {
    it('should return the eu nomenclatures from the store', () => {
      const euNomenclatures = ['EU123', 'EU456', 'EU789'];

      (debStore.get as jest.Mock).mockReturnValue(signal(euNomenclatures));

      expect(facade.getEuNomenclatures()()).toBe(euNomenclatures);
    });
  });

  describe('getStatisticalProceduresForCurrentCompany', () => {
    it('should return the statistical procedures from the store', () => {
      const statisticalProcedures = ['SPC1', 'SPC2', 'SPC3'];

      (debStore.get as jest.Mock).mockReturnValue(
        signal(statisticalProcedures)
      );

      expect(facade.getStatisticalProceduresForCurrentCompany()()).toBe(
        statisticalProcedures
      );
    });
  });

  describe('getDestinationCountryCodesForCurrentCompanyAndDeclarationMonth', () => {
    it('should return the destination country codes from the store', () => {
      const destinationCountryCodes = ['FR', 'DE', 'US'];

      (debStore.get as jest.Mock).mockReturnValue(
        signal(destinationCountryCodes)
      );

      expect(
        facade.getDestinationCountryCodesForCurrentCompanyAndDeclarationMonth()()
      ).toBe(destinationCountryCodes);
    });
  });

  describe('getClosure', () => {
    it('should return the closure from the store', () => {
      const closure = {
        file: new Blob(),
        filename: 'test.xml',
      };

      (debStore.get as jest.Mock).mockReturnValue(signal(closure));

      expect(facade.getClosure()()).toBe(closure);
    });
  });

  describe('hasMissingParameters', () => {
    it('should return true if parameters are missing', () => {
      const parameters: DebParameters = {
        declarantName: 'Test Declarant',
        authorizationNumber: 'AUTH123',
        declarationNumber: 5,
        declarationType: 'light',
      };

      expect(facade.hasMissingParameters({ ...parameters })).toBe(false);
    });

    it('should return false if parameters are not missing', () => {
      expect(facade.hasMissingParameters({})).toBe(true);
    });
  });

  describe('acknowledgeUncloseMonth', () => {
    it('should clear the unclose month from the store', () => {
      const clearSpy = jest.spyOn(debStore, 'clear');

      facade.acknowledgeUncloseMonth();

      expect(clearSpy).toHaveBeenCalledWith(DebStoreEnum.UNCLOSE_MONTH);
    });
  });

  describe('companyHasChangedTo', () => {
    it('should clear the store and call the use cases', () => {
      const clearSpy = jest.spyOn(debStore, 'clear');
      const loadParametersSpy = jest.spyOn(facade, 'loadParametersForCompany');
      const loadClosedMonthsSpy = jest.spyOn(
        facade,
        'loadClosedMonthsForCompany'
      );

      // Mock the observables used in companyHasChangedTo
      (parametersUseCase.getFor as jest.Mock).mockReturnValue(of({}));
      (closedMonthsUseCase.for as jest.Mock).mockReturnValue(of([]));

      facade.companyHasChangedTo('company-123');

      expect(clearSpy).toHaveBeenCalledWith(DebStoreEnum.PARAMETERS);
      expect(clearSpy).toHaveBeenCalledWith(DebStoreEnum.CLOSED_MONTHS);
      expect(clearSpy).toHaveBeenCalledWith(DebStoreEnum.DECLARATIONS);
      expect(clearSpy).toHaveBeenCalledWith(DebStoreEnum.DETAILS);

      expect(loadParametersSpy).toHaveBeenCalledWith('company-123');
      expect(loadClosedMonthsSpy).toHaveBeenCalledWith('company-123');
    });
  });
});
