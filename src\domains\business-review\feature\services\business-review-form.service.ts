// Public properties are related to private properties
/* eslint-disable @typescript-eslint/member-ordering */
import { DestroyRef, Injectable, inject } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  startWith,
  switchMap,
  tap,
  withLatestFrom,
} from 'rxjs';
import {
  CalendarYear,
  BusinessReviewParameters,
  PeriodicityKind,
} from '@gc/business-review/models';
import {
  PeriodicityHelper,
  periodicityValidator,
} from '@gc/business-review/utils';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { Warehouse } from '@gc/warehouse/models';
import { BusinessReviewService } from '@gc/business-review/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

const CALENDAR_YEAR_OPTIONS_COUNT = 5;
const DATE_RANGES_COUNT_FOR_STIMULSOFT = 5;

@Injectable({
  providedIn: 'root',
})
export class BusinessReviewFormService {
  private readonly _fb = inject(FormBuilder);
  private readonly _businessReviewService = inject(BusinessReviewService);
  private readonly _destroyRef = inject(DestroyRef);

  /**
   * @description List of n last years for selector options
   */
  public calendarYearsOptions: CalendarYear[] =
    PeriodicityHelper.getCalendarYearsFromDate(
      CALENDAR_YEAR_OPTIONS_COUNT,
      new Date()
    );

  public businessReviewFG = this._fb.group({
    companies: this._fb.nonNullable.control<string[]>([], Validators.required),

    periodicity: this._fb.group(
      {
        periodicityKind: this._fb.nonNullable.control('calendarYear'),

        calendarYearFC: this._fb.nonNullable.control<CalendarYear>(
          this.calendarYearsOptions[0]
        ),

        fiscalYearFC: this._fb.control<FiscalYearDateRange | null>(null),
      },
      { validators: periodicityValidator() }
    ),

    warehouses: this._fb.nonNullable.control<string[]>([]),

    vatRate: this._fb.control<number | null>(null, Validators.required),
  });

  public get companiesFC(): FormControl<string[]> {
    return this.businessReviewFG.get('companies') as FormControl<string[]>;
  }

  public get periodicityKindFC(): FormControl<string> {
    return this.businessReviewFG.get(
      'periodicity.periodicityKind'
    ) as FormControl<string>;
  }

  public get periodicityFG(): FormGroup<{
    periodicityKind: FormControl<string>;
    calendarYearFC: FormControl<CalendarYear>;
    fiscalYearFC: FormControl<FiscalYearDateRange | null>;
  }> {
    return this.businessReviewFG.get('periodicity') as FormGroup<{
      periodicityKind: FormControl<string>;
      calendarYearFC: FormControl<CalendarYear>;
      fiscalYearFC: FormControl<FiscalYearDateRange | null>;
    }>;
  }

  public get warehousesFC(): FormControl<string[]> {
    return this.businessReviewFG.get('warehouses') as FormControl<string[]>;
  }

  public get vatRateFC(): FormControl<number | null> {
    return this.businessReviewFG.get('vatRate') as FormControl<number | null>;
  }

  public get periodicityKind(): string {
    return this.periodicityKindFC.value;
  }

  public get calendarYearFC(): FormControl<CalendarYear> {
    return this.periodicityFG.get(
      'calendarYearFC'
    ) as FormControl<CalendarYear>;
  }

  public get fiscalYearFC(): FormControl<FiscalYearDateRange | null> {
    return this.periodicityFG.get(
      'fiscalYearFC'
    ) as FormControl<FiscalYearDateRange | null>;
  }

  public selectedDateRange$: Observable<{
    startDate: Date;
    endDate: Date;
  }> = this.periodicityFG.valueChanges.pipe(
    startWith(this.periodicityFG.value),
    map((value) => this._getSelectedDates(value)),
    filter(
      (dateRange): dateRange is { startDate: Date; endDate: Date } =>
        !!dateRange.startDate && !!dateRange.endDate
    )
  );

  private _fiscalYearsOptions$$ = new BehaviorSubject<FiscalYearDateRange[]>(
    []
  );

  public fiscalYearsOptions$ = this._fiscalYearsOptions$$.asObservable();

  public isLoadingAdvancedSettingsData$$ = new BehaviorSubject<boolean>(false);
  public isLoadingFiscalYears$$ = new BehaviorSubject<boolean>(false);

  public isLoading$: Observable<boolean> = combineLatest([
    this.isLoadingAdvancedSettingsData$$,
    this.isLoadingFiscalYears$$,
  ]).pipe(map((values) => values.some((value) => value)));

  private _formReinitialized$$ = new Subject<void>();

  get formReinitialized$(): Observable<void> {
    return this._formReinitialized$$.asObservable();
  }

  init(): void {
    this._getDataOnCompanyChange();
    this._setFiscalYears();
  }

  getBusinessReviewParameters(): BusinessReviewParameters {
    return {
      companyIds: this.companiesFC.value,
      dates: this._getDatesFromSelection(),
      selectedDates: this._getSelectedDates(this.periodicityFG.value),
      warehouseIds: this.warehousesFC.value,
      vatRate: this.vatRateFC.value,
      isFiscalYear:
        this.periodicityKindFC.value === PeriodicityKind.FISCAL_YEAR,
    } as BusinessReviewParameters;
  }

  private _getSelectedDates(
    periodicityFGValue: Partial<{
      periodicityKind: string;
      calendarYearFC: CalendarYear;
      fiscalYearFC: FiscalYearDateRange | null;
    }>
  ): {
    startDate: Date | undefined;
    endDate: Date | undefined;
  } {
    return {
      startDate:
        periodicityFGValue.periodicityKind === PeriodicityKind.CALENDAR_YEAR
          ? periodicityFGValue.calendarYearFC?.startDate
          : periodicityFGValue.fiscalYearFC?.dateFrom,
      endDate:
        periodicityFGValue.periodicityKind === PeriodicityKind.CALENDAR_YEAR
          ? periodicityFGValue.calendarYearFC?.endDate
          : periodicityFGValue.fiscalYearFC?.dateTo,
    };
  }

  private _getDataOnCompanyChange(): void {
    this.companiesFC.valueChanges
      .pipe(
        switchMap((companyIds) => {
          this.isLoadingAdvancedSettingsData$$.next(true);
          return this._businessReviewService
            .getData(companyIds)
            .pipe(
              finalize(() => this.isLoadingAdvancedSettingsData$$.next(false))
            );
        }),
        tap(({ vatRates, warehouses }) => {
          this._reinitParameters(vatRates, warehouses);
        }),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  private _reinitParameters(vatRates: number[], warehouses: Warehouse[]): void {
    if (
      this.periodicityFG.dirty ||
      this.vatRateFC.dirty ||
      this.warehousesFC.dirty
    ) {
      this._formReinitialized$$.next();
    }

    this._setVatRateFCValue(vatRates);
    this._setWarehousesFCValue(warehouses);
    this.periodicityFG.reset();
  }

  private _getDatesFromSelection(): string[] {
    return this.periodicityKindFC.value === PeriodicityKind.CALENDAR_YEAR
      ? PeriodicityHelper.getDatesRangesFromCalendarYear(
          this.calendarYearFC.value,
          DATE_RANGES_COUNT_FOR_STIMULSOFT
        )
      : PeriodicityHelper.getDatesRangesFromFiscalYear(
          this.fiscalYearFC.value,
          this._fiscalYearsOptions$$.value,
          DATE_RANGES_COUNT_FOR_STIMULSOFT
        );
  }

  /**
   * Call the API to retrieve the fiscal years shared by the selected companies, and set the fiscal year options and default selection.
   * Fiscal years are requested when the periodicity kind is set to 'fiscal years' and the companies have changed.
   */
  private _setFiscalYears(): void {
    this.periodicityKindFC.valueChanges
      .pipe(
        filter(
          (periodicityKind) => periodicityKind === PeriodicityKind.FISCAL_YEAR
        ),
        withLatestFrom(this.companiesFC.valueChanges),
        distinctUntilChanged(
          (
            [_prevPeriodicityKind, prevCompaniesIds],
            [_currPeriodicityKind, currCompaniesIds]
          ) => prevCompaniesIds === currCompaniesIds
        ),
        switchMap(([_periodicityKind, companyIds]) =>
          this._getFiscalYearsSharedByCompanies(companyIds)
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  private _getFiscalYearsSharedByCompanies(
    companyIds: string[]
  ): Observable<FiscalYearDateRange[]> {
    this.isLoadingFiscalYears$$.next(true);
    return this._businessReviewService.getFiscalYears(companyIds).pipe(
      tap((fiscalYears: FiscalYearDateRange[]) => {
        this._fiscalYearsOptions$$.next(fiscalYears);
        this.fiscalYearFC.setValue(fiscalYears[0] ?? null);
      }),
      finalize(() => this.isLoadingFiscalYears$$.next(false))
    );
  }

  private _setVatRateFCValue(vatRates: number[]): void {
    const DEFAULT_SELECTION_RATE = 20;
    let defaultValue: number | null = null;
    if (vatRates.includes(DEFAULT_SELECTION_RATE)) {
      defaultValue = DEFAULT_SELECTION_RATE;
    } else if (vatRates.length > 0) {
      defaultValue = Math.max(...vatRates);
    }
    this.vatRateFC.setValue(defaultValue);
    this.vatRateFC.markAsPristine();
  }

  private _setWarehousesFCValue(warehouses: Warehouse[]): void {
    const visibleOnly: Warehouse[] = warehouses.filter(
      (w) => w.visible && !w.unusable
    );
    this.warehousesFC.setValue(visibleOnly.map((w) => w.id));
    this.warehousesFC.markAsPristine();
  }
}
