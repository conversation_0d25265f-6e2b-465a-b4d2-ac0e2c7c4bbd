import { Injectable, inject } from '@angular/core';
import { selectCurrentLetterHasUnsavedChanges } from '@gc/dunning/data-access';
import { DialogService } from '@gc/shared/ui';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import { Observable, of, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CanDeactivateDunningParametersGuard {
  private readonly _store = inject(Store);
  private readonly _translocoService = inject(TranslocoService);
  private readonly _dialogService = inject(DialogService);

  private readonly _currentLetterHasUnsavedChanges$: Observable<
    boolean | undefined
  > = this._store.select(selectCurrentLetterHasUnsavedChanges);

  canDeactivate(): Observable<boolean> {
    return this._currentLetterHasUnsavedChanges$.pipe(
      switchMap((haveUnsavedChanges: boolean | undefined) => {
        if (haveUnsavedChanges) {
          return this._translocoService
            .selectTranslate(
              'default-messages.confirm-loss-unsaved-changes',
              {},
              'sharedDialog'
            )
            .pipe(
              switchMap((confirmMessage: string) =>
                this._dialogService.confirm(confirmMessage)
              )
            );
        }
        return of(true);
      })
    );
  }
}
