import { waitForAsync } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import {
  monthlyTreatmentActions,
  MonthlyTreatmentService,
} from '@gc/accounting/data-access';
import { SalesDetailsTreatmentIdRetrieverCardComponent } from '@gc/accounting/feature';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';

describe('SalesDetailsTreatmentIdRetrieverCardComponent', () => {
  let spectator: Spectator<SalesDetailsTreatmentIdRetrieverCardComponent>;
  let component: SalesDetailsTreatmentIdRetrieverCardComponent;
  let store: Store;

  let dispatchSpy: jest.SpyInstance<void>;

  const createComponent = createComponentFactory({
    component: SalesDetailsTreatmentIdRetrieverCardComponent,
    mocks: [TranslocoService, MonthlyTreatmentService, Store],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
    store = spectator.inject(Store);

    dispatchSpy = jest.spyOn(store, 'dispatch');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    let selectSalesDetailsCurrentFilterSpy: jest.SpyInstance<
      Observable<boolean>
    >;

    let handleStoreUpdateWhenValueChangeSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      selectSalesDetailsCurrentFilterSpy = jest.spyOn(
        store,
        'select'
      ) as jest.SpyInstance<Observable<boolean>>;
      selectSalesDetailsCurrentFilterSpy.mockReturnValue(of(true));

      handleStoreUpdateWhenValueChangeSpy = jest
        .spyOn(component, 'handleStoreUpdateWhenValueChange')
        .mockImplementation(() => {});
    });

    describe('given a state where selectSalesDetailsDefaultFilter return SalesDetailsOrderByEnum.BusinessType value', () => {
      beforeEach(() => {
        selectSalesDetailsCurrentFilterSpy.mockReturnValue(of(true));
      });
      it('should call handleStoreUpdateWhenValueChange method and initialize detailsFilterFC FormControl to true', waitForAsync(() => {
        component.ngOnInit();

        expect(component.detailsFilterFC.value).toBe(true);
        expect(handleStoreUpdateWhenValueChangeSpy).toHaveBeenCalled();
      }));
    });

    describe('given a state where selectSalesDetailsDefaultFilter return SalesDetailsOrderByEnum.Invoices value', () => {
      beforeEach(() => {
        selectSalesDetailsCurrentFilterSpy.mockReturnValue(of(false));
      });
      it('should call handleStoreUpdateWhenValueChange method and initialize detailsFilterFC FormControl to false', waitForAsync(() => {
        component.ngOnInit();

        expect(component.detailsFilterFC.value).toBe(false);
        expect(handleStoreUpdateWhenValueChangeSpy).toHaveBeenCalled();
      }));
    });
  });

  describe('handleStoreUpdateWhenValueChange', () => {
    describe('given a state with detailsFilterFC set', () => {
      const fb = new FormBuilder();

      const detailsFilterFC = fb.control<boolean>(true, {
        nonNullable: true,
      });

      it('should dispatch changeSalesDetailsCurrentFilter with FormControl value as payload', () => {
        component.detailsFilterFC = detailsFilterFC;

        component.handleStoreUpdateWhenValueChange();

        component.detailsFilterFC.setValue(false);
        expect(dispatchSpy).toHaveBeenCalledWith(
          monthlyTreatmentActions.changeSalesDetailsCurrentFilter({
            filter: false,
          })
        );
      });
    });
  });
});
