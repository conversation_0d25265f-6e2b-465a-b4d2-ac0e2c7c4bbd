<ng-container *transloco="let t">
  <div mat-dialog-title class="title-container">
    <mat-icon [color]="'primary'"> visibility </mat-icon>
    <span>
      {{ t('sales.commissions-tab.payout-table.commission-details.title') }}
    </span>
  </div>
  <mat-dialog-content class="content-container">
    @if (currency$ | ngrxPush; as currency) {
      @if (!commissionDetails().isLoading && commissionDetails().data) {
        <mat-card
          *transloco="
            let t;
            read: 'sales.commissions-tab.payout-table.commission-details'
          ">
          <mat-card-content>
            <table
              mat-table
              [dataSource]="commissionDetails().data!"
              class="gc-table-cell-center">
              <ng-container matColumnDef="position">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let rowIndex = index">
                  {{ rowIndex + 1 }}
                </td>
              </ng-container>
              <ng-container matColumnDef="productLabel">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.productLabel') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{ commissionDetails['productLabel'] }}
                </td>
              </ng-container>
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.quantity') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{ commissionDetails['quantity'] }}
                </td>
              </ng-container>
              <ng-container matColumnDef="amountWithoutTax">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.amountWithoutTax') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{
                    commissionDetails['amountWithTax']
                      | amountWithCurrency: currency
                  }}
                </td>
              </ng-container>
              <ng-container matColumnDef="amountWithTax">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.amountWithTax') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{
                    commissionDetails['amountWithTax']
                      | amountWithCurrency: currency
                  }}
                </td>
              </ng-container>
              <ng-container matColumnDef="commissionRate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.commissionRate') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{ commissionDetails['commissionRate'] }} %
                </td>
              </ng-container>
              <ng-container matColumnDef="commissionAmount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  {{ t('table-labels.commissionAmount') }}
                </th>
                <td
                  mat-cell
                  *matCellDef="let commissionDetails; let rowIndex = index">
                  {{ commissionDetails['commissionAmount'] }} €
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      } @else if (!commissionDetails().isLoading && !commissionDetails().data) {
        <p class="user-feedback-container">
          {{
            t('sales.commissions-tab.payout-table.commission-details.no-data')
          }}
        </p>
      } @else if (
        commissionDetails().isLoading ||
        (defaultCurrencyLoadingStatus$ | ngrxPush) === 'IN_PROGRESS'
      ) {
        <div class="user-feedback-container">
          <gc-loader [label]="t('sales.shared.is-loading')" />
        </div>
      }
    }
  </mat-dialog-content>
  <div *transloco="let t" class="action-button-container">
    <button
      mat-stroked-button
      color="primary"
      type="button"
      cdkFocusInitial
      mat-dialog-close>
      {{ t('sharedAction.close') }}
    </button>
  </div>
</ng-container>
