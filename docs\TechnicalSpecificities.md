[[_TOC_]]

# Project technical specificities

This project is based on Angular framework. We update the project on regular basis and release a new version every few months.
Nevertheless, we edited the initial settings for our own usage. Following you will find an exhaustive modifications list compared to an Angular project initial configuration.

> 💡 Frequently check if these special settings are still relevant, after each Angular and @isagri-ng updates for example.

## 👍 Still relevent

### "Outils" team libs

#### Context

As we are using some of the "Outils" team libs, we have to update our `tsconfig.json`, `package.json` and `angular.json` files.

#### Impacts

1. We needed to declare the `crypto-js` in the `path` of our `tsconfig.json` file:

```json
// tsconfig.json
{
    "paths": {
        "crypto": ["node_modules/crypto-js"]
    }
}
```

**⚠️ What if we didn't ?** Warning on build and in the browser console.

> 📖 see [Outils - Installation de l'authentification](https://isagri-ng-doc.groupeisagri.com/guides/parametrage_du_contexte_applicatif/authentification/installation.html)

3. We also had to add `allowedCommonJsDependencies` in our `angular.json` file.

```json
// angular.json
{
    "projects": {
        "gc_webapp": {
            "architect": {
                "build": {
                    "allowedCommonJsDependencies": [
                        "file-saver",
                        "crypto-js",
                        "flat",
                        "angular2-uuid",
                        "quill",
                        "@messageformat/core"
                    ]
                }
            }
        }
    }
}
```

**⚠️ What if we didn't ?** Warnings on build and in the browser console.

> 📖 see [Outils - Migration d'une application Angular vers la version 14](https://isagri-ng-doc.groupeisagri.com/guides/versions/migration_angular_14_16112022.html?q=allowedCommonJsDependencies&tabs=tabiddep-delete#import-des-styles-outils-et-des-common-js)

### Jest instead of Jasmine/Karma

#### Context

We chose to remove Jasmine and Karma, but use Jest instead.

#### Impact

We had to update the `tsconfig.json` file to allow the `jest.config.ts` to import and use the `tsconfig.json`:

```json
// tsconfig.json
{
    "compilerOptions": {
        "resolveJsonModule": true
    }
}
```

**🛑 What if we didn't ?** Error when running tests.

> 📖 see [Resolve JSON Module](https://www.typescriptlang.org/tsconfig#resolveJsonModule)

## 👎 Not relevent anymore (maybe ?)

Because the "Outils" team documentation is not always up to date and not exhaustive either, some projects settings might be out of date/useless.

### Language/i18n/translation lib from the "Outils" team

#### Context

As we are using the language/i18n/translation lib of the "Outils" team, we have to update our `angular.json` file.

#### Impact

We had to update the `tsconfig.json` file with 2 more rules.

```json
// tsconfig.json
{
    "compilerOptions": {
        "resolveJsonModule": true
    }
}
```

**🛑 What if we didn't ?** The project wouldn't build.

> 📖 see [Outils - Configuration pour l'internationalisation des applications](https://isagri-ng-doc.groupeisagri.com/guides/parametrage_du_contexte_applicatif/internationalisation/config_internationalisation_20240130.html)

> 📖 see [Resolve JSON Module](https://www.typescriptlang.org/tsconfig#resolveJsonModule)
