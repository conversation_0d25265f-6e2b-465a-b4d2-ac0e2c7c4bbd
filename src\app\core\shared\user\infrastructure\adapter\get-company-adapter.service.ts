import { inject, Injectable } from '@angular/core';
import { GetCompanyPort } from '@gc/core/shared/user/domains/ports';
import { UserStoreService } from '@gc/user/data-access';
import { filter, map } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class GetCompanyAdapter implements GetCompanyPort {
  private readonly userStoreService = inject(UserStoreService);

  getCompany() {
    return this.userStoreService.user$.pipe(
      filter(Boolean),
      map((user) => user.defaultCompanyId)
    );
  }
}
