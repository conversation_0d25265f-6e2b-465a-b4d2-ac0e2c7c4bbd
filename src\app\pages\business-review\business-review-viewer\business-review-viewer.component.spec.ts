import { MockComponents, MockPipes } from 'ng-mocks';
import {
  StimulsoftNavigationService,
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import { PushPipe } from '@ngrx/component';
import { BusinessReviewStimulsoftNavigationService } from './services/business-review-stimulsoft-navigation.service';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { of } from 'rxjs';
import { GuidHelper } from '@isagri-ng/core';
import { waitForAsync } from '@angular/core/testing';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
} from '@gc/shared/stimulsoft/models';
import { BusinessReviewViewerComponent } from './business-review-viewer.component';
import { BusinessReviewSupplementaryStimulsoftProperties } from './models/business-review-supplementary-stimulsoft-properties.model';

describe('BusinessReviewViewerComponent', () => {
  let spectator: Spectator<BusinessReviewViewerComponent>;

  let stimulsoftNavigationService: StimulsoftNavigationService;
  let businessReviewStimulsoftNavigationService: BusinessReviewStimulsoftNavigationService;
  let stimulsoftPropertiesService: StimulsoftPropertiesService;

  const createComponent = createComponentFactory({
    component: BusinessReviewViewerComponent,
    declarations: [
      MockComponents(StimulsoftViewerContainerComponent),
      MockPipes(PushPipe),
    ],
    mocks: [
      StimulsoftNavigationService,
      BusinessReviewStimulsoftNavigationService,
      StimulsoftPropertiesService,
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    stimulsoftNavigationService = spectator.inject(StimulsoftNavigationService);
    businessReviewStimulsoftNavigationService = spectator.inject(
      BusinessReviewStimulsoftNavigationService
    );
    stimulsoftPropertiesService = spectator.inject(StimulsoftPropertiesService);
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('stimulsoftProperties$', () => {
    describe('Given a state where the stimulsoftNavigationService returns values', () => {
      const commonStimulsoftProperties: CommonStimulsoftProperties = {
        reportId: 'REPORT_ID',
        reportFamily: 'REPORT_FAMILY',
      };
      beforeEach(() => {
        jest
          .spyOn(
            stimulsoftNavigationService,
            'getCommonStimulsoftPropertiesFromQueryParams$'
          )
          .mockReturnValue(
            of({
              reportId: commonStimulsoftProperties.reportId,
              reportFamily: commonStimulsoftProperties.reportFamily,
            })
          );
      });

      describe('and given a state where the businessReviewStimulsoftNavigationService returns values', () => {
        const selectedCompanies = [GuidHelper.newGuid(), GuidHelper.newGuid()];
        const selectedWarehouses = [GuidHelper.newGuid(), GuidHelper.newGuid()];
        const selectedVatRate = 0.2;

        const selectedDates = [
          '2024-01-01',
          '2024-12-31',
          '2023-01-01',
          '2023-12-31',
          '2022-01-01',
          '2022-12-31',
          '2021-01-01',
          '2021-12-31',
          '2020-01-01',
          '2020-12-31',
        ];

        beforeEach(() => {
          jest
            .spyOn(
              businessReviewStimulsoftNavigationService,
              'getBusinessReviewStimulsoftPropertiesFromQueryParams$'
            )
            .mockReturnValue(
              of({
                companyIds: selectedCompanies,
                warehouseIds: selectedWarehouses,
                vatRate: selectedVatRate,
                dateRange1startDate: selectedDates[0],
                dateRange1endDate: selectedDates[1],
                dateRange2startDate: selectedDates[2],
                dateRange2endDate: selectedDates[3],
                dateRange3startDate: selectedDates[4],
                dateRange3endDate: selectedDates[5],
                dateRange4startDate: selectedDates[6],
                dateRange4endDate: selectedDates[7],
                dateRange5startDate: selectedDates[8],
                dateRange5endDate: selectedDates[9],
              } as BusinessReviewSupplementaryStimulsoftProperties)
            );
        });

        describe('and given a state where the stimulsoftPropertiesService returns the full properties', () => {
          const coreStimulsoftProperties: CoreStimulsoftProperties = {
            authToken: 'Bearer 123',
            culture: 'fr-FR',
          };

          beforeEach(() => {
            jest
              .spyOn(stimulsoftPropertiesService, 'createStimulsoftProperties$')
              .mockReturnValue(
                of({
                  authToken: coreStimulsoftProperties.authToken,
                  culture: coreStimulsoftProperties.culture,
                  reportId: commonStimulsoftProperties.reportId,
                  reportFamily: commonStimulsoftProperties.reportFamily,
                  companyIds: selectedCompanies,
                  warehouseIds: selectedWarehouses,
                  vatRate: selectedVatRate,
                  dateRange1startDate: selectedDates[0],
                  dateRange1endDate: selectedDates[1],
                  dateRange2startDate: selectedDates[2],
                  dateRange2endDate: selectedDates[3],
                  dateRange3startDate: selectedDates[4],
                  dateRange3endDate: selectedDates[5],
                  dateRange4startDate: selectedDates[6],
                  dateRange4endDate: selectedDates[7],
                  dateRange5startDate: selectedDates[8],
                  dateRange5endDate: selectedDates[9],
                })
              );
          });

          it('should emit the stimulsoft properties once all involved services emits', waitForAsync(() => {
            const expectedProperties = {
              authToken: coreStimulsoftProperties.authToken,
              culture: coreStimulsoftProperties.culture,
              reportId: 'REPORT_ID',
              reportFamily: 'REPORT_FAMILY',
              companyIds: selectedCompanies,
              warehouseIds: selectedWarehouses,
              vatRate: selectedVatRate,
              dateRange1startDate: selectedDates[0],
              dateRange1endDate: selectedDates[1],
              dateRange2startDate: selectedDates[2],
              dateRange2endDate: selectedDates[3],
              dateRange3startDate: selectedDates[4],
              dateRange3endDate: selectedDates[5],
              dateRange4startDate: selectedDates[6],
              dateRange4endDate: selectedDates[7],
              dateRange5startDate: selectedDates[8],
              dateRange5endDate: selectedDates[9],
            };

            spectator.detectChanges();

            spectator.component.stimulsoftProperties$.subscribe((value) => {
              expect(value).toStrictEqual(expectedProperties);
            });
          }));
        });
      });
    });
  });
});
