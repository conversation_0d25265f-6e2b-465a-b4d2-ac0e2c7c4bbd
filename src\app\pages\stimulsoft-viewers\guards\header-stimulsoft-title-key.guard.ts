import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { HeaderTitleService } from '@gc/core/navigation/feature';
import { URL_PATHS } from '@gc/core/navigation/models';
import { StimulsoftQueryParamsKeys } from '@gc/shared/models';
import { ReportFamilies } from '@gc/shared/stimulsoft/models';
import { STIMULSOFT_REPORT_FAMILY_TO_HEADER_TRANSLATION_KEY_MAP } from '../constants/stimulsoft-viewer-translation-keys-from-report-family.constant';

export const setStimulsoftHeaderTitleGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  _state: RouterStateSnapshot,
  headerTitleService: HeaderTitleService = inject(HeaderTitleService)
) => {
  const router = inject(Router);
  const reportFamily: ReportFamilies = route.queryParams[
    StimulsoftQueryParamsKeys.REPORT_FAMILY
  ] as ReportFamilies;

  const headerTranslationKey =
    STIMULSOFT_REPORT_FAMILY_TO_HEADER_TRANSLATION_KEY_MAP.get(reportFamily);

  if (!headerTranslationKey) {
    router.navigate([URL_PATHS.invalidConfig]);
    throw new Error(
      'setStimulsoftHeaderTitleGuard: headerTranslationKey is undefined'
    );
  }

  headerTitleService.updateCurrentTitle(headerTranslationKey);

  return true;
};
