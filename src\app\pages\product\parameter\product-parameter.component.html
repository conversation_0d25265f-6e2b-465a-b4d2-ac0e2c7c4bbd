<gc-main-container>
  <div
    data-testid="product-container"
    class="container"
    *transloco="let t; read: 'productParameter'">
    <nav mat-tab-nav-bar [tabPanel]="tabPanel">
      @for (link of navLinks; track link) {
        <a
          mat-tab-link
          [routerLink]="link.link"
          routerLinkActive
          #rla="routerLinkActive"
          [active]="rla.isActive">
          {{ t(link.label) }}
        </a>
      }
    </nav>
    <div class="content">
      <mat-tab-nav-panel #tabPanel>
        <router-outlet />
      </mat-tab-nav-panel>
    </div>
  </div>
</gc-main-container>
