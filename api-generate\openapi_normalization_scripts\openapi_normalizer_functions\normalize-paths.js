/**
 *  This method take as input an openapi object corresponding to a openapi file from stoplight ; for instance : IS-GC_DEB_V1.json
 *  This method create the full path based on openapi file name as follow :
 *
 *  For this file : IS-GC_DEB_V1.json
 *  With this content :
 *    "paths": {
 *       "/parameters": {
 *    ...
 *  It create :
 *    "paths": {
 *       "deb/v1/parameters": {
 *
 * @param {*} openapiObj deserialized object from .json openapi file
 * @return {*} openapiObj with paths updated
 */
function normalizePaths(openapiObj, domain, version) {
    if (openapiObj.paths) {
        const normalizedPaths = {};

        for (const pathKey in openapiObj.paths) {
            let normalizedPathName = `/${domain.toLowerCase()}/${version.toLowerCase()}${pathKey}`;
            normalizedPaths[normalizedPathName] = openapiObj.paths[pathKey];
        }
        openapiObj.paths = normalizedPaths;
    }

    return openapiObj;
}

exports.normalizePaths = normalizePaths;
