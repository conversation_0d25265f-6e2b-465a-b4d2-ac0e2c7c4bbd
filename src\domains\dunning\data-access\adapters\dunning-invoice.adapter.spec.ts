import {
  <PERSON>ing<PERSON><PERSON>,
  Dunning<PERSON>ue<PERSON>ate<PERSON>pi,
  DunningLevelApi,
} from '@gc/shared/api/data-access';
import { DunningInvoiceAdapter } from './dunning-invoice.adapter';
import { DateAdapter } from '@gc/shared/utils';
import { anyDunningDueDateApi } from '../fixtures/dunning-due-date-api.fixtures';
import { anyDunningApi } from '../fixtures/dunning-api.fixtures';
import {
  anyDunningInvoice,
  DunningLevel,
  DunningInvoice,
} from '@gc/dunning/models';

describe('DunningInvoiceAdapter', () => {
  describe('fromDueDatesApi method', () => {
    describe('given a list of DunningDueDatesApi', () => {
      const commonDateForAll = '1970-11-10';
      const invoice1Id = '9999-6666-9999-8888';
      const invoice1Number = '158568566';

      const invoice2Id = '1111-2222-5455-8888';
      const invoice2Number = '99995885555';
      const dueDateLevel = DunningLevel.LEVEL_1;

      const fakeDunningDueDateApi: DunningDueDateApi = {
        ...anyDunningDueDateApi,
        invoiceDate: commonDateForAll,
        invoiceId: invoice1Id,
        invoiceNumber: invoice1Number,
        dunningLevel: DunningLevelApi.Level1,
      };

      const fakeDunningDueDateWithSameInvoiceInfoApi: DunningDueDateApi = {
        ...fakeDunningDueDateApi,
        id: '7896-5555-9879-8888',
      };

      const fakeDunningDueDateWithOtherInvoiceInfoApi: DunningDueDateApi = {
        ...fakeDunningDueDateApi,
        id: '3333-5555-9879-9999',
        invoiceDate: commonDateForAll,
        invoiceId: invoice2Id,
        invoiceNumber: invoice2Number,
      };

      const fakeDunningDueDates: DunningDueDateApi[] = [
        fakeDunningDueDateApi,
        fakeDunningDueDateWithSameInvoiceInfoApi,
        fakeDunningDueDateWithOtherInvoiceInfoApi,
      ];

      it('should return a list of DunningInvoice', () => {
        const expectedDate = new Date(commonDateForAll);

        jest
          .spyOn(DateAdapter, 'dateFromStringAPI')
          .mockReturnValue(expectedDate);

        const expectedDunningInvoices: DunningInvoice[] = [
          {
            dueDatesIds: [
              fakeDunningDueDateApi.id,
              fakeDunningDueDateWithSameInvoiceInfoApi.id,
            ],
            id: invoice1Id,
            totalAmountDue: 240,
            number: invoice1Number,
            date: expectedDate,
            amount: fakeDunningDueDateApi.invoiceAmount,
            level: dueDateLevel,
            selectedLevel: undefined,
          },
          {
            dueDatesIds: [fakeDunningDueDateWithOtherInvoiceInfoApi.id],
            id: invoice2Id,
            totalAmountDue: 120,
            number: invoice2Number,
            date: expectedDate,
            amount: fakeDunningDueDateApi.invoiceAmount,
            level: dueDateLevel,
            selectedLevel: undefined,
          },
        ];

        expect(
          DunningInvoiceAdapter.fromDueDatesApi(fakeDunningDueDates)
        ).toStrictEqual(expectedDunningInvoices);
      });
    });
  });

  describe('fromDunningsApi method', () => {
    describe('given a list of 2 DunningApi with a list of DunningDueDateApi', () => {
      const invoice1Id = '8889-7778-5563';
      const invoice2Id = '8889-7778-9999';
      const invoice3Id = '8889-7778-9999';

      const dunningDueDateApi1: DunningDueDateApi = {
        ...anyDunningDueDateApi,
        invoiceId: invoice1Id,
      };

      const dunningDueDateApi2: DunningDueDateApi = {
        ...anyDunningDueDateApi,
        invoiceId: invoice1Id,
      };

      const dunningDueDateApi3: DunningDueDateApi = {
        ...anyDunningDueDateApi,
        invoiceId: invoice2Id,
      };

      const dunningDueDateApi4: DunningDueDateApi = {
        ...anyDunningDueDateApi,
        invoiceId: invoice3Id,
      };

      const dunningApi1: DunningApi = {
        ...anyDunningApi,
        customerId: '7899-9999-2222',
        dueDates: [dunningDueDateApi1, dunningDueDateApi2, dunningDueDateApi3],
      };

      const dunningApi2: DunningApi = {
        ...anyDunningApi,
        customerId: '1899-6666-2222',
        dueDates: [dunningDueDateApi4],
      };

      let fromDueDatesApiSpy: jest.SpyInstance<DunningInvoice[]>;

      beforeEach(() => {
        fromDueDatesApiSpy = jest.spyOn(
          DunningInvoiceAdapter,
          'fromDueDatesApi'
        );
        fromDueDatesApiSpy.mockReturnValue([anyDunningInvoice]);
      });

      it('should have call fromDueDatesApi method for each DunningApi', () => {
        DunningInvoiceAdapter.fromDunningsApi([dunningApi1, dunningApi2]);
        expect(fromDueDatesApiSpy).toHaveBeenNthCalledWith(
          1,
          dunningApi1.dueDates
        );
        expect(fromDueDatesApiSpy).toHaveBeenNthCalledWith(
          2,
          dunningApi2.dueDates
        );
      });

      describe('and a state where fromDueDatesApi return 2 DunningInvoice in the first call and 1 in the second call', () => {
        const dunningInvoice1: DunningInvoice = {
          ...anyDunningInvoice,
          id: '44444-5555-8558-4444',
        };
        const dunningInvoice2: DunningInvoice = {
          ...anyDunningInvoice,
          id: '56874-5555-8558-4444',
        };
        const dunningInvoice3: DunningInvoice = {
          ...anyDunningInvoice,
          id: '11111-5555-8558-4444',
        };

        beforeEach(() => {
          fromDueDatesApiSpy.mockReturnValueOnce([
            dunningInvoice1,
            dunningInvoice2,
          ]);
          fromDueDatesApiSpy.mockReturnValueOnce([dunningInvoice3]);
        });

        it('should return the 3 DunningInvoice', () => {
          expect(
            DunningInvoiceAdapter.fromDunningsApi([dunningApi1, dunningApi2])
          ).toStrictEqual([dunningInvoice1, dunningInvoice2, dunningInvoice3]);
        });
      });
    });
  });
});
