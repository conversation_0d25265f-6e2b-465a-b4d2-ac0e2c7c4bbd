import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { Component } from '@angular/core';
import { NoNavigationContainerComponent } from '@gc/core/navigation/feature';

@Component({
  selector: 'gc-login',
  standalone: true,
  imports: [TranslocoModule, NoNavigationContainerComponent],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'login',
      multi: true,
    },
  ],
  template: `
    <ng-container *transloco="let t">
      <gc-no-navigation-container>
        {{ t('login.page.need-to-login-from-application') }}
      </gc-no-navigation-container>
    </ng-container>
  `,
})
export class LoginComponent {}
