import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DebDeclarationFormContainerComponent } from './deb-declaration-form-container.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebDeclarationFormComponent } from '../deb-declaration-form/deb-declaration-form.component';
import { FormConfig } from '../models/form-config.model';
import { DraftDeclaration } from '@gc/core/deb/domains/models';
import { Currency } from '@gc/shared/models';
import { MatIconModule } from '@angular/material/icon';

describe('DebDeclarationFormContainerComponent', () => {
  let spectator: Spectator<DebDeclarationFormContainerComponent>;
  let component: DebDeclarationFormContainerComponent;
  let dialogRef: MatDialogRef<DebDeclarationFormContainerComponent>;

  const mockCurrency: Currency = {
    code: 'EUR',
    symbol: '€',
    usualPrecision: 2,
  };

  const mockDraftDeclaration: DraftDeclaration = {
    id: 'test-id',
    euNomenclature: 'EU001',
    destinationCountryCode: 'FR',
    invoicedAmount: 1000,
    statisticalProcedureCode: 'CODE1',
    vatNumber: 'VAT123',
    state: 'initial',
    status: 'enabled',
  };

  const mockFormConfig: FormConfig = {
    draftDeclaration: mockDraftDeclaration,
    companyId: 'test-company-id',
    month: new Date('2023-01-01'),
    currency: mockCurrency,
  };

  const createComponent = createComponentFactory({
    component: DebDeclarationFormContainerComponent,
    imports: [MatIconModule],
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(DebDeclarationFormComponent),
    ],
    providers: [
      {
        provide: MAT_DIALOG_DATA,
        useValue: mockFormConfig,
      },
      {
        provide: MatDialogRef,
        useValue: {
          close: jest.fn(),
        },
      },
      {
        provide: TranslocoService,
        useValue: {
          translate: jest.fn().mockReturnValue('Translated Text'),
        },
      },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
    dialogRef = spectator.inject(MatDialogRef);
    spectator.inject(TranslocoService);
  });

  it('should create', () => {
    spectator.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('constructor', () => {
    it('should initialize with the provided form config', () => {
      spectator.detectChanges();
      expect(component.formConfig).toEqual(mockFormConfig);
    });
  });

  describe('UI elements', () => {
    describe('when editing an existing declaration', () => {
      beforeEach(() => {
        component.formConfig = mockFormConfig;
        spectator.detectChanges();
      });

      it('should render the component with edit mode', () => {
        expect(spectator.fixture.nativeElement.textContent).toBeDefined();
      });
    });

    describe('when adding a new declaration', () => {
      beforeEach(() => {
        const addLineConfig: FormConfig = {
          companyId: 'test-company-id',
          month: new Date('2023-01-01'),
          currency: mockCurrency,
          draftDeclaration: undefined,
        };
        component.formConfig = addLineConfig;
        spectator.detectChanges();
      });

      it('should render the component with add mode', () => {
        expect(spectator.fixture.nativeElement.textContent).toBeDefined();
      });
    });
  });

  describe('onEdit', () => {
    it('should close the dialog with the draft declaration when a valid declaration is provided', () => {
      // Arrange
      const updatedDraftDeclaration: DraftDeclaration = {
        ...mockDraftDeclaration,
        euNomenclature: 'EU002',
        invoicedAmount: 2000,
      };

      component.onEdit(updatedDraftDeclaration);

      expect(dialogRef.close).toHaveBeenCalledWith(updatedDraftDeclaration);
    });

    it('should close the dialog with no arguments when null is provided', () => {
      component.onEdit(null);

      expect(dialogRef.close).toHaveBeenCalledWith();
    });
  });

  describe('form interaction', () => {
    it('should handle the editedDraftDeclaration output from the form component', () => {
      spectator.detectChanges();

      const updatedDeclaration: DraftDeclaration = {
        ...mockDraftDeclaration,
        euNomenclature: 'Updated EU Code',
      };

      // Directly call the onEdit method to simulate the form component emitting a value
      component.onEdit(updatedDeclaration);

      expect(dialogRef.close).toHaveBeenCalledWith(updatedDeclaration);
    });

    it('should handle the editedDraftDeclaration output with null value', () => {
      spectator.detectChanges();

      // Directly call the onEdit method to simulate the form component emitting null
      component.onEdit(null);

      expect(dialogRef.close).toHaveBeenCalledWith();
    });
  });
});
