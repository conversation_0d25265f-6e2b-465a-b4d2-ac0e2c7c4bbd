import { Injectable, inject } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';
import { BehaviorSubject, Observable, switchMap } from 'rxjs';

type title = string;

@Injectable({ providedIn: 'root' })
export class HeaderTitleService {
  private readonly _translocoService = inject(TranslocoService);

  title$: Observable<title>;
  private _titleKey$$: BehaviorSubject<title> = new BehaviorSubject('');

  constructor() {
    this.title$ = this._titleKey$$
      .asObservable()
      .pipe(
        switchMap((titleKey: string) =>
          this._translocoService.selectTranslate<title>(titleKey)
        )
      );
  }
  public updateCurrentTitle(titleKey: string): void {
    this._titleKey$$.next(titleKey);
  }
}
