import { Routes } from '@angular/router';
import { setHeaderTitleGuard } from '@gc/core/navigation/feature';
import { Paths, TitleKeyHeader, TitleKeyTab } from '@gc/core/navigation/models';

export const ROUTES: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./business-review/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.BUSINESS_REVIEW,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.BUSINESS_REVIEW)],
  },
  {
    path: Paths.VIEWER,
    loadChildren: () =>
      import('./business-review-viewer/routes').then((m) => m.ROUTES),
    title: TitleKeyTab.BUSINESS_REVIEW_VIEWER,
    canActivate: [setHeaderTitleGuard(TitleKeyHeader.BUSINESS_REVIEW_VIEWER)],
  },
];
