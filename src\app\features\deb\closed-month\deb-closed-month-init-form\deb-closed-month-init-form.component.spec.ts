import { DebClosedMonthInitFormComponent } from './deb-closed-month-init-form.component';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { MockDirectives } from 'ng-mocks';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { FormBuilder } from '@angular/forms';
import { ClosedMonth } from '@gc/core/deb/domains/models';
import {
  DEB_INITIAL_DECLARATION_NUMBER,
  MINIMUM_DECLARATION_NUMBER_VALUE,
} from '@gc/core/deb/application/constants';

describe('DebClosedMonthInitFormComponent', () => {
  let spectator: Spectator<DebClosedMonthInitFormComponent>;
  let component: DebClosedMonthInitFormComponent;

  const createComponent = createComponentFactory({
    component: DebClosedMonthInitFormComponent,
    declarations: [MockDirectives(TranslocoDirective)],
    mocks: [TranslocoService],
    providers: [FormBuilder],
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize form controls with correct values', () => {
      expect(component.declarationNumberFC.value).toBe(
        DEB_INITIAL_DECLARATION_NUMBER
      );
      expect(component.declarationMonthFC.value).toBeNull();
      expect(component.formGroup).toBeDefined();
    });

    it('should set validators on form controls', () => {
      expect(component.declarationNumberFC.hasError('required')).toBeFalsy();

      component.declarationNumberFC.setValue(null as any);
      expect(component.declarationNumberFC.hasError('required')).toBeTruthy();

      component.declarationNumberFC.setValue(
        MINIMUM_DECLARATION_NUMBER_VALUE - 1
      );
      expect(component.declarationNumberFC.hasError('min')).toBeTruthy();

      component.declarationNumberFC.setValue(MINIMUM_DECLARATION_NUMBER_VALUE);
      expect(component.declarationNumberFC.hasError('min')).toBeFalsy();
    });
  });

  describe('onSubmit', () => {
    it('should emit closedMonth with form values', () => {
      const emitSpy = jest.spyOn(component.closedMonth, 'emit');
      const testDate = new Date('2023-01-01');
      const testNumber = 123;

      component.declarationNumberFC.setValue(testNumber);
      component.declarationMonthFC.setValue(testDate);

      component.onSubmit();

      const expectedClosedMonth: ClosedMonth = {
        declarationNumber: testNumber,
        declarationMonth: testDate,
      };

      expect(emitSpy).toHaveBeenCalledWith(expectedClosedMonth);
    });
  });

  describe('cancel', () => {
    it('should emit null', () => {
      const emitSpy = jest.spyOn(component.closedMonth, 'emit');

      component.cancel();

      expect(emitSpy).toHaveBeenCalledWith(null);
    });
  });

  describe('lastSixMonths', () => {
    it('should contain the correct number of months', () => {
      expect(component.lastSixMonths.length).toBe(6);
    });
  });

  describe('minimumDeclarationNumberValue', () => {
    it('should be set to the correct constant value', () => {
      expect(component.minimumDeclarationNumberValue).toBe(
        MINIMUM_DECLARATION_NUMBER_VALUE
      );
    });
  });
});
