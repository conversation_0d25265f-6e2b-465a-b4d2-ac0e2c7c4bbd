import { FormControl, FormGroup } from '@angular/forms';
import { RangeFormGroup } from '@gc/accounting/models';
import { DateRangeErrors } from '@gc/shared/models';
import { ToErrorKeyPipe } from './to-error-key.pipe';

describe('ToErrorKeyPipe', () => {
  let pipe: ToErrorKeyPipe;

  beforeEach(() => {
    pipe = new ToErrorKeyPipe();
  });
  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  [
    {
      startError: undefined,
      endError: DateRangeErrors.DIFFERENT_MONTH_OR_YEAR,
      expectedResult:
        'accounting.monthly-edition-tab.form.error.different-month-or-year',
    },
    {
      startError: undefined,
      endError: DateRangeErrors.START_DATE_AFTER_END_DATE,
      expectedResult:
        'accounting.monthly-edition-tab.form.error.start-date-after-end-date',
    },
    {
      startError: 'matDatepickerParse',
      endError: undefined,
      expectedResult: 'sharedForm.error.date.invalid',
    },
    {
      startError: undefined,
      endError: 'matDatepickerParse',
      expectedResult: 'sharedForm.error.date.invalid',
    },
    {
      startError: 'required',
      endError: undefined,
      expectedResult: 'sharedForm.error.required',
    },
    {
      startError: undefined,
      endError: 'required',
      expectedResult: 'sharedForm.error.required',
    },
  ].forEach(({ startError, endError, expectedResult }) => {
    describe(`given any state`, () => {
      describe(`when pipe is called with a range FormGroup with start date error equal to ${startError} and end date error equal to ${endError}`, () => {
        it(`should return the following translation key ${expectedResult}`, () => {
          const fg = new FormGroup<RangeFormGroup>({
            start: new FormControl(),
            end: new FormControl(),
          });

          if (startError) {
            fg.controls.start.setErrors({ [startError]: true });
          }

          if (endError) {
            fg.controls.end.setErrors({ [endError]: true });
          }

          const result = pipe.transform(fg.value, fg);

          expect(result).toBe(expectedResult);
        });
      });
    });
  });
});
