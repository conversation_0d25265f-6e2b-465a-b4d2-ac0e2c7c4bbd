/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { HttpClient, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  DebClosureParamsRequest,
  GetDebDraftParamRequest,
  GetDebLineDetailsParamsRequest,
  SaveDebParamsRequest,
} from '@gc/core/deb/infrastructure/api/request';
import {
  ClosedDebResponse,
  DebDraftResponse,
  DebLineDetailsResponse,
  DestinationCountriesResponse,
  ParametersResponse,
  StatisticalProcedureResponse,
} from '@gc/core/deb/infrastructure/api/response';
import { UrlBuilder } from '@gc/core/shared/infrastructure';
import { LegacyApiService } from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';
import { map } from 'rxjs/operators';

@Injectable()
export class DebApiService {
  private readonly httpClient = inject(HttpClient);
  private readonly legacyApiService = inject(LegacyApiService);
  private readonly baseUrl = '/deb/v1';

  getLineDetails(params: GetDebLineDetailsParamsRequest) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('deb-line-details')
      .withQueryParam('companyId', params.companyId)
      .withQueryParam(
        'month',
        DateAdapter.dateToStringAPIWithoutDay(params.month)!
      )
      .withQueryParam('invoicesIncluded', params.invoicesIncluded)
      .withQueryParam('deliveryNotesIncluded', params.deliveryNotesIncluded)
      .withQueryParam('destinationCountryCode', params.destinationCountryCode)
      .withQueryParam(
        'statisticalProcedureCode',
        params.statisticalProcedureCode
      )
      .withQueryParam('euNomenclature', params.euNomenclature)
      .withQueryParam('vatNumber', params.vatNumber);

    return this.httpClient.get<DebLineDetailsResponse[]>(urlBuilder.build());
  }

  saveParameters(params: SaveDebParamsRequest) {
    const urlBuilder = UrlBuilder.create(this.baseUrl).withRouteParam(
      'parameters'
    );

    return this.httpClient.put<void>(urlBuilder.build(), params);
  }

  saveClosure(params: DebClosureParamsRequest) {
    const { companyId, declarationMonth, ...body } = params;

    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('closure')
      .withRouteParam(companyId)
      .withRouteParam(DateAdapter.dateToStringAPIWithoutDay(declarationMonth)!);

    // * There is no response body when archiveOnly is true
    return this.httpClient
      .post(urlBuilder.build(), body, {
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/xml',
          'Content-Type': 'application/json',
        },
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<ArrayBuffer>) => {
          const contentDisposition = response.headers.get(
            'Content-Disposition'
          );
          let filename: string | undefined;

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(
              /filename[^;=\\n]*=((['"]).*?\\2|[^;\\n]*)/
            );
            if (filenameMatch && filenameMatch[1]) {
              // Remove quotes if present
              filename = filenameMatch[1].replace(/['"]/g, '');
            }
          }

          return {
            file: response.body as ArrayBuffer,
            filename,
          };
        })
      );
  }

  getParameters(companyId: string) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('parameters')
      .withRouteParam(companyId);

    return this.httpClient.get<ParametersResponse>(urlBuilder.build());
  }

  getDraft(params: GetDebDraftParamRequest) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('deb')
      .withRouteParam('draft')
      .withRouteParam(params.companyId)
      .withRouteParam(DateAdapter.dateToStringAPIWithoutDay(params.month)!)
      .withQueryParam('invoicesIncluded', params.invoicesIncluded)
      .withQueryParam('deliveryNotesIncluded', params.deliveryNotesIncluded);

    return this.httpClient.get<DebDraftResponse>(urlBuilder.build());
  }

  getClosedDebCollection(companyId: string, page: number, pageSize: number) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('deb')
      .withRouteParam(companyId)
      .withQueryParam('page', page)
      .withQueryParam('pageSize', pageSize);

    return this.httpClient.get<ClosedDebResponse>(urlBuilder.build());
  }

  deleteClosure(companyId: string, month: Date) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('closure')
      .withRouteParam(companyId)
      .withRouteParam(DateAdapter.dateToStringAPIWithoutDay(month)!);

    return this.httpClient.delete<void>(urlBuilder.build());
  }

  getEuNomenclatures() {
    return this.legacyApiService
      .euNomenclaturesGetEUNomenclatures()
      .pipe(map((response) => response.euNomenclatures));
  }

  getDestinationCountries(companyId: string, month: Date) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('destination-countries')
      .withRouteParam(companyId)
      .withRouteParam(DateAdapter.dateToStringAPIWithoutDay(month)!);

    return this.httpClient.get<DestinationCountriesResponse[]>(
      urlBuilder.build()
    );
  }

  getStatisticalProcedures(companyId: string) {
    const urlBuilder = UrlBuilder.create(this.baseUrl)
      .withRouteParam('statistical-procedure-codes')
      .withRouteParam(companyId);

    return this.httpClient.get<StatisticalProcedureResponse[]>(
      urlBuilder.build()
    );
  }
}
