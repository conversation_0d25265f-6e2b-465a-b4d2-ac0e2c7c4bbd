@if (companiesOptions) {
  <mat-form-field
    appearance="outline"
    subscriptSizing="dynamic"
    class="company-select-form-field">
    <mat-label class="label"> {{ label }}</mat-label>
    <mat-select
      multiple
      [required]="required"
      id="{{ inputId }}"
      [ngModel]="getValue$() | async"
      (ngModelChange)="onCompaniesChanged($event)">
      <mat-checkbox
        class="mat-option"
        color="primary"
        [indeterminate]="isIndeterminate$() | async"
        (click)="$event.stopPropagation()"
        (change)="toggleSelection($event)"
        [checked]="isChecked$() | async"
        >{{ selectAllLabel }}</mat-checkbox
      >
      @for (company of companiesOptions; track company) {
        <mat-option [value]="company.id">{{ company.code }}</mat-option>
      }
    </mat-select>
    @if (invalid) {
      <mat-error>{{ errorText }}</mat-error>
    }
  </mat-form-field>
}
