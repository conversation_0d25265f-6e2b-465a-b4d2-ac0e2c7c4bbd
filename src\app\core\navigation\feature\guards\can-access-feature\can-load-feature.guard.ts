import { inject } from '@angular/core';
import { CanMatchFn, Router } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { FlagService } from '../../services/flag.service';

export const canLoadFeature = (): CanMatchFn => (route, _) => {
  const flagService = inject(FlagService);
  const router: Router = inject(Router);

  const flag = route.data && route.data['feature'];

  return flagService.canAccessFeature(flag)
    ? true
    : router.parseUrl(`/${URL_PATHS.unavailableModule}`);
};
