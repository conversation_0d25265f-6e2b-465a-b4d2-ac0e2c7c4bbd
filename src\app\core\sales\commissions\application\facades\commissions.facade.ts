import { inject, Injectable } from '@angular/core';
import { handleStoreLoading } from '@gc/core/shared/store/operators';
import { CommissionsStore, CommissionsStoreEnum } from '../store';
import { CommissionsUseCase } from '../use-cases/commissions.use-case';
import { CommissionDetailsUseCase } from '../use-cases/commission-details.use-case';
import { Commission, CommissionsFilters } from '../../domains/models';
import { UpdateCommissionUseCase } from '../use-cases/update-commission.use-case';

@Injectable()
export class CommissionsFacade {
  private readonly store: CommissionsStore = inject(CommissionsStore);
  private readonly loadCommissions = inject(CommissionsUseCase);
  private readonly loadCommissionDetails = inject(CommissionDetailsUseCase);
  private readonly updateCommission = inject(UpdateCommissionUseCase);

  getCommissionsForCurrentFilters() {
    return this.store.get(CommissionsStoreEnum.COMMISSIONS);
  }

  loadCommissionsForFilter(filters: CommissionsFilters) {
    this.loadCommissions
      .for(filters)
      .pipe(handleStoreLoading(this.store, CommissionsStoreEnum.COMMISSIONS))
      .subscribe();
  }

  clearCommissions(): void {
    this.store.clear(CommissionsStoreEnum.COMMISSIONS);
  }

  loadCommissionDetailsFor(commission: Commission) {
    this.loadCommissionDetails
      .for(commission)
      .pipe(
        handleStoreLoading(this.store, CommissionsStoreEnum.COMMISSION_DETAILS)
      )
      .subscribe();
  }

  getCommissionDetails() {
    return this.store.get(CommissionsStoreEnum.COMMISSION_DETAILS);
  }

  updateCommissionWith(commission: Commission) {
    this.updateCommission
      .with(commission)
      .pipe(handleStoreLoading(this.store, CommissionsStoreEnum.COMMISSIONS))
      .subscribe();
  }
}
