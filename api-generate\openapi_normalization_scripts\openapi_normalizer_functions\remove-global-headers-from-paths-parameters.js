const GLOBAL_HEADERS_NAMES = [
    'Authorization',
    'Accept-Language',
    'DomainId',
    'DataSetLabel',
    'Ocp-Apim-Subscription-Key',
    'ocpApimSubscriptionKeyParameter',
    'domainIdParameter',
    'datasetLabelParameter',
];

/**
 *  This method take as input an openapi object corresponding to a openapi file from stoplight ; for instance : IS-GC_DEB_V1.json
 *  This method remove the global headers defined under each paths (under "parameters: []" keys) for the openapi generator NOT to add them in the generated service.
 *  👉 Those global parameters will be handled globally

 *
 * @param {*} openapiObj deserialized object from .json openapi file
 * @return {*} openapiObj with global headers (Authorization, Ocp-Apim-Subscription-Key ...) removed from path's parameters
 */
function removeGlobalHeadersFromPathsParameters(openapiObj) {
    if (openapiObj.paths) {
        for (const pathKey in openapiObj.paths) {
            const pathItem = openapiObj.paths[pathKey];

            for (const method in pathItem) {
                const operation = pathItem[method];

                if (operation.parameters) {
                    operation.parameters = operation.parameters.filter(
                        (param) => {
                            return param?.['$ref']
                                ? !GLOBAL_HEADERS_NAMES.find((headerName) =>
                                      param['$ref'].includes(headerName)
                                  )
                                : true;
                        }
                    );
                }
            }
        }
    }
    return openapiObj;
}

exports.removeGlobalHeadersFromPathsParameters =
    removeGlobalHeadersFromPathsParameters;
