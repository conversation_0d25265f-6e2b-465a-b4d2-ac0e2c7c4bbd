import { provideHttpClientTesting } from '@angular/common/http/testing';
import { waitForAsync } from '@angular/core/testing';
import { TokenManagerService } from '@gc/shared/api/authentication/data-access';
import { companiesIdFixture } from '@gc/company/models';
import {
  reportActions,
  ReportState,
  ReportStoreModule,
  selectCustomReportsCount,
  selectReports,
  selectReportsCategories,
} from '@gc/report/data-access';
import {
  reportCategoriesFixture,
  ReportCategory,
  ReportInformation,
  reportInformationFixture,
  reportInformationsFilteredByCategoriesId,
  reportInformationSortedByTitleFixture,
  reportInformationsWithCategoryClients,
  reportInformationsWithCategoryStocks,
} from '@gc/report/models';
import { LegacyApiService } from '@gc/shared/api/data-access';
import { ToggleLayoutOptions } from '@gc/shared/ui';
import { createRoutingFactory, SpectatorRouting } from '@ngneat/spectator/jest';
import { MemoizedSelector } from '@ngrx/store';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { MockComponents, MockDirectives, MockModule } from 'ng-mocks';
import { combineLatest, of } from 'rxjs';
import { ReportsListComponent } from './reports-list.component';
import { ReportCardRowComponent } from '@gc/report/feature';
import { UserStoreService } from '@gc/user/data-access';
import { NavigationService } from '@gc/core/navigation/feature';
import { Params } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { CompanyService } from '@gc/company/data-access';
import {
  CompanyMultiSelectComponent,
  IsSingleCompanyDirective,
} from '@gc/company/feature';

describe('ReportListComponent', () => {
  let store: MockStore<ReportState>;

  const mockCompaniesIds: string[] = companiesIdFixture();

  const mockReportsSortedByTitle: ReportInformation[] =
    reportInformationSortedByTitleFixture();

  const mockCategories: ReportCategory[] = reportCategoriesFixture();
  const reportsState: ReportState = {
    reports: undefined,
    categories: undefined,
    customReports: undefined,
    saveCustomReportStatus: 'NOT_PROCESSED',
  };

  const initialState = {
    reports: reportsState,
  };

  let selectMockReports: MemoizedSelector<
    ReportState,
    ReportInformation[] | undefined
  >;
  let selectMockCategories: MemoizedSelector<
    ReportState,
    ReportCategory[] | undefined
  >;

  let dispatchSpy: jest.SpyInstance<void>;

  const queryParamsCategoryStocks = 'Stocks';
  let spectator: SpectatorRouting<ReportsListComponent>;

  const declarations = [
    MockModule(ReportStoreModule),
    MockDirectives(IsSingleCompanyDirective),
    MockComponents(CompanyMultiSelectComponent, ReportCardRowComponent),
  ];

  const createComponentWithRoutingNoParams = createRoutingFactory({
    component: ReportsListComponent,
    declarations,
    providers: [provideHttpClientTesting(), provideMockStore({ initialState })],
    mocks: [TokenManagerService, CompanyService],
    detectChanges: false,
  });

  const createComponentWithQueryParamsCategoryStocks = createRoutingFactory({
    component: ReportsListComponent,
    params: { category: queryParamsCategoryStocks },
    declarations,
    imports: [],
    mocks: [CompanyService, LegacyApiService],
    providers: [provideMockStore({ initialState })],
    detectChanges: false,
  });

  describe('Given any state', () => {
    beforeEach(() => {
      spectator = createComponentWithRoutingNoParams();
      store = spectator.inject(MockStore);

      selectMockReports = store.overrideSelector(selectReports, undefined);
      selectMockCategories = store.overrideSelector(
        selectReportsCategories,
        undefined
      );
      store.refreshState();

      dispatchSpy = jest.spyOn(store, 'dispatch');

      const companyService = spectator.inject(CompanyService);
      jest
        .spyOn(companyService, 'companiesIds$', 'get')
        .mockReturnValue(of(mockCompaniesIds));

      spectator.detectChanges();
    });

    describe('ngOnInit', () => {
      describe('when component is initialized', () => {
        it('should dispatch reportsListComponentLoaded on ngOnInit', () => {
          const action = reportActions.reportsListComponentLoaded();
          expect(dispatchSpy).toHaveBeenCalledWith(action);
        });

        it('should init the report view', () => {
          expect(spectator.component.useView).toEqual(ToggleLayoutOptions.GRID);
        });
      });
    });

    describe('method toggleView', () => {
      it('should update the view', () => {
        spectator.component.toggleView(ToggleLayoutOptions.ROW);

        expect(spectator.component.useView).toEqual(ToggleLayoutOptions.ROW);
      });
    });

    describe('custom Reports Count', () => {
      describe('Given a state where the store contains a list of cutom reports and selector emits a number', () => {
        beforeEach(() => {
          spectator.inject(MockStore);
          store.overrideSelector(selectCustomReportsCount, 3);
          store.refreshState();
        });

        it('should emit the number of custom reports', waitForAsync(() => {
          spectator.component.customReportsCount$.subscribe((value) =>
            expect(value).toEqual(3)
          );
        }));
      });
    });

    describe('openStimulsoftReport method', () => {
      const [, , reportToOpen] = reportInformationFixture();
      const fakeCompanyId = 'fakeCompanyId';
      let getUserDefaultCompanyIdSpy: jest.SpyInstance<string | undefined>;
      let openInNewTabSpy: jest.SpyInstance<void>;

      beforeEach(() => {
        const navigationService = spectator.inject(NavigationService);
        const userStoreService = spectator.inject(UserStoreService);
        getUserDefaultCompanyIdSpy = jest
          .spyOn(userStoreService, 'getUserDefaultCompanyId')
          .mockReturnValue(fakeCompanyId);
        openInNewTabSpy = jest
          .spyOn(navigationService, 'openInNewTab')
          .mockImplementation(() => {});
      });

      describe('given a state where getUserDefaultCompanyId of UserStoreService return a value', () => {
        it('should call NavigationService openInNewTab method with the expected queryParams', () => {
          const expectedQueryParams: Params = {
            reportId: reportToOpen.id,
            reportFamily: reportToOpen.reportFamily,
            entrepriseId: fakeCompanyId,
            companiesIds: [],
            title: reportToOpen.title,
          };

          spectator.component.openStimulsoftReport(reportToOpen);

          expect(getUserDefaultCompanyIdSpy).toHaveBeenCalled();
          expect(openInNewTabSpy).toHaveBeenCalledWith(
            URL_PATHS.reportsReport,
            expectedQueryParams
          );
        });
      });
    });
  });

  describe('Given a state where the component is loaded with no queryparams in url', () => {
    // Dans ce composant, une liste de rapports est d'abord filtrée par catégorie puis passée au composant SearchbarComponent
    // qui émet la nouvelle liste filtrée. Puisque l'evenement est mocké, on ne peut pas uniquement se contenter de tester uniquement la liste finale:
    // il faut pouvoir tester que SearchbarComponent a recu la liste attendue avant de mocker son retour. De plus, l'utilisation d'un observable (filteredReportsByCategory$)
    // nous oblige à y souscrire dès l'initialisation du composant (une nouvelle souscription ne permet pas de récupérer les valeurs émises précédemment).
    let reportsPassedToSearchbarComponent:
      | ReportInformation[]
      | null
      | undefined = undefined;
    beforeEach(() => {
      spectator = createComponentWithRoutingNoParams();
      store = spectator.inject(MockStore);
      selectMockReports = store.overrideSelector(selectReports, undefined);
      selectMockCategories = store.overrideSelector(
        selectReportsCategories,
        undefined
      );
      store.refreshState();

      dispatchSpy = jest.spyOn(store, 'dispatch');
      reportsPassedToSearchbarComponent = undefined;
      const companyService = spectator.inject(CompanyService);
      jest
        .spyOn(companyService, 'companiesIds$', 'get')
        .mockReturnValue(of(mockCompaniesIds));
      spectator.detectChanges();
      spectator.component.filteredReportsByCategory$.subscribe(
        (value) => (reportsPassedToSearchbarComponent = value)
      );
    });

    describe('Given a state where companiesIds getter of CompanyService return a list of ids', () => {
      describe('then the company multi-select', () => {
        it('should init with all companies selected', () => {
          expect(spectator.component.companiesFC.value).toEqual(
            mockCompaniesIds
          );
        });
      });

      describe('and given a state where the reports state contains a list of reports', () => {
        beforeEach(() => {
          selectMockReports.setResult(mockReportsSortedByTitle);
          store.refreshState();
        });

        describe('and given a state where the reports state contains a list of categories', () => {
          beforeEach(() => {
            selectMockCategories.setResult(mockCategories);
            store.refreshState();
          });

          it('should init the category filter autocomplete with no filter applied', () => {
            expect(spectator.component.categoryFilterCtrl.value).toEqual([]);
          });

          it('should display the full list of reports sorted by title', waitForAsync(() => {
            spectator.component.filteredReports$.subscribe((reports) => {
              expect(reports).toEqual(mockReportsSortedByTitle);
            });
          }));

          describe('Then when the category filter is applied with a single category', () => {
            const [, , selectedCategory] = mockCategories;
            const selectedCategories = [selectedCategory];

            const expectedReportList = reportInformationsWithCategoryStocks();

            beforeEach(() => {
              spectator.component.categoryFilterCtrl.setValue(
                selectedCategories
              );
            });

            it('should filter the reports with the selected category and pass it to the SearchComponent', waitForAsync(() => {
              expect(reportsPassedToSearchbarComponent).toEqual(
                expectedReportList
              );
            }));

            describe('with no value in the SearchBarComponent so it doesnt filter and emits the search event which call the onSearch method', () => {
              beforeEach(() => {
                spectator.component.onSearch(expectedReportList);
              });

              it('should filter the report list when a single category is selected', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) =>
                  expect(value).toEqual(expectedReportList)
                );
              }));
            });

            describe('with a value in the SearchBarComponent so it filter and emits the search event which call the onSearch method with a different list', () => {
              const listFromSearchBarComponent = [expectedReportList[0]];
              beforeEach(() => {
                spectator.component.onSearch(listFromSearchBarComponent);
              });

              it('should filter the report list', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) => {
                  expect(value).not.toEqual(expectedReportList);
                  expect(value).toEqual(listFromSearchBarComponent);
                });
              }));
            });
          });

          describe('Then when the category filter is applied with multiple categories', () => {
            const selectedCategories = [mockCategories[1], mockCategories[0]];
            const expectedReportList = reportInformationsFilteredByCategoriesId(
              [mockCategories[1].id, mockCategories[0].id]
            );

            beforeEach(() => {
              spectator.component.categoryFilterCtrl.setValue(
                selectedCategories
              );
            });

            describe('with no value in the SearchBarComponent so it doesnt filter and emits the search event which call the onSearch method', () => {
              beforeEach(() => {
                spectator.component.onSearch(expectedReportList);
              });

              it('should filter the report list when multiple categories are selected', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) =>
                  expect(value).toEqual(expectedReportList)
                );
              }));
            });
          });

          describe('Then when the queryParam Category change, whatever the case', () => {
            const [, expectedCategory] = mockCategories;
            const expectedReportList = reportInformationsWithCategoryClients();

            beforeEach(() => {
              spectator.setRouteQueryParam('category', 'clIeNts');
            });

            it('should set the category filter fields with the new category', () => {
              expect(expectedCategory.label).toEqual('Clients');
              expect(spectator.component.categoryFilterCtrl.value).toEqual([
                expectedCategory,
              ]);
            });

            it('should filter the reports with the selected category and pass it to the SearchComponent', waitForAsync(() => {
              expect(reportsPassedToSearchbarComponent).toEqual(
                expectedReportList
              );
            }));

            describe('with no value in the SearchBarComponent so it doesnt filter and emits the search event which call the onSearch method', () => {
              beforeEach(() => {
                spectator.component.onSearch(expectedReportList);
              });

              it('should display the reports matching the new category', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) =>
                  expect(value).toEqual(expectedReportList)
                );
              }));
            });
          });
        });
      });
    });
  });

  describe('Given a state where the component is loaded with a queryparam category Stocks in url', () => {
    // Dans ce composant, une liste de rapports est d'abord filtrée par catégorie puis passée au composant SearchbarComponent
    // qui émet la nouvelle liste filtrée. Puisque l'evenement est mocké, on ne peut pas uniquement se contenter de tester uniquement la liste finale:
    // il faut pouvoir tester que SearchbarComponent a recu la liste attendue avant de mocker son retour. De plus, l'utilisation d'un observable (filteredReportsByCategory$)
    // nous oblige à y souscrire dès l'initialisation du composant (une nouvelle souscription ne permet pas de récupérer les valeurs émises précédemment).
    let reportsPassedToSearchbarComponent:
      | ReportInformation[]
      | null
      | undefined = undefined;
    beforeEach(() => {
      spectator = createComponentWithQueryParamsCategoryStocks();
      store = spectator.inject(MockStore);

      selectMockReports = store.overrideSelector(selectReports, undefined);
      selectMockCategories = store.overrideSelector(
        selectReportsCategories,
        undefined
      );
      store.refreshState();

      reportsPassedToSearchbarComponent = undefined;
      const companyService = spectator.inject(CompanyService);
      jest
        .spyOn(companyService, 'companiesIds$', 'get')
        .mockReturnValue(of(mockCompaniesIds));
      spectator.detectChanges();
      spectator.component.filteredReportsByCategory$.subscribe(
        (value) => (reportsPassedToSearchbarComponent = value)
      );
    });

    describe('Given a state where companiesIds getter of CompanyService return a list of ids', () => {
      describe('then the company multi-select', () => {
        it('should init with all companies selected', () => {
          expect(spectator.component.companiesFC.value).toEqual(
            mockCompaniesIds
          );
        });
      });

      describe('and given a state where the reports state contains a list of reports', () => {
        beforeEach(() => {
          selectMockReports.setResult(mockReportsSortedByTitle);
          store.refreshState();
        });

        describe('and given a state where the reports state contains a list of categories', () => {
          beforeEach(() => {
            selectMockCategories.setResult(mockCategories);
            store.refreshState();
          });

          it('should init the category filter autocomplete with the filter applied on the category received in route params', () => {
            const [, , categoryStocks] = mockCategories;
            expect(categoryStocks.label).toEqual('Stocks');

            spectator.component.categoryFilterCtrl.valueChanges.subscribe(
              () => {
                expect(spectator.component.categoryFilterCtrl.value).toEqual([
                  categoryStocks,
                ]);
              }
            );
          });

          it('should display the list of reports matching the category received in queryParams', waitForAsync(() => {
            const [, , categoryStocks] = mockCategories;
            expect(categoryStocks.label).toEqual('Stocks');

            const expectedReports: ReportInformation[] =
              reportInformationsWithCategoryStocks();

            combineLatest([
              spectator.component.categoryFilterCtrl.valueChanges,
              spectator.component.filteredReports$,
            ]).subscribe(([_ctrlValue, reports]) => {
              expect(spectator.component.categoryFilterCtrl.value).toEqual([
                categoryStocks,
              ]);
              expect(reports).toEqual(expectedReports);
            });
          }));

          describe('Then when the category filter is applied with a single category', () => {
            const [, , selectedCategory] = mockCategories;
            const selectedCategories: ReportCategory[] = [selectedCategory];
            const expectedReportList: ReportInformation[] =
              reportInformationsWithCategoryStocks();

            beforeEach(() => {
              spectator.component.categoryFilterCtrl.setValue(
                selectedCategories
              );
            });

            it('should filter the reports with the selected category and pass it to the SearchComponent', waitForAsync(() => {
              expect(reportsPassedToSearchbarComponent).toEqual(
                expectedReportList
              );
            }));

            describe('with no value in the SearchBarComponent so it doesnt filter and emits the search event which call the onSearch method', () => {
              beforeEach(() => {
                spectator.component.onSearch(expectedReportList);
              });

              it('should filter the report list when a single category is selected', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) =>
                  expect(value).toEqual(expectedReportList)
                );
              }));
            });

            describe('with a value in the SearchBarComponent so it filter and emits the search event which call the onSearch method with a different list', () => {
              const listFromSearchBarComponent = [expectedReportList[0]];
              beforeEach(() => {
                spectator.component.onSearch(listFromSearchBarComponent);
              });

              it('should filter the report list', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) => {
                  expect(value).not.toEqual(expectedReportList);
                  expect(value).toEqual(listFromSearchBarComponent);
                });
              }));
            });
          });

          describe('Then when the category filter is applied with multiple categories', () => {
            const selectedCategories: ReportCategory[] = [
              mockCategories[1], // Articles
              mockCategories[0], // Clients
            ];

            const expectedReportList: ReportInformation[] =
              reportInformationsFilteredByCategoriesId([
                mockCategories[1].id,
                mockCategories[0].id,
              ]);

            beforeEach(() => {
              spectator.component.categoryFilterCtrl.setValue(
                selectedCategories
              );
            });

            describe('with no value in the SearchBarComponent so it doesnt filter and emits the search event and call the onSearch method with the same report list', () => {
              beforeEach(() => {
                spectator.component.onSearch(expectedReportList);
              });

              it('should filter the report list', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) =>
                  expect(value).toEqual(expectedReportList)
                );
              }));
            });

            describe('with a value in the SearchBarComponent so it filter and emits the search event which call the onSearch method with a different list', () => {
              const listFromSearchBarComponent = [expectedReportList[0]];
              beforeEach(() => {
                spectator.component.onSearch(listFromSearchBarComponent);
              });

              it('should filter the report list', waitForAsync(() => {
                spectator.component.filteredReports$.subscribe((value) => {
                  expect(value).not.toEqual(expectedReportList);
                  expect(value).toEqual(listFromSearchBarComponent);
                });
              }));
            });
          });
        });
      });
    });
  });
});
