<ng-container *transloco="let t">
  <div mat-dialog-title>{{ t('sharedDialog.titles.info') }}</div>

  <mat-dialog-content>
    <div class="message-container">
      {{ t('accounting.monthly-edition-tab.dialog.message') }}
    </div>

    <div class="select-container">
      <ng-container
        *ngrxLet="{
          loadingStatusStartMonths: loadingStatusStartMonths$,
        } as vm">
        @if (vm.loadingStatusStartMonths === 'LOADED') {
          <div class="select-label-month-container">
            {{ t('accounting.monthly-edition-tab.dialog.label') }}
          </div>
          <mat-form-field id="select">
            <mat-select [formControl]="selectedDefaultMonthsFC">
              @for (
                availableMonth of availableStartMonths;
                track availableMonth
              ) {
                <mat-option [value]="availableMonth">
                  {{
                    availableMonth
                      | translocoDate
                        : {
                            month: 'long',
                            year: 'numeric',
                          }
                      | capitalize
                  }}
                </mat-option>
              }
            </mat-select>
          </mat-form-field>
        } @else if (vm.loadingStatusStartMonths === 'IN_PROGRESS') {
          <gc-loader
            [label]="t('accounting.monthly-edition-tab.dialog.is-loading')" />
        }
      </ng-container>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions [align]="'end'">
    <button mat-stroked-button mat-dialog-close>
      {{ t('sharedAction.cancel') }}
    </button>

    <button
      cdkFocusInitial
      mat-raised-button
      color="primary"
      (click)="onSelect()">
      {{ t('sharedAction.select') }}
    </button>
  </mat-dialog-actions>
</ng-container>
