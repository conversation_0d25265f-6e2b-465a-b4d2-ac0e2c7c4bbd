import { MainContainerComponent } from '@gc/core/navigation/feature';
import { DebDeclarationComponent } from '@gc/features/deb/declaration/deb-declaration';
import { TranslocoDirective } from '@jsverse/transloco';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebPageComponent } from './deb-page.component';

describe('DebPageComponent', () => {
  let spectator: Spectator<DebPageComponent>;

  const createComponent = createComponentFactory({
    component: DebPageComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(MainContainerComponent, DebDeclarationComponent),
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });
});
