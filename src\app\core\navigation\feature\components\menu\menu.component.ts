import { Component, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CodeModuleCommercial, URL_PATHS } from '@gc/core/navigation/models';
import { TranslocoModule } from '@jsverse/transloco';
import { NavigationRightsService } from '../../services/navigation-rights.service';
import { FlagService } from '../../services/flag.service';
import { UserModulesService } from '../../services/user-modules.service';
import { DeviceAccessService } from '../../services/device-access.service';
import { ALLOWED_DEVICES } from '@gc/navigation/device-access';
import { PushPipe } from '@ngrx/component';

@Component({
  selector: 'gc-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss', './menu-theme.component.scss'],
  standalone: true,
  imports: [TranslocoModule, RouterLink, PushPipe],
})
export class MenuComponent {
  navigationRightsService = inject(NavigationRightsService);
  userModulesService = inject(UserModulesService);
  flagService = inject(FlagService);
  deviceAccessService = inject(DeviceAccessService);
  urlPaths = URL_PATHS;
  CodeModuleCommercial = CodeModuleCommercial;
  ALLOWED_DEVICES = ALLOWED_DEVICES;
}
