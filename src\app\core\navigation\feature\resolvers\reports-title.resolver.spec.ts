import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { waitForAsync } from '@angular/core/testing';
import { getTitleReport } from './reports-title.resolver';

describe('Reports Title Resolver', () => {
  describe('getTitleReport', () => {
    describe('when the route has a title in the queryParams', () => {
      const expectedTitle = 'le titre de la stat';

      const mockRoute = {
        queryParams: { id: 'STAT_A', title: expectedTitle },
      } as unknown as ActivatedRouteSnapshot;

      it('should return the title', waitForAsync(() => {
        const result = getTitleReport(mockRoute, {} as RouterStateSnapshot);
        expect(result).toEqual(expectedTitle);
      }));
    });

    describe('when the route has no title in the queryParams', () => {
      const mockRoute = {
        queryParams: { id: 'STAT_A' },
      } as unknown as ActivatedRouteSnapshot;

      it('should return undefined', waitForAsync(() => {
        const result = getTitleReport(mockRoute, {} as RouterStateSnapshot);
        expect(result).toBeUndefined();
      }));
    });
  });
});
