import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { CommissionsFacade } from './commissions.facade';
import {
  CommissionsStore,
  CommissionsStoreEnum,
} from '@gc/core/sales/commissions/application/store';
import { CommissionDetailsUseCase } from '../use-cases/commission-details.use-case';
import { Commission, CommissionsFilters } from '../../domains/models';
import { of } from 'rxjs';
import { CommissionsUseCase, UpdateCommissionUseCase } from '../use-cases';

describe('CommissionsFacade', () => {
  let spectator: SpectatorService<CommissionsFacade>;
  let commissionDetailsUseCase: CommissionDetailsUseCase;
  let commissionsUseCase: CommissionsUseCase;
  let updateCommissionUseCase: UpdateCommissionUseCase;
  let commissionStore: CommissionsStore;

  const createService = createServiceFactory({
    service: CommissionsFacade,
    mocks: [
      CommissionsStore,
      CommissionDetailsUseCase,
      CommissionsUseCase,
      UpdateCommissionUseCase,
    ],
  });

  const mockCommission: Commission = {
    documentId: 'DOC1',
    documentDate: '2024-01-01',
    amount: 100,
    amountToBePaid: 50,
    status: 1,
  };

  const mockFilters: CommissionsFilters = {
    companyId: 'company-1',
    representativeId: 'rep-1',
    date: '2024-01-01',
  };

  beforeEach(() => {
    spectator = createService();
    commissionDetailsUseCase = spectator.inject(CommissionDetailsUseCase);
    commissionsUseCase = spectator.inject(CommissionsUseCase);
    updateCommissionUseCase = spectator.inject(UpdateCommissionUseCase);
    commissionStore = spectator.inject(CommissionsStore);
  });

  describe('getCommissionsForCurrentFilters', () => {
    it('should call store.get with COMMISSIONS enum', () => {
      const getSpy = jest.spyOn(commissionStore, 'get');

      spectator.service.getCommissionsForCurrentFilters();

      expect(getSpy).toHaveBeenCalledWith(CommissionsStoreEnum.COMMISSIONS);
    });
  });

  describe('loadCommissionsForFilter', () => {
    it('should call commissions use case with filters', () => {
      const forSpy = jest
        .spyOn(commissionsUseCase, 'for')
        .mockReturnValue(of([]));

      spectator.service.loadCommissionsForFilter(mockFilters);

      expect(forSpy).toHaveBeenCalledWith(mockFilters);
    });
  });

  describe('clearCommissions', () => {
    it('should call store.clear with COMMISSIONS enum', () => {
      const clearSpy = jest.spyOn(commissionStore, 'clear');

      spectator.service.clearCommissions();

      expect(clearSpy).toHaveBeenCalledWith(CommissionsStoreEnum.COMMISSIONS);
    });
  });

  describe('loadCommissionDetailsFor', () => {
    it('should call use case and update store with results', () => {
      const forSpy = jest
        .spyOn(commissionDetailsUseCase, 'for')
        .mockReturnValue(of([]));

      spectator.service.loadCommissionDetailsFor(mockCommission);

      expect(forSpy).toHaveBeenCalledWith(mockCommission);
    });
  });

  describe('getCommissionDetails', () => {
    it('should call store.get with COMMISSION_DETAILS enum', () => {
      const getSpy = jest.spyOn(commissionStore, 'get');

      spectator.service.getCommissionDetails();

      expect(getSpy).toHaveBeenCalledWith(
        CommissionsStoreEnum.COMMISSION_DETAILS
      );
    });
  });

  describe('updateCommissionWith', () => {
    it('should call update commission use case with commission', () => {
      const withSpy = jest
        .spyOn(updateCommissionUseCase, 'with')
        .mockReturnValue(of([]));

      spectator.service.updateCommissionWith(mockCommission);

      expect(withSpy).toHaveBeenCalledWith(mockCommission);
    });
  });
});
