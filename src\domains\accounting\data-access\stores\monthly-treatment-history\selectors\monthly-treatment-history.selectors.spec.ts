import { initialState } from '../reducers/monthly-treatment-history.reducer';
import { MonthlyTreatmentHistoryState } from '../models/monthly-treatment-history-state.model';
import * as selectors from './monthly-treatment-history.selectors';
import {
  selectAvailableMonthlyEditions,
  selectAvailableMonthlyEditionsLoadingStatus,
  selectEnclosedMonthsOfSelectedYear,
  selectEnclosedYears,
  selectHasEnclosedMonths,
  selectIsNewestEnclosedMonthSelected,
  selectIsOldestEnclosedMonthSelected,
  selectSelectedEnclosedMonth,
  selectSelectedEnclosedYear,
  selectSelectedMonthlyEditions,
  selectUncloseStatus,
} from './monthly-treatment-history.selectors';
import { MonthlyTreatmentEditionsEnum } from '@gc/accounting/models';

describe('MonthlyTreatmentHistorySelectors', () => {
  [
    {
      currentState: {
        ...initialState,
        selectedEnclosedMonth: new Date('2024-07-29'),
      } as MonthlyTreatmentHistoryState,
      selector: selectSelectedEnclosedMonth,
      expectedResult: new Date('2024-07-29'),
      selectTitle: 'selectSelectedEnclosedMonth',
      givenTitle: 'given a current state with a selectedEnclosedMonth set',
      shouldTitle: 'should return selectedEnclosedMonth value',
    },
    {
      currentState: {
        ...initialState,
        selectedEnclosedYear: 2024,
      } as MonthlyTreatmentHistoryState,
      selector: selectSelectedEnclosedYear,
      expectedResult: 2024,
      selectTitle: 'selectSelectedEnclosedYear',
      givenTitle: 'given a current state with selectedEnclosedYear set',
      shouldTitle: 'should return the selectedEnclosedYear value',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
      } as MonthlyTreatmentHistoryState,
      selector: selectEnclosedYears,
      expectedResult: [2024, 2023],
      selectTitle: 'selectEnclosedYears',
      givenTitle: 'given a current state with a enclosedYears set',
      shouldTitle: 'should return enclosedYears value',
    },
    {
      currentState: {
        ...initialState,
        enclosedMonthsOfSelectedYear: [
          new Date('2024-07-29'),
          new Date('2024-08-29'),
        ],
      } as MonthlyTreatmentHistoryState,
      selector: selectEnclosedMonthsOfSelectedYear,
      expectedResult: [new Date('2024-07-29'), new Date('2024-08-29')],
      selectTitle: 'selectEnclosedMonthsOfSelectedYear',
      givenTitle:
        'given a current state with a enclosedMonthsOfSelectedYear set',
      shouldTitle: 'should return the enclosedMonthsOfSelectedYear value',
    },
    {
      currentState: {
        ...initialState,
        selectedMonthlyEditions: [
          MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          MonthlyTreatmentEditionsEnum.DEBT,
        ],
      } as MonthlyTreatmentHistoryState,
      selector: selectSelectedMonthlyEditions,
      expectedResult: [
        MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
        MonthlyTreatmentEditionsEnum.DEBT,
      ],
      selectTitle: 'selectSelectedMonthlyEditions',
      givenTitle: 'given a current state with a selectedMonthlyEditions set',
      shouldTitle: 'should return the selectedMonthlyEditions value',
    },
    {
      currentState: {
        ...initialState,
        availableMonthlyEditions: [
          {
            id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
            isAvailable: true,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DEBT,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.DETAILS,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.RECEIPTS,
            isAvailable: false,
          },
          {
            id: MonthlyTreatmentEditionsEnum.SALES,
            isAvailable: false,
          },
        ],
      } as MonthlyTreatmentHistoryState,
      selector: selectAvailableMonthlyEditions,
      expectedResult: [
        {
          id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          isAvailable: true,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DEBT,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DETAILS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.RECEIPTS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.SALES,
          isAvailable: false,
        },
      ],
      selectTitle: 'selectAvailableMonthlyEditions',
      givenTitle: 'given a current state with a availableMonthlyEditions set',
      shouldTitle: 'should return the availableMonthlyEditions value',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2024,
        enclosedMonthsOfSelectedYear: [
          new Date('2024-01-01'),
          new Date('2024-02-01'),
        ],
        selectedEnclosedMonth: new Date('2024-02-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsNewestEnclosedMonthSelected,
      expectedResult: true,
      selectTitle: 'selectIsNewestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear being the biggest of enclosedYears and selectedEnclosedMonth being the biggest of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return true',
    },
    {
      currentState: {
        ...initialState,
        availableMonthlyEditionsLoadingStatus: 'LOADED',
      } as MonthlyTreatmentHistoryState,
      selector: selectAvailableMonthlyEditionsLoadingStatus,
      expectedResult: 'LOADED',
      selectTitle: 'selectAvailableMonthlyEditionsLoadingStatus',
      givenTitle:
        'given a current state with a availableMonthlyEditionsLoadingStatus set to LOADED',
      shouldTitle: 'should return LOADED',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2024,
        enclosedMonthsOfSelectedYear: [
          new Date('2024-01-01'),
          new Date('2024-02-01'),
        ],
        selectedEnclosedMonth: new Date('2024-01-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsNewestEnclosedMonthSelected,
      expectedResult: false,
      selectTitle: 'selectIsNewestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear being the biggest of enclosedYears and selectedEnclosedMonth not being the biggest of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return false',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2023,
        enclosedMonthsOfSelectedYear: [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
        ],
        selectedEnclosedMonth: new Date('2023-02-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsNewestEnclosedMonthSelected,
      expectedResult: false,
      selectTitle: 'selectIsNewestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear not being the biggest of enclosedYears and selectedEnclosedMonth being the biggest of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return false',
    },
    {
      currentState: {
        ...initialState,
        uncloseSelectedMonthStatus: 'IN_PROGRESS',
      } as MonthlyTreatmentHistoryState,
      selector: selectUncloseStatus,
      expectedResult: 'IN_PROGRESS',
      selectTitle: 'selectUncloseStatus',
      givenTitle:
        'given a current state with uncloseSelectedMonthStatus equal to "IN_PROGRESS"',
      shouldTitle: 'should return IN_PROGRESS',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2023,
        enclosedMonthsOfSelectedYear: [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
          new Date('2024-02-01'),
        ],
        selectedEnclosedMonth: new Date('2023-01-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsOldestEnclosedMonthSelected,
      expectedResult: true,
      selectTitle: 'selectIsOldestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear being the oldest of enclosedYears and selectedEnclosedMonth being the oldes of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return true',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2023,
        enclosedMonthsOfSelectedYear: [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
          new Date('2024-02-01'),
        ],
        selectedEnclosedMonth: new Date('2023-02-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsOldestEnclosedMonthSelected,
      expectedResult: false,
      selectTitle: 'selectIsOldestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear being the oldest of enclosedYears and selectedEnclosedMonth not being the oldest of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return false',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2024,
        enclosedMonthsOfSelectedYear: [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
          new Date('2024-02-01'),
        ],
        selectedEnclosedMonth: new Date('2023-01-01'),
      } as MonthlyTreatmentHistoryState,
      selector: selectIsOldestEnclosedMonthSelected,
      expectedResult: false,
      selectTitle: 'selectIsOldestEnclosedMonthSelected',
      givenTitle:
        'given a current state with a selectedEnclosedYear not being the oldest of enclosedYears and selectedEnclosedMonth being the oldest of enclosedMonthsOfSelectedYear',
      shouldTitle: 'should return false',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: undefined,
        selectedEnclosedYear: undefined,
        enclosedMonthsOfSelectedYear: [],
        selectedEnclosedMonth: null,
      } as MonthlyTreatmentHistoryState,
      selector: selectHasEnclosedMonths,
      expectedResult: null,
      selectTitle: 'selectHasEnclosedMonths',
      givenTitle: 'given an initial state',
      shouldTitle: 'should return null',
    },
    {
      currentState: {
        ...initialState,
        enclosedYears: [2024, 2023],
        selectedEnclosedYear: 2024,
        selectedEnclosedMonth: new Date('2023-01-01'),
        enclosedMonthsOfSelectedYear: [
          new Date('2023-01-01'),
          new Date('2023-02-01'),
          new Date('2024-02-01'),
        ],
      } as MonthlyTreatmentHistoryState,
      selector: selectHasEnclosedMonths,
      expectedResult: true,
      selectTitle: 'selectHasEnclosedMonths',
      givenTitle: 'given a current state with enclosure',
      shouldTitle: 'should return true',
    },

    {
      currentState: {
        ...initialState,
        enclosedYears: [],
      } as MonthlyTreatmentHistoryState,
      selector: selectHasEnclosedMonths,
      expectedResult: false,
      selectTitle: 'selectHasEnclosedMonths',
      givenTitle: 'given a current state without enclosure',
      shouldTitle: 'should return false',
    },
  ].forEach(
    ({
      currentState,
      selector,
      expectedResult,
      selectTitle,
      givenTitle,
      shouldTitle,
    }) => {
      describe(`${selectTitle} selector`, () => {
        describe(`${givenTitle}`, () => {
          it(`${shouldTitle}`, () => {
            const result = selector.projector(currentState);

            expect(result).toStrictEqual(expectedResult);
          });
        });
      });
    }
  );
});

describe('selectCompanyIdAndSelectedEnclosedMonth', () => {
  it('should get companyId and selectedEnclosedMonth value', () => {
    const companyId = '1234';
    const selectedEnclosedMonth = new Date('2024-08');

    const state = {
      selectedEnclosedMonth,
    } as MonthlyTreatmentHistoryState;

    const select = selectors.selectCompanyIdAndSelectedEnclosedMonth.projector(
      companyId,
      state.selectedEnclosedMonth
    );

    expect(select).toEqual({
      companyId,
      selectedEnclosedMonth,
    });
  });
});

describe('selectHasNotEditionToConsult', () => {
  it('should get availableMonthlyEditions and selectedMonthlyEditions value and determine if there is consultation available', () => {
    const availableMonthlyEditions = [
      {
        id: MonthlyTreatmentEditionsEnum.SALES,
        isAvailable: true,
      },
    ];
    const selectedMonthlyEditions: number[] = [];

    const state = {
      availableMonthlyEditions,
      selectedMonthlyEditions,
    } as MonthlyTreatmentHistoryState;

    const select = selectors.selectHasNotEditionToConsult.projector(
      state.availableMonthlyEditions,
      state.selectedMonthlyEditions
    );

    expect(select).toBeTruthy();
  });
});
