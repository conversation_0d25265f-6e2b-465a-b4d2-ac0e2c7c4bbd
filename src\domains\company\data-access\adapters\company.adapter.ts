import { Company } from '@gc/company/models';
import { CompanyApi } from '@gc/shared/api/data-access';

export class CompanyAdapter {
  public static fromApi(companyApi: CompanyApi): Company {
    if (!companyApi.id || !companyApi.code || !companyApi.designation) {
      throw new Error();
    }
    const { id, code, designation } = companyApi;
    return {
      id,
      code,
      designation,
    };
  }
}
