import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { DebDeclarationDetailsComponent } from './deb-declaration-details.component';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import {
  DebDetails,
  DebLineDetailsDocumentEnum,
  DraftDeclaration,
} from '@gc/core/deb/domains/models';
import { LoaderComponent } from '@gc/shared/ui';
describe('DebDeclarationDetailsComponent', () => {
  let spectator: Spectator<DebDeclarationDetailsComponent>;
  let component: DebDeclarationDetailsComponent;
  const createComponent = createComponentFactory({
    component: DebDeclarationDetailsComponent,
    imports: [MatTableModule, MatCardModule],
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(LoaderComponent),
    ],
  });
  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should correctly map document types', () => {
    Object.entries(component.documentTypeMappings).forEach(
      ([enumValue, expectedTranslation]) => {
        expect(
          component.getDocumentTypeTranslation(
            enumValue as DebLineDetailsDocumentEnum
          )
        ).toBe(expectedTranslation);
      }
    );
  });
});
