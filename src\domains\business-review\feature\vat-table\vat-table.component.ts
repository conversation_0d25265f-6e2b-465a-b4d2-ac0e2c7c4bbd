import { SelectionModel } from '@angular/cdk/collections';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  inject,
} from '@angular/core';
import {
  MatCheckboxChange,
  MatCheckboxModule,
} from '@angular/material/checkbox';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { BusinessReviewService } from '@gc/business-review/data-access';
import { LoaderComponent } from '@gc/shared/ui';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { TranslocoDecimalPipe } from '@jsverse/transloco-locale';
import { LetDirective, PushPipe } from '@ngrx/component';
import { Observable, filter, map, tap } from 'rxjs';
import { BusinessReviewFormService } from '../services/business-review-form.service';

@Component({
  selector: 'gc-vat-table',
  standalone: true,
  imports: [
    MatTableModule,
    MatCheckboxModule,
    TranslocoDirective,
    PushPipe,
    LetDirective,
    TranslocoDecimalPipe,
    LoaderComponent,
  ],
  templateUrl: './vat-table.component.html',
  styleUrls: ['./vat-table.component.scss', './vat-table-theme.component.scss'],
})
export class VatTableComponent implements OnChanges {
  private readonly _businessReviewService = inject(BusinessReviewService);
  private readonly _businessReviewFormService = inject(
    BusinessReviewFormService
  );
  private readonly _translocoService = inject(TranslocoService);

  @Output() selectionChange = new EventEmitter<number | null>();

  @Input() selectedVatRate: number | null | undefined = null;

  displayedColumns: string[] = ['select', 'label', 'rate'];

  dataSource!: MatTableDataSource<number>;

  selection: SelectionModel<number> = new SelectionModel<number>(
    false,
    undefined
  );

  vatDatasource$: Observable<MatTableDataSource<number>> =
    this._businessReviewService.vatRates$$.pipe(
      filter(Boolean),
      map((vatRates) => {
        const sortedVatRates = vatRates.sort((a, b) => b - a);
        return new MatTableDataSource<number>(sortedVatRates);
      }),
      tap(() => {
        if (this._businessReviewFormService.vatRateFC.value)
          this.selection.select(
            this._businessReviewFormService.vatRateFC.value
          );
      })
    );

  isLoading$ = this._businessReviewFormService.isLoadingAdvancedSettingsData$$;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedVatRate']) {
      this.selectedVatRate
        ? this.selection.select(this.selectedVatRate)
        : this.selection.clear();
    }
  }

  toggleRow(event: MatCheckboxChange, rate: number): void {
    // Avoid unchecking and deselecting the currently selected rate to prevent an empty selection
    if (this.selection.isSelected(rate) && !event.checked) {
      event.source.checked = true;
    } else {
      this.selection.toggle(rate);
      this.selectionChange.emit(this.selection.selected[0]);
    }
  }

  checkboxAriaLabel(rate: number): string {
    return this.selection.isSelected(rate)
      ? this._translocoService.translate(
          'businessReview.advanced-settings-tab.vat.table.checkbox-aria-label.deselect',
          { rate }
        )
      : this._translocoService.translate(
          'businessReview.advanced-settings-tab.vat.table.checkbox-aria-label.select',
          { rate }
        );
  }
}
