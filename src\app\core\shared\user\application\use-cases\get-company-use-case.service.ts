import { inject, Injectable } from '@angular/core';
import {
  SharedUserStore,
  SharedUserStoreEnum,
} from '@gc/core/shared/user/application/store';
import { handleStoreLoading } from '@gc/core/shared/store/operators';
import { GetCompanyPort } from '@gc/core/shared/user/domains/ports';

@Injectable({
  providedIn: 'root',
})
export class GetCompanyUseCase {
  private readonly SELECTED_COMPANY = SharedUserStoreEnum.SELECTED_COMPANY;
  private readonly port = inject(GetCompanyPort);
  private readonly store = inject(SharedUserStore);

  forCurrentUser() {
    const { SELECTED_COMPANY: selectedCompany } = this;

    this.port
      .getCompany()
      .pipe(
        handleStoreLoading(this.store, selectedCompany, {
          completeOnFirstEmission: false,
        })
      )
      .subscribe();

    return this.store.get(selectedCompany);
  }
}
