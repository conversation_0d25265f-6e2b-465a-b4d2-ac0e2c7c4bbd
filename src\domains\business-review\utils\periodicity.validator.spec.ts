import { FormControl, UntypedFormGroup } from '@angular/forms';

import { periodicityValidator } from './periodicity.validator';
import { CalendarYear, PeriodicityKind } from '@gc/business-review/models';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';

describe('periodicityValidator', () => {
  let periodicityKindFC: FormControl<PeriodicityKind | null>;
  let calendarYearFC: FormControl<CalendarYear | null>;
  let fiscalYearFC: FormControl<FiscalYearDateRange | null>;
  let formGroup: UntypedFormGroup;

  beforeEach(() => {
    periodicityKindFC = new FormControl<PeriodicityKind | null>(null);
    calendarYearFC = new FormControl<CalendarYear | null>(null);
    fiscalYearFC = new FormControl<FiscalYearDateRange | null>(null);
    formGroup = new UntypedFormGroup({
      periodicityKind: periodicityKindFC,
      calendarYearFC: calendarYearFC,
      fiscalYearFC: fiscalYearFC,
    });
  });

  describe(`Given a state where the periodicuty kind is set to ${PeriodicityKind.CALENDAR_YEAR}`, () => {
    beforeEach(() => {
      periodicityKindFC.setValue(PeriodicityKind.CALENDAR_YEAR);
    });

    describe(`When the calendar year is not set`, () => {
      beforeEach(() => {
        calendarYearFC.setValue(null);
      });
      it('should return an error', () => {
        const result = periodicityValidator()(formGroup);
        expect(result).toEqual({ emptyDateRange: true });
      });
    });

    describe(`When the calendar year is set`, () => {
      beforeEach(() => {
        calendarYearFC.setValue({
          startDate: new Date(),
          endDate: new Date(),
          viewValue: '2021',
        } as CalendarYear);
      });
      it('should return null', () => {
        expect(periodicityValidator()(formGroup)).toBeNull();
      });
    });
  });

  describe(`Given a state where the periodicuty kind is set to ${PeriodicityKind.FISCAL_YEAR}`, () => {
    beforeEach(() => {
      periodicityKindFC.setValue(PeriodicityKind.FISCAL_YEAR);
    });

    describe(`When the calendar year is not set`, () => {
      beforeEach(() => {
        fiscalYearFC.setValue(null);
      });
      it('should return an error', () => {
        expect(periodicityValidator()(formGroup)).toEqual({
          emptyDateRange: true,
        });
      });
    });

    describe(`When the fiscal year is set`, () => {
      beforeEach(() => {
        fiscalYearFC.setValue({
          dateFrom: new Date(),
          dateTo: new Date(),
        } as FiscalYearDateRange);
      });
      it('should return null', () => {
        expect(periodicityValidator()(formGroup)).toBeNull();
      });
    });
  });
});
