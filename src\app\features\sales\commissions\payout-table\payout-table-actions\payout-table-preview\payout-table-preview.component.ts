import {
  ChangeDetectionStrategy,
  Component,
  Input,
  inject,
} from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { TranslocoModule } from '@jsverse/transloco';
import { CommissionsFacade } from '@gc/core/sales/commissions/application/facades';
import { Commission } from '@gc/core/sales/commissions/domains/models';
import { LoaderComponent } from '@gc/shared/ui';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { commissionsInfrastructureProviders } from '@gc/core/sales/commissions/infrastructure/commissions-infrastructure.providers';
import { commissionsServicesProviders } from '@gc/core/sales/commissions/commissions-services.providers';
import { MatButtonModule } from '@angular/material/button';
import { PushPipe } from '@ngrx/component';
import { CurrencyService } from '@gc/currency/data-access';
import { AmountWithCurrencyPipe } from '@gc/currency/ui';

@Component({
  selector: 'gc-payout-table-preview',
  standalone: true,
  imports: [
    MatCardModule,
    MatTableModule,
    TranslocoModule,
    LoaderComponent,
    MatIconModule,
    MatDialogModule,
    MatButtonModule,
    PushPipe,
    AmountWithCurrencyPipe,
  ],
  providers: [
    ...commissionsInfrastructureProviders(),
    ...commissionsServicesProviders(),
  ],
  templateUrl: './payout-table-preview.component.html',
  styleUrls: ['./payout-table-preview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PayoutTablePreviewComponent {
  private readonly commissionsFacade = inject(CommissionsFacade);
  private readonly currencyService = inject(CurrencyService);
  defaultCurrencyLoadingStatus$ =
    this.currencyService.getDefaultCurrencyLoadingStatus$();
  currency$ = this.currencyService.getDefaultCurrency$();
  @Input() commission!: Commission;

  readonly commissionDetails = this.commissionsFacade.getCommissionDetails();

  displayedColumns: string[] = [
    'position',
    'productLabel',
    'quantity',
    'amountWithoutTax',
    'amountWithTax',
    'commissionRate',
    'commissionAmount',
  ];

  constructor() {
    this.commissionsFacade.loadCommissionDetailsFor(this.commission);
  }
}
