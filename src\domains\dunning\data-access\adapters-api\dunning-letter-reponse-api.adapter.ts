import { DunningLetterApiAdapter } from './dunning-letter-api.adapter';
import { DunningLettersResult } from '@gc/dunning/models';
import {
  DunningLetterApi,
  DunningLetterResponseApi,
} from '@gc/shared/api/data-access';

export class DunningLetterResponseApiAdapter {
  public static fromApi(
    dunningLetterResponseApi: DunningLetterResponseApi | undefined
  ): DunningLettersResult {
    return {
      dunningLetters: dunningLetterResponseApi?.dunningLetters?.map(
        (dunningLetterApi: DunningLetterApi) => {
          return DunningLetterApiAdapter.fromApi(dunningLetterApi);
        }
      ),
    };
  }
}
