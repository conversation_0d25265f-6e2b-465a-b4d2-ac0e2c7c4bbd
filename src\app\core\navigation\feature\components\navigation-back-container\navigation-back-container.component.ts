import { Component, Input } from '@angular/core';
import { HeaderComponent } from '../header/header.component';
import { SubHeaderComponent } from '@gc/core/navigation/ui';

@Component({
  selector: 'gc-navigation-back-container',
  templateUrl: './navigation-back-container.component.html',
  styleUrls: [
    './navigation-back-container.component.scss',
    './navigation-back-container-theme.component.scss',
  ],
  standalone: true,
  imports: [HeaderComponent, SubHeaderComponent],
})
export class NavigationBackContainerComponent {
  @Input() title!: string;
  @Input() routeBack? = '../';
}
