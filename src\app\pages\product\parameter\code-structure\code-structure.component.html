<div data-testid="code-structure" class="container" gcBlockReload>
  <ng-container *transloco="let t">
    <form
      class="form-container"
      [formGroup]="productCharacteristicsFormService.productCharacteristicsForm"
      (ngSubmit)="onSave()">
      <ng-container
        *transloco="let t; read: 'productParameter.code-structure-tab'">
        <gc-master-detail-panels-layout
          [masterPanelTitle]="t('titles.master-panel')">
          <div masterPanel class="panel">
            <div class="characteristics-container">
              <gc-characteristics-list-view-edit />
            </div>
            @if (canDisplayVisualizationPanel$ | ngrxPush) {
              <div class="visualization-container">
                <div class="title-container">
                  {{ t('titles.visualization-panel') }}
                </div>
                <gc-code-structure-result />
              </div>
            }
          </div>

          <div detailPanelHeader class="detail-header">
            {{ t('titles.detail-panel') }}
            @if (selectedCharacteristicCodesAreLocked$ | ngrxPush) {
              <div class="locked-icons-container">
                <mat-icon> lock </mat-icon>
                <gc-fixed-characteristic-edit-info-icon />
              </div>
            }
          </div>
          <div detailPanel class="panel">
            <gc-characteristic-codes-list-view-edit />
          </div>
        </gc-master-detail-panels-layout>
      </ng-container>
      <div class="action-button-container">
        <button
          mat-stroked-button
          color="primary"
          type="button"
          (click)="cancel()">
          {{ t('sharedAction.cancel') }}
        </button>
        <button
          mat-raised-button
          color="primary"
          type="submit"
          [disabled]="
            !productCharacteristicsFormService.productCharacteristicsForm
              .dirty ||
            !productCharacteristicsFormService.productCharacteristicsForm
              .valid ||
            saveInProgress
          ">
          {{ t('sharedAction.save') }}
        </button>
      </div>
    </form>
  </ng-container>
</div>
