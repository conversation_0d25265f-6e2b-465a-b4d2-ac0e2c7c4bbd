import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { HasSomeAvailableEditionsPipe } from './has-some-available-editions.pipe';

describe('HasSomeAvailableEditionsPipe', () => {
  const pipe = new HasSomeAvailableEditionsPipe();
  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  describe('given a list of MonthlyTreatmentAvailableEdition with some being available', () => {
    const monthlyTreatmentAvailableEditions: MonthlyTreatmentAvailableEdition[] =
      [
        {
          id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          isAvailable: true,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DEBT,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DETAILS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.RECEIPTS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.SALES,
          isAvailable: false,
        },
      ];

    it('should return true', () => {
      expect(pipe.transform(monthlyTreatmentAvailableEditions)).toBe(true);
    });
  });

  describe('given a list of MonthlyTreatmentAvailableEdition with none being available', () => {
    const monthlyTreatmentAvailableEditions: MonthlyTreatmentAvailableEdition[] =
      [
        {
          id: MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DEBT,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.DETAILS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.RECEIPTS,
          isAvailable: false,
        },
        {
          id: MonthlyTreatmentEditionsEnum.SALES,
          isAvailable: false,
        },
      ];

    it('should return false', () => {
      expect(pipe.transform(monthlyTreatmentAvailableEditions)).toBe(false);
    });
  });

  describe('given a list of undefined available edition', () => {
    const monthlyTreatmentAvailableEditions = undefined;
    it('should return false', () => {
      expect(pipe.transform(monthlyTreatmentAvailableEditions)).toBe(false);
    });
  });

  describe('given a list of null available edition', () => {
    const monthlyTreatmentAvailableEditions = null;
    it('should return false', () => {
      expect(pipe.transform(monthlyTreatmentAvailableEditions)).toBe(false);
    });
  });
});
