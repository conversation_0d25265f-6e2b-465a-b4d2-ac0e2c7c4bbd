/**
    Currently, openapi generator does not support functionnal providers.
    It still uses module to provide the Configuration.
    As a workaround, we got the original mustache tempalte from https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/typescript-angular/api.module.mustache
    and overrided it.
*/

import {
    EnvironmentProviders,
    makeEnvironmentProviders,
    inject,
    FactoryProvider,
    APP_INITIALIZER,
} from '@angular/core';
import { Configuration, ConfigurationParameters } from './configuration';
import { HttpClient } from '@angular/common/http';

const providerNotPresent = <FactoryProvider>{
    provide: APP_INITIALIZER,
    multi: true,
    useFactory: () => {
        const http = inject(HttpClient, { optional: true });

        return (): void => {
            if (!http) {
                throw new Error(
                  'HttpClient has not been provided. You should provide provideHttpClient in the app.config.ts file.'
                );
            }
        };
    },
};

export function provideApi(
    apiUrl: string
): EnvironmentProviders {
  const params: ConfigurationParameters = {
      basePath: apiUrl,
  };

  return makeEnvironmentProviders([
      providerNotPresent,
      { provide: Configuration, useValue: new Configuration(params) },
  ]);
}
