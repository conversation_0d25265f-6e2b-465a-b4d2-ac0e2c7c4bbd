import { DueDateDocumentTypeEnumApi } from '@gc/shared/api/data-access';
import { DueDateDocumentPostStateApi } from './due-date-document-post-state-api.model';
import { DueDateStateApi } from './due-date-state-api.model';

export interface DueDateFilterApi {
  /**
   * Due date number contains value
   */
  numberContains?: string;
  /**
   * Identifier of customer
   */
  customerId?: string;
  /**
   * Identifier of payment
   */
  paymentId?: string;
  /**
   * Identifier od credit note
   */
  creditNoteId?: string;
  /**
   * Identifier of the due date
   */
  dueDateId?: string;
  /**
   * Identifier of the document
   */
  documentId?: string;
  /**
   * Document Type (0-Unknown, 1-Invoice, 2-Credit note, 3-Delivery note, 4-Return note)
   */
  documentType?: DueDateDocumentTypeEnumApi;
  /**
   * Identifier of the company
   */
  companyId?: string;
  /**
   * State of the due date
   */
  dueDateState?: DueDateStateApi;
  /**
   * Post state of the document
   */
  documentPostState?: DueDateDocumentPostStateApi;
  /**
   * First element number (1 = first customer, default=1).
   */
  firstElementNumber?: number;
  /**
   * Number of elemebts per page (default=1000).
   */
  elementsPerPage?: number;
}
