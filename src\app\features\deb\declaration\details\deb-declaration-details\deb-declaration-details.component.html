<ng-container *transloco="let t; read: 'deb.deb-line-preview'">
  @if (debSummaryInfo()?.state === 'added') {
    <p class="information-message">{{ t('added-line-details-Dialog') }}</p>
  } @else if (debSummaryInfo()?.state === 'updated') {
    <p class="information-message">
      {{ t('updated-line-details-Dialog') }}
    </p>
  } @else {
    @if (!isDetailsLoading() && lineDetails()) {
      <mat-card>
        <mat-card-content
          *transloco="let t; read: 'deb.deb-line-preview.table-labels'">
          <table
            mat-table
            [dataSource]="lineDetails()!"
            class="gc-table-cell-center">
            <ng-container matColumnDef="documentType">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('documentType') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{
                  t(
                    'documentTypeValues.' +
                      getDocumentTypeTranslation(lineDetails.documentType)
                  )
                }}
              </td>
            </ng-container>
            <ng-container matColumnDef="customerCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('customerCode') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['customerCode'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="productLabel">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('productLabel') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['productLabel'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="documentNumber">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('documentNumber') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['documentNumber'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="productCode">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('productCode') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['productCode'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="deliveryDate">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('deliveryDate') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['deliveryDate'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="lineAmount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ t('lineAmount') }}
              </th>
              <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
                {{ lineDetails['lineAmount'] + ' €' }}
              </td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </mat-card-content>
      </mat-card>
    } @else if (!isDetailsLoading() && !lineDetails()) {
      <p class="user-feedback-container">{{ t('no-data') }}</p>
    } @else if (isDetailsLoading()) {
      <div class="user-feedback-container">
        <gc-loader [label]="t('is-loading')" />
      </div>
    }
  }
</ng-container>
