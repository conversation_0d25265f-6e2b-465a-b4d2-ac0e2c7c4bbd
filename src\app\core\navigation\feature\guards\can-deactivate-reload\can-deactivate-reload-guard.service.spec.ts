import { waitForAsync } from '@angular/core/testing';
import { DialogService } from '@gc/shared/ui';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Observable, of } from 'rxjs';
import { CanDeactivateReloadGuard } from './can-deactivate-reload-guard.service';
import { LockableComponent } from '@gc/core/navigation/models';

class TestComponentWithoutLock {}

class TestComponentWithLock implements LockableComponent {
  canDeactivate(): boolean {
    return true;
  }
}

describe('CanDeactivateCodeStructureGuard', () => {
  let spectator: SpectatorService<CanDeactivateReloadGuard>;
  let guard: CanDeactivateReloadGuard;
  let confirmSpy: jest.SpyInstance<Observable<boolean>>;
  let selectTranslateSpy: jest.SpyInstance<Observable<string>>;
  let translocoService: TranslocoService;

  let dialogService: DialogService;

  const createService = createServiceFactory({
    service: CanDeactivateReloadGuard,
    mocks: [TranslocoService, DialogService],
  });

  beforeEach(() => {
    spectator = createService();
    guard = spectator.service;
    translocoService = spectator.inject(TranslocoService);
    dialogService = spectator.inject(DialogService);
    confirmSpy = jest.spyOn(dialogService, 'confirm');
    selectTranslateSpy = jest.spyOn(
      translocoService,
      'selectTranslate'
    ) as jest.SpyInstance<Observable<string>>;
  });

  it('should be created', () => {
    expect(spectator).toBeTruthy();
  });

  describe('canDeactivate method', () => {
    describe('Given a state where the selectTranslate method returns a confirm message', () => {
      const confirmMessage = 'confirmMessage';
      beforeEach(() => {
        selectTranslateSpy.mockReturnValue(of(confirmMessage));
      });

      describe('and component implement canDeactivate method return false and user confirm to leave', () => {
        let testComponent: TestComponentWithLock;

        beforeEach(() => {
          testComponent = new TestComponentWithLock();
          testComponent.canDeactivate = jest.fn().mockReturnValue(false);
          confirmSpy.mockReturnValue(of(true));
        });

        it('canDeactivate should return true', waitForAsync(() => {
          guard.canDeactivate(testComponent).subscribe((canDeactivate) => {
            expect(canDeactivate).toBeTruthy();
          });

          expect(selectTranslateSpy).toHaveBeenCalledWith(
            'default-messages.confirm-loss-unsaved-changes',
            {},
            'sharedDialog'
          );
        }));
      });

      describe('and component implement canDeactivate method return false and user cancel', () => {
        let testComponent: TestComponentWithLock;

        beforeEach(() => {
          testComponent = new TestComponentWithLock();
          testComponent.canDeactivate = jest.fn().mockReturnValue(false);
          confirmSpy.mockReturnValue(of(false));
        });

        it('canDeactivate should return false', waitForAsync(() => {
          guard.canDeactivate(testComponent).subscribe((canDeactivate) => {
            expect(canDeactivate).toBeFalsy();
          });

          expect(selectTranslateSpy).toHaveBeenCalledWith(
            'default-messages.confirm-loss-unsaved-changes',
            {},
            'sharedDialog'
          );
        }));
      });

      describe('and component canDeactivate method is not defined', () => {
        let testComponent: TestComponentWithoutLock;

        beforeEach(() => {
          testComponent = new TestComponentWithoutLock();
          confirmSpy.mockReturnValue(of(false));
        });

        it('canDeactivate should return true', waitForAsync(() => {
          guard
            .canDeactivate(testComponent as LockableComponent)
            .subscribe((canDeactivate) => {
              expect(canDeactivate).toBeTruthy();
            });
        }));
      });

      describe('and component implement canDeactivate method return true', () => {
        let testComponent: TestComponentWithLock;

        beforeEach(() => {
          testComponent = new TestComponentWithLock();
          testComponent.canDeactivate = jest.fn().mockReturnValue(true);
          confirmSpy.mockReturnValue(of(false));
        });

        it('canDeactivate should return true', waitForAsync(() => {
          guard.canDeactivate(testComponent).subscribe((canDeactivate) => {
            expect(canDeactivate).toBeTruthy();
          });
        }));
      });
    });
  });
});
