import {
  DunningDueDateToSaveWithFilter,
  DunningDueDateToSave,
  DunningInvoice,
  DunningNextLevelProposal,
  DunningsToSaveAll,
  DunningsToSave,
} from '@gc/dunning/models';
import { DunningListState } from '../stores/dunning-list/models/dunning-list-state.model';
import { DueDateHelper } from '../utils/due-date.helper';
import { DunningLevelHelper } from '../utils/dunning-level.helper';

export class DunningListStateAdapter {
  public static toDunningToSave(
    dunningListState: DunningListState,
    dunningStateNextLevelsProposals: DunningNextLevelProposal[] | undefined
  ): DunningsToSave {
    const nextLevelProposals: DunningNextLevelProposal[] =
      dunningStateNextLevelsProposals ?? [];
    return {
      dueDates: this._toDunningDueDateToSave(
        dunningListState,
        nextLevelProposals
      ),
    };
  }

  public static toDunningToSaveAll(
    dunningListState: DunningListState,
    dunningStateNextLevelsProposals: DunningNextLevelProposal[] | undefined
  ): DunningsToSaveAll {
    const nextLevelProposals: DunningNextLevelProposal[] =
      dunningStateNextLevelsProposals ?? [];
    const dunningToSaveAll: DunningsToSaveAll = {
      dueDateIdsExcluded: this._toDueDateIdsExcluded(dunningListState),
      invoicesDueUpTo:
        dunningListState.filters?.dueDateFrom ??
        dunningListState.defaultDueDateFrom,
      enterpriseIds: dunningListState.filters?.companiesId,
      dueDates: this._toSaveDueDates(dunningListState, nextLevelProposals),
    };
    return dunningToSaveAll;
  }

  private static _toDueDateIdsExcluded(
    dunningListState: DunningListState
  ): string[] {
    return dunningListState.invoices
      .filter(
        (dunningInvoice: DunningInvoice) =>
          !dunningListState.selectedInvoicesIds.includes(dunningInvoice.id)
      )
      .reduce(
        (dueDateIds: string[], invoice: DunningInvoice) =>
          dueDateIds.concat(invoice.dueDatesIds),
        []
      );
  }

  private static _toDunningDueDateToSave(
    dunningListState: DunningListState,
    nextLevelProposals: DunningNextLevelProposal[]
  ): DunningDueDateToSave[] {
    const invoicesDueUpTo =
      dunningListState.filters?.dueDateFrom ??
      dunningListState.defaultDueDateFrom;

    return dunningListState.selectedInvoicesIds
      .reduce((invoices: DunningInvoice[], invoiceId: string) => {
        const invoiceFromId = dunningListState.invoices.find(
          (dunningInvoice: DunningInvoice) => dunningInvoice.id === invoiceId
        );
        if (invoiceFromId) {
          invoices.push(invoiceFromId);
        }
        return invoices;
      }, [])
      .reduce(
        (dueDates: DunningDueDateToSave[], invoice: DunningInvoice) =>
          dueDates.concat(
            DueDateHelper.filterIsDue(
              invoice.dueDatesIds,
              dunningListState.dueDates
            ).map(
              (dueDateId: string) =>
                ({
                  id: dueDateId,
                  invoicesDueUpTo,
                  level:
                    invoice.selectedLevel ??
                    DunningLevelHelper.getNextLevel(
                      invoice.level,
                      nextLevelProposals
                    ),
                }) as unknown as DunningDueDateToSave
            )
          ),
        []
      );
  }

  private static _toSaveDueDates(
    dunningListState: DunningListState,
    nextLevelProposals: DunningNextLevelProposal[]
  ): DunningDueDateToSaveWithFilter[] {
    return dunningListState.invoices
      .filter(
        (invoice: DunningInvoice) =>
          dunningListState.selectedInvoicesIds.indexOf(invoice.id) >= 0 &&
          invoice.selectedLevel !== undefined &&
          invoice.selectedLevel !==
            DunningLevelHelper.getNextLevel(invoice.level, nextLevelProposals)
      )
      .reduce(
        (dueDates: DunningDueDateToSave[], invoice: DunningInvoice) =>
          dueDates.concat(
            DueDateHelper.filterIsDue(
              invoice.dueDatesIds,
              dunningListState.dueDates
            ).map(
              (dueDateId: string) =>
                ({
                  id: dueDateId,
                  level: invoice.selectedLevel,
                }) as unknown as DunningDueDateToSave
            )
          ),
        []
      );
  }
}
