import { CanActivateFn, Router } from '@angular/router';
import { NavigationRightsService } from '../../services/navigation-rights.service';
import { inject } from '@angular/core';
import { iif, of, switchMap, tap } from 'rxjs';
import { URL_PATHS } from '@gc/core/navigation/models';

export const canNavigateToBusinessReview: CanActivateFn = (_route, _state) => {
  const navigationRightsService = inject(NavigationRightsService);
  const router: Router = inject(Router);
  return navigationRightsService
    .canAccessBusinessReview()
    .pipe(
      switchMap((canNavigate) =>
        iif(
          () => canNavigate,
          of(true),
          of(false).pipe(
            tap(() => router.navigate([URL_PATHS.unavailableModule]))
          )
        )
      )
    );
};
