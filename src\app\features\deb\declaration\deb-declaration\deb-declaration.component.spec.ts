import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { DebDeclarationComponent } from './deb-declaration.component';
import { MockDirectives, MockComponents } from 'ng-mocks';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { DebFacade } from '@gc/core/deb/application/facades';
import { SharedUserFacade } from '@gc/core/shared/user/application/facades';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { DebDeclarationDocumentsOverviewComponent } from '../deb-declaration-documents-overview';
import { DebParametersComponent } from '../parameters';
import { DebFiltersComponent } from '../deb-filters/deb-filters.component';
import {
  ClosedMonth,
  DebParameters,
  DraftDeclaration,
} from '@gc/core/deb/domains/models';
import { LetDirective } from '@ngrx/component';
import { CompanyService } from '@gc/company/data-access';
import { CurrencyService } from '@gc/currency/data-access';
import { DialogService, SnackbarService } from '@gc/shared/ui';
import { ResourceState } from '@gc/core/shared/store';
import { signal } from '@angular/core';
import { TranslocoLocaleService } from '@jsverse/transloco-locale';
import { Observable, of } from 'rxjs';

describe('DebDeclarationComponent', () => {
  let spectator: Spectator<DebDeclarationComponent>;
  let component: DebDeclarationComponent;
  let debFacade: DebFacade;
  let translocoService: TranslocoService;
  let translocoLocaleService: TranslocoLocaleService;
  let dialogService: DialogService;

  const createComponent = createComponentFactory({
    component: DebDeclarationComponent,
    declarations: [
      MockDirectives(TranslocoDirective, LetDirective),
      MockComponents(
        CompanySingleSelectComponent,
        DebDeclarationDocumentsOverviewComponent,
        DebParametersComponent,
        DebFiltersComponent
      ),
    ],
    mocks: [
      DebFacade,
      DialogService,
      CompanyService,
      CurrencyService,
      SharedUserFacade,
      SnackbarService,
      TranslocoService,
      TranslocoLocaleService,
      DialogService,
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
    debFacade = spectator.inject(DebFacade);
    translocoService = spectator.inject(TranslocoService);
    translocoLocaleService = spectator.inject(TranslocoLocaleService);
    dialogService = spectator.inject(DialogService);

    const mockDebParametersResourceState: ResourceState<DebParameters> = {
      data: {
        declarationNumber: 123,
      },
      isLoading: false,
      status: 'Success',
      errors: undefined,
    };

    const mockClosedMonthsResourceState: ResourceState<ClosedMonth[]> = {
      data: [
        {
          declarationNumber: 123,
          declarationMonth: new Date('2025-07-01'),
        },
      ],
      isLoading: false,
      status: 'Success',
      errors: undefined,
    };

    component['companyId'].set('COMPANY1');

    (component as any).parameters = signal(mockDebParametersResourceState);
    (component as any).closedMonths = signal(mockClosedMonthsResourceState);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('saveClosure', () => {
    beforeEach(() => {
      component.monthToClose = {
        deliveryNotesIncluded: true,
        invoicesIncluded: false,
        declarations: [],
      };
    });

    it('should call debFacade.saveClosureFor with correct parameters', () => {
      const saveClosure = jest.spyOn(debFacade, 'saveClosureFor');

      (component as any).saveClosure();

      expect(saveClosure).toHaveBeenCalledWith({
        companyId: 'COMPANY1',
        declarationNumber: 123,
        month: new Date('2025-08-01'),
        archiveOnly: false,
        deliveryNotesIncluded: true,
        invoicesIncluded: false,
        declarations: [],
      });
    });
  });

  describe('onDeclarationsChange', () => {
    const mockDraftDeclarations: DraftDeclaration[] = [
      {
        id: 'mock-id-1',
        state: 'initial',
        status: 'enabled',
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 1000,
        statisticalProcedureCode: 'CODE1',
        vatNumber: 'VAT123',
      },
      {
        id: 'mock-id-2',
        state: 'initial',
        status: 'disabled',
        euNomenclature: 'EU456',
        destinationCountryCode: 'DE',
        invoicedAmount: 2000,
        statisticalProcedureCode: 'CODE2',
        vatNumber: 'VAT456',
      },
    ];

    it('should not update stateForClose if it is null', () => {
      component.monthToClose = null;
      component.onDeclarationsChange(mockDraftDeclarations);
      expect(component.monthToClose).toBeNull();
    });

    it('should update stateForClose declarations with enabled declarations only', () => {
      component.monthToClose = {
        deliveryNotesIncluded: false,
        invoicesIncluded: false,
        declarations: [],
      };

      component.onDeclarationsChange(mockDraftDeclarations);

      expect(component.monthToClose.declarations).toHaveLength(1);
      expect(component.monthToClose.declarations![0]).toEqual({
        euNomenclature: 'EU123',
        destinationCountryCode: 'FR',
        invoicedAmount: 1000,
        statisticalProcedureCode: 'CODE1',
        vatNumber: 'VAT123',
      });
    });
  });

  describe('onSaveClosureButtonClick', () => {
    let saveClosureSpy: jest.SpyInstance<void>;
    let getLocaleSpy: jest.SpyInstance<string>;
    let localizeDateSpy: jest.SpyInstance<string>;
    let confirmDialogSpy: jest.SpyInstance<Observable<boolean>>;
    let selectTranslateSpy: jest.SpyInstance<Observable<string>>;
    beforeEach(() => {
      getLocaleSpy = jest.spyOn(
        translocoLocaleService,
        'getLocale'
      ) as jest.SpyInstance<string>;
      localizeDateSpy = jest.spyOn(
        translocoLocaleService,
        'localizeDate'
      ) as jest.SpyInstance<string>;
      selectTranslateSpy = jest.spyOn(
        translocoService,
        'selectTranslate'
      ) as jest.SpyInstance<Observable<string>>;
      confirmDialogSpy = jest.spyOn(
        dialogService,
        'confirm'
      ) as jest.SpyInstance<Observable<boolean>>;
      saveClosureSpy = jest.spyOn(component as any, 'saveClosure');

      getLocaleSpy.mockReturnValue('fr-FR');
      localizeDateSpy.mockReturnValue('février 2024');
      selectTranslateSpy.mockReturnValue(of('Message de confirmation'));
    });

    it('should format date and show confirmation dialog', () => {
      jest.spyOn(dialogService, 'confirm').mockReturnValue(of(true));

      component.onSaveClosureButtonClick();

      expect(localizeDateSpy).toHaveBeenCalledWith(
        new Date('2025-08-01'),
        'fr-FR',
        { year: 'numeric', month: 'long' }
      );

      expect(selectTranslateSpy).toHaveBeenCalledWith(
        'statement-page.closed-month.confirm',
        { month: 'Février 2024' },
        'deb'
      );

      expect(confirmDialogSpy).toHaveBeenCalledWith('Message de confirmation');

      expect(saveClosureSpy).toHaveBeenCalled();
    });

    it('should not call saveClosure when user cancels dialog', () => {
      jest.spyOn(dialogService, 'confirm').mockReturnValue(of(false));

      component.onSaveClosureButtonClick();

      expect(saveClosureSpy).not.toHaveBeenCalled();
    });

    it('should capitalize the first letter of the month', () => {
      jest.spyOn(dialogService, 'confirm').mockReturnValue(of(true));

      jest
        .spyOn(translocoLocaleService, 'localizeDate')
        .mockReturnValue('février 2024');

      component.onSaveClosureButtonClick();

      expect(selectTranslateSpy).toHaveBeenCalledWith(
        'statement-page.closed-month.confirm',
        { month: 'Février 2024' },
        'deb'
      );
    });
  });

  describe('onNoClosedMonthButtonClick', () => {
    it('should call handleFirstDeclarationDialog', () => {
      const handleFirstDeclarationDialogSpy = jest
        .spyOn(component as any, 'handleFirstDeclarationDialog')
        .mockReturnValue(of(true));

      component.onNoClosedMonthButtonClick();

      expect(handleFirstDeclarationDialogSpy).toHaveBeenCalled();
    });
  });

  describe('onParametersButtonClick', () => {
    it('should stop event propagation and prevent default', () => {
      const event = {
        stopPropagation: jest.fn(),
        preventDefault: jest.fn(),
      };

      const openParametersDialogAndSaveSpy = jest
        .spyOn(component as any, 'openParametersDialogAndSave')
        .mockReturnValue(of({}));

      component.onParametersButtonClick(event as any);

      expect(event.stopPropagation).toHaveBeenCalled();
      expect(event.preventDefault).toHaveBeenCalled();
      expect(openParametersDialogAndSaveSpy).toHaveBeenCalledWith({
        declarationNumber: 123,
      });
    });
  });

  describe('onHistoryButtonClick', () => {
    it('should open history dialog with correct parameters', () => {
      const openSpy = jest
        .spyOn(dialogService, 'open')
        .mockReturnValue(of(undefined));

      component.onHistoryButtonClick();

      expect(openSpy).toHaveBeenCalledWith(
        expect.anything(),
        { companyId: 'COMPANY1' },
        expect.objectContaining({
          disableClose: true,
        })
      );
    });
  });

  describe('onApplyFilters', () => {
    it('should set monthToClose with filter values', () => {
      const partialFilters = {
        includeDeliveryNotes: true,
        includeInvoices: false,
      };

      component.onApplyFilters(partialFilters);

      expect(component.monthToClose).toEqual({
        deliveryNotesIncluded: true,
        invoicesIncluded: false,
        declarations: [],
      });
    });

    it('should open parameters dialog if hasMissingParameters is true', () => {
      const partialFilters = {
        includeDeliveryNotes: true,
        includeInvoices: false,
      };

      jest
        .spyOn(component as any, 'hasMissingParameters')
        .mockReturnValue(true);
      const openParametersDialogAndSaveSpy = jest
        .spyOn(component as any, 'openParametersDialogAndSave')
        .mockReturnValue(of({}));

      expect(component.areFiltersApplied()).toBe(false);
      component.onApplyFilters(partialFilters);

      expect(openParametersDialogAndSaveSpy).toHaveBeenCalledWith({
        declarationNumber: 123,
      });
    });

    it('should set filters and load draft declarations if hasMissingParameters is false', () => {
      const partialFilters = {
        includeDeliveryNotes: true,
        includeInvoices: false,
      };

      Object.defineProperty(component, 'hasMissingParameters', {
        value: () => false,
      });

      const loadDraftDeclarationsSpy = jest.spyOn(
        debFacade,
        'loadDraftDeclarationsWithFilters'
      );

      component.onApplyFilters(partialFilters);

      expect(component.filters()).toEqual({
        includeDeliveryNotes: true,
        includeInvoices: false,
        declarationMonth: new Date('2025-08-01'),
        companyId: 'COMPANY1',
      });
      expect(loadDraftDeclarationsSpy).toHaveBeenCalledWith(
        component.filters()
      );
      expect(component.areFiltersApplied()).toBe(true);
    });
  });

  describe('formatMonthToClose', () => {
    it('should format and capitalize the month', () => {
      const date = new Date('2025-08-01');

      jest.spyOn(translocoLocaleService, 'getLocale').mockReturnValue('fr-FR');
      jest
        .spyOn(translocoLocaleService, 'localizeDate')
        .mockReturnValue('août 2025');

      const result = (component as any).formatMonthToClose(date);

      expect(result).toBe('Août 2025');
      expect(translocoLocaleService.localizeDate).toHaveBeenCalledWith(
        date,
        'fr-FR',
        { year: 'numeric', month: 'long' }
      );
    });
  });

  describe('displaySuccessSnackbar', () => {
    it('should call snackBarService.success with correct parameters', () => {
      const successSpy = jest.spyOn(
        spectator.inject(SnackbarService),
        'success'
      );

      (component as any).displaySuccessSnackbar();

      expect(successSpy).toHaveBeenCalledWith({
        key: 'sharedSnackbar.default-message.save',
      });
    });
  });

  describe('initLastClosedMonthSignal', () => {
    it('should return null if no closed months', () => {
      Object.defineProperty(component, 'closedMonths', {
        value: () => ({
          data: [],
          isLoading: false,
          status: 'Success',
          errors: undefined,
        }),
      });

      const result = (component as any).initLastClosedMonthSignal()();

      expect(result).toBeNull();
    });

    it('should return next month of the latest closed month', () => {
      Object.defineProperty(component, 'closedMonths', {
        value: () => ({
          data: [
            {
              declarationNumber: 123,
              declarationMonth: new Date('2025-06-01'),
            },
            {
              declarationNumber: 124,
              declarationMonth: new Date('2025-07-01'),
            },
          ],
          isLoading: false,
          status: 'Success',
          errors: undefined,
        }),
      });

      const result = (component as any).initLastClosedMonthSignal()();

      expect(result.getMonth()).toBe(7); // August (0-indexed)
      expect(result.getFullYear()).toBe(2025);
    });
  });
});
