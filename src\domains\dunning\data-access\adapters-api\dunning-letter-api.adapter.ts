import { DunningLetter } from '@gc/dunning/models';
import { DunningLevelAdapter } from '../adapters/dunning-level.adapter';
import { DunningLetterApi } from '@gc/shared/api/data-access';

export class DunningLetterApiAdapter {
  public static fromApi(dunningLetterApi: DunningLetterApi): DunningLetter {
    const { id, companyId, dunningLevel, text } = dunningLetterApi;
    return {
      id,
      companyId,
      dunningLevel: DunningLevelAdapter.fromApi(dunningLevel),
      text: text ?? '',
    };
  }

  public static toApi(dunningLetter: DunningLetter): DunningLetterApi {
    const { id, companyId, dunningLevel, text } = dunningLetter;
    return {
      id,
      companyId,
      dunningLevel: DunningLevelAdapter.toApi(dunningLevel),
      text,
    };
  }
}
