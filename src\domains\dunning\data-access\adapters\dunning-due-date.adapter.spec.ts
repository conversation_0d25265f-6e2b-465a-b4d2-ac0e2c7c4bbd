import { DueDate } from '@gc/due-date/models';
import { DateAdapter } from '@gc/shared/utils';
import { anyDunningApi } from '../fixtures/dunning-api.fixtures';
import { anyDunningDueDateApi } from '../fixtures/dunning-due-date-api.fixtures';
import { anyDunningDueDate, DunningDueDate } from '@gc/dunning/models';
import { DunningDueDateAdapter } from './dunning-due-date.adapter';
import { DunningApi, DunningDueDateApi } from '@gc/shared/api/data-access';

describe('DunningDueDateAdapter', () => {
  describe('fromDunningDueDateApi', () => {
    describe('given a DunningDueDateApi', () => {
      const dueDateId = '7896-9874-9879-9634';
      const dunningDueDateApi: DunningDueDateApi = {
        id: dueDateId,
        balance: 120,
        date: '1970-12-10',
        dueDateAmount: 200,
        invoiceAmount: 600,
        invoiceDate: '1970-11-10',
        invoiceId: '9999-6666-9999-8888',
        invoiceNumber: '158568566',
      };
      it('should return a DunningDueDate', () => {
        const expectedDunningDueDate: DunningDueDate = {
          id: dueDateId,
          date: DateAdapter.dateFromStringAPI(dunningDueDateApi.date)!,
          balance: 120,
          amount: 200,
          isDue: true,
        };

        expect(
          DunningDueDateAdapter.fromDunningDueDateApi(dunningDueDateApi)
        ).toStrictEqual(expectedDunningDueDate);
      });
    });
  });
  describe('fromDunningsApi', () => {
    describe('given a list of 2 DunningApi with 1 DunningDueDate api each', () => {
      const dunningDueDateApi1: DunningDueDateApi = {
        ...anyDunningDueDateApi,
      };

      const dunningDueDateApi2: DunningDueDateApi = {
        ...anyDunningDueDateApi,
      };

      const dunningApi1: DunningApi = {
        ...anyDunningApi,
        customerId: '7899-9999-2222',
        dueDates: [dunningDueDateApi1],
      };

      const dunningApi2: DunningApi = {
        ...anyDunningApi,
        customerId: '1899-6666-2222',
        dueDates: [dunningDueDateApi2],
      };

      const dunningDueDate1: DunningDueDate = {
        ...anyDunningDueDate,
        id: '4444-888-556-5560',
      };

      const dunningDueDate2: DunningDueDate = {
        ...anyDunningDueDate,
        id: '55555-888-556-5560',
      };

      let fromDunningDueDateApiSpy: jest.SpyInstance<DunningDueDate>;

      beforeEach(() => {
        fromDunningDueDateApiSpy = jest.spyOn(
          DunningDueDateAdapter,
          'fromDunningDueDateApi'
        );
        fromDunningDueDateApiSpy.mockReturnValueOnce(dunningDueDate1);
        fromDunningDueDateApiSpy.mockReturnValueOnce(dunningDueDate2);
      });

      it('should have called fromDunningDueDateApi twice', () => {
        DunningDueDateAdapter.fromDunningsApi([dunningApi1, dunningApi2]);
        expect(fromDunningDueDateApiSpy).toHaveBeenNthCalledWith(
          1,
          dunningApi1.dueDates[0]
        );
        expect(fromDunningDueDateApiSpy).toHaveBeenNthCalledWith(
          2,
          dunningApi2.dueDates[0]
        );
      });

      it('should return two DunningDueDate', () => {
        expect(
          DunningDueDateAdapter.fromDunningsApi([dunningApi1, dunningApi2])
        ).toStrictEqual([dunningDueDate1, dunningDueDate2]);
      });
    });
  });

  describe('fromDueDate', () => {
    describe('given a DueDate', () => {
      const dueDateId = '7896-9874-9879-9634';
      const dueDate: DueDate = {
        id: dueDateId,
        balance: 56,
        date: new Date('1970-12-10'),
        documentDate: new Date('1970-15-10'),
        dueDateAmount: 150,
      };
      it('should return a DunningDueDate', () => {
        const expectedDunningDueDate: DunningDueDate = {
          id: dueDate.id,
          date: dueDate.date,
          balance: 56,
          amount: 150,
        };

        expect(DunningDueDateAdapter.fromDueDate(dueDate)).toStrictEqual(
          expectedDunningDueDate
        );
      });

      it('should left isDue undefined', () => {
        expect(
          DunningDueDateAdapter.fromDueDate(dueDate).isDue
        ).toBeUndefined();
      });
    });
  });
});
