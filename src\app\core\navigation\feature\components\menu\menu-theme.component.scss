@use 'sass:map';
@use '@angular/material' as mat;
@use 'gc-material-theme' as gc-material-theme;
@use 'gc-material-typography' as gc-material-typography;
@use 'gc-colors';

$color-config: mat.m2-get-color-config(gc-material-theme.$theme);
$primary-palette: map.get($color-config, 'primary');
$secondary-palette: map.get($color-config, 'accent');

.container {
    @include mat.m2-typography-level(
        gc-material-typography.$typography,
        'body-1'
    );

    .menu-item,
    .submenu-item {
        color: mat.m2-get-color-from-palette(
            $primary-palette,
            'default-contrast'
        );
        background-color: mat.m2-get-color-from-palette(
            $primary-palette,
            'default'
        );

        &:hover {
            color: mat.m2-get-color-from-palette(
                $secondary-palette,
                'lighter-contrast'
            );
            border-color: mat.m2-get-color-from-palette(
                $primary-palette,
                'lighter'
            );
            background-color: mat.m2-get-color-from-palette(
                $secondary-palette,
                'lighter'
            );
        }
    }
}
