import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
} from '@angular/core';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import {
  monthlyTreatmentActions,
  selectDefaultFiltersLoadingStatus,
  selectSalesByProductCurrentFilter,
} from '@gc/accounting/data-access';
import { MonthlyTreatmentCardComponent } from '@gc/accounting/ui';
import { CharacteristicSelectComponent } from '@gc/product/feature';
import { ProductCharacteristicEnum } from '@gc/product/models';
import {
  LoaderComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import { TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { distinctUntilChanged, filter, Observable, startWith } from 'rxjs';

import { ProductCharacteristicFiltersFormGroup } from './models/product-characteristic-filters-form-group.model';
import { FormGroupManagerService } from './services/form-group-manager.service';
import { Store } from '@ngrx/store';
import { delayInProgress } from '@gc/shared/utils';
import { LoadingStatus } from '@gc/shared/models';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-sales-by-product-category-treatment-id-retriever-card',
  templateUrl:
    './sales-by-product-category-treatment-id-retriever-card.component.html',
  standalone: true,
  styleUrls: [
    './sales-by-product-category-treatment-id-retriever-card.component.scss',
    './sales-by-product-category-treatment-id-retriever-card-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MatIconModule,
    MatOptionModule,
    MatSelectModule,
    MatCheckboxModule,
    MonthlyTreatmentCardComponent,
    TranslocoModule,
    CharacteristicSelectComponent,
    ReactiveFormsModule,
    LoaderComponent,
    PushPipe,
  ],
})
export class SalesByProductCategoryTreatmentIdRetrieverCardComponent
  implements OnInit
{
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _formGroupManagerService = inject(FormGroupManagerService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  @Input() invalid!: boolean;
  @Input() isLoadingData?: boolean;

  @Output() consult = new EventEmitter<(ProductCharacteristicEnum | null)[]>();

  productCharacteristicFG!: ProductCharacteristicFiltersFormGroup | undefined;
  loadingFiltersStatus$!: Observable<LoadingStatus>;

  requiredValidator = Validators.required;
  selectFilters: ProductCharacteristicEnum[] = [];

  ngOnInit(): void {
    this.loadingFiltersStatus$ = this._store
      .select(selectDefaultFiltersLoadingStatus)
      .pipe(delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING));

    this._store
      .select(selectSalesByProductCurrentFilter)
      .pipe(
        filter(
          (salesProductFilter?: (ProductCharacteristicEnum | null)[]) =>
            salesProductFilter !== undefined &&
            salesProductFilter !== this.productCharacteristicFG?.value
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe((defaultCharacteristicsEnum) => {
        this.productCharacteristicFG = this._formGroupManagerService.init(
          defaultCharacteristicsEnum
        );

        this._cdr.markForCheck();
        this.handleRequiredState();
        this.handleSelectFilters();
        this.handleStoreUpdateWhenValueChange();
      });
  }

  handleRequiredState(): void {
    if (!this.productCharacteristicFG)
      throw new Error('productCharacteristicFG is not initialized');

    this.productCharacteristicFG.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef), distinctUntilChanged())
      .subscribe(() => {
        this._formGroupManagerService.switchRequiredValidationState(
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          this.productCharacteristicFG!
        );
        this._cdr.markForCheck();
      });
  }

  handleSelectFilters(): void {
    if (!this.productCharacteristicFG)
      throw new Error('productCharacteristicFG is not initialized');

    this.productCharacteristicFG.valueChanges
      .pipe(
        takeUntilDestroyed(this._destroyRef),
        startWith(this.productCharacteristicFG.value)
      )
      .subscribe(() => {
        this.selectFilters =
          // characteristics can't be undefined since it's always init in ngOnInit
          (this.productCharacteristicFG!.value.characteristics!.filter(
            (productCharacteristicEnum: ProductCharacteristicEnum | null) =>
              productCharacteristicEnum !== null
          ) as ProductCharacteristicEnum[]) ?? [];
        this._cdr.markForCheck();
      });
  }

  handleStoreUpdateWhenValueChange(): void {
    if (!this.productCharacteristicFG)
      throw new Error('productCharacteristicFG is not initialized');

    this.productCharacteristicFG.valueChanges
      .pipe(takeUntilDestroyed(this._destroyRef))
      .subscribe((value) => {
        this._store.dispatch(
          monthlyTreatmentActions.changeSalesByProductCurrentFilter({
            filter: value.characteristics,
          })
        );
      });
  }
}
