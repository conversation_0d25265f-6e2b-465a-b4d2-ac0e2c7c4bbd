<gc-main-container *transloco="let t">
  <div class="container">
    <div class="container-header">
      <gc-dunning-filters
        class="dunning-filters"
        (filtersApplied)="onFiltersApplied(drawer)"
        (openPanelFilters)="drawer.toggle()" />
      <div
        class="action-buttons-container"
        data-testid="action-buttons-container">
        <button
          mat-raised-button
          color="primary"
          [disabled]="isDialogOpened"
          (click)="openSaveDunningDialog()">
          {{ t('dunning.dunning-page.action.remind-customers') }}
        </button>
        <a [routerLink]="'parameter'" mat-raised-button color="primary">
          {{ t('sharedAction.parameters') }}
          <mat-icon>settings</mat-icon>
        </a>
        <gc-dunning-more-actions-button hidden />
      </div>
    </div>
    <mat-drawer-container>
      <mat-drawer #drawer mode="side">
        <button mat-raised-button color="primary" (click)="drawer.close()">
          {{ t('sharedAction.apply-filter') }}
        </button>
      </mat-drawer>

      <mat-drawer-content>
        <div class="gc-dunning-list-container">
          <gc-dunning-list />
        </div>
      </mat-drawer-content>
    </mat-drawer-container>
  </div>
</gc-main-container>
