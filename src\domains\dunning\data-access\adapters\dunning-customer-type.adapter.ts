import { CustomerTypeEnumApi } from '@gc/shared/api/data-access';
import { DunningCustomerType } from '@gc/dunning/models';

export class DunningCustomerTypeAdapter {
  static fromApi(customerTypeAPi: CustomerTypeEnumApi): DunningCustomerType {
    switch (customerTypeAPi) {
      case CustomerTypeEnumApi.Individual:
        return DunningCustomerType.INDIVIDUAL;
      case CustomerTypeEnumApi.Company:
        return DunningCustomerType.COMPANY;
      case CustomerTypeEnumApi.IndividualCompany:
        return DunningCustomerType.INDIVIDUAL_COMPANY;
      default:
        throw new Error('CustomerTypeEnum unknown new value, checks by spec');
    }
  }
}
