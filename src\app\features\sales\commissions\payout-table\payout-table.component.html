<ng-container *transloco="let t; read: 'sales.commissions-tab.payout-table'">
  <div class="header">
    <div class="selected-information">
      {{ selection.selected.length }}
      {{ t('header.selected') }}
    </div>
    <div class="add-commission-button">
      <mat-icon>post_add</mat-icon>
      <span class="add-line">{{ t('header.add') }}</span>
    </div>
  </div>
  @if (currency$ | ngrxPush; as currency) {
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
      <!-- Checkbox Column -->
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox
            (change)="$event ? toggleAllRows() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()"
            [aria-label]="checkboxLabel()">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox
            (click)="$event.stopPropagation()"
            (change)="onRowSelection(row)"
            [checked]="selection.isSelected(row)"
            [aria-label]="checkboxLabel(row)">
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="documentId">
        <th mat-header-cell *matHeaderCellDef>
          {{ t('document.id') }}
        </th>
        <td mat-cell *matCellDef="let element">{{ element.documentId }}</td>
      </ng-container>

      <ng-container matColumnDef="documentDate">
        <th mat-header-cell *matHeaderCellDef>
          {{ t('document.date') | translocoDate }}
        </th>
        <td mat-cell *matCellDef="let element">{{ element.documentDate }}</td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef>
          {{ t('document.amount') }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.amount | amountWithCurrency: currency }}
        </td>
      </ng-container>

      <ng-container matColumnDef="amountToBePaid">
        <th mat-header-cell *matHeaderCellDef>
          {{ t('document.amount-to-be-paid') }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.amountToBePaid | amountWithCurrency: currency }}
        </td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>
          {{ t('document.status') }}
        </th>
        <td mat-cell *matCellDef="let element">
          <div class="progress-bar-container">
            <div class="progress-bar-amount">{{ element.status }}%</div>
            <mat-progress-bar
              color="primary"
              mode="determinate"
              class="mat-progress-bar"
              [value]="element.status">
            </mat-progress-bar>
          </div>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: displayedColumns"
        (click)="selection.toggle(row)"></tr>

      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
        <td mat-cell *matCellDef="let commission; let rowIndex = index">
          <span
            class="actions-container"
            *transloco="let t; read: 'sales.shared'">
            <gc-action-container
              (click)="onPreviewClick($event, commission)"
              [iconeName]="'visibility'"
              tooltipLabel="{{ t('preview') }}" />

            <gc-action-container
              (click)="onEditClick($event, commission)"
              [iconeName]="'mode_edit'"
              tooltipLabel="{{ t('edit') }}" />
          </span>
        </td>
      </ng-container>

      <!-- Footer Row -->
      <ng-container matColumnDef="footer">
        <td
          mat-footer-cell
          *matFooterCellDef
          [attr.colspan]="displayedColumns.length">
          <div class="footer-text">
            <span class="table-footer-text">
              <span class="table-footer-bold"
                >{{ t('footer.total-commissions') }} :</span
              >
              {{ totalCommissions() | amountWithCurrency: currency }}
            </span>
            <span class="table-footer-text">
              <span class="table-footer-bold"
                >{{ t('footer.total-remaining') }} :</span
              >
              {{ totalCommissionsToBePaid() | amountWithCurrency: currency }}
            </span>
          </div>
        </td>
      </ng-container>
      <tr
        mat-footer-row
        *matFooterRowDef="['footer']"
        class="sticky-footer"></tr>
    </table>
  } @else if (defaultCurrencyLoadingStatus$ | ngrxPush) {
    <div class="user-feedback-container">
      <gc-loader [label]="t('sales.shared.is-loading')" />
    </div>
  }
</ng-container>
