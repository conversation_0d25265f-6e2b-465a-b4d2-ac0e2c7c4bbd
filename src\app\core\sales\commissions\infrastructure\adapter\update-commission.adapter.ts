import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CommissionsApiService } from '../api';
import { Commission } from '../../domains/models';
import { UpdateCommissionPort } from '../../domains/ports/update-commission.port';

@Injectable()
export class UpdateCommissionAdapter implements UpdateCommissionPort {
  private readonly api = inject(CommissionsApiService);

  with(commission: Commission): Observable<Commission[]> {
    return this.api.updateCommission(commission);
  }
}
