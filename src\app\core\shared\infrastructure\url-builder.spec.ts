import { UrlBuilder } from './url-builder';

describe('UrlBuilder', () => {
  describe('create', () => {
    it('should create a new UrlBuilder instance', () => {
      const builder = UrlBuilder.create('https://example.com');
      expect(builder).toBeInstanceOf(UrlBuilder);
    });
  });

  describe('withRouteParam', () => {
    it('should add a single route parameter', () => {
      const url = UrlBuilder.create('https://example.com')
        .withRouteParam('users')
        .build();

      expect(url).toBe('https://example.com/users');
    });

    it('should add multiple route parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withRouteParam('users')
        .withRouteParam('123')
        .withRouteParam('profile')
        .build();

      expect(url).toBe('https://example.com/users/123/profile');
    });
  });

  describe('withQueryParam', () => {
    it('should add a single query parameter', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('page', 1)
        .build();

      expect(url).toBe('https://example.com?page=1');
    });

    it('should add multiple query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('page', 1)
        .withQueryParam('limit', 10)
        .withQueryParam('sort', 'name')
        .build();

      expect(url).toBe('https://example.com?page=1&limit=10&sort=name');
    });

    it('should convert non-string values to strings', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('number', 42)
        .withQueryParam('boolean', true)
        .build();

      expect(url).toBe('https://example.com?number=42&boolean=true');
    });

    it('should not add query parameters with null or undefined values', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('nullValue', null)
        .withQueryParam('undefinedValue', undefined)
        .withQueryParam('validValue', 'test')
        .build();

      expect(url).toBe('https://example.com?validValue=test');
    });
  });

  describe('combined usage', () => {
    it('should combine route parameters and query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withRouteParam('api')
        .withRouteParam('users')
        .withQueryParam('page', 1)
        .withQueryParam('limit', 10)
        .build();

      expect(url).toBe('https://example.com/api/users?page=1&limit=10');
    });

    it('should handle special characters in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('search', 'user name')
        .withQueryParam('filter', 'status=active')
        .build();

      expect(url).toBe(
        'https://example.com?search=user%20name&filter=status%3Dactive'
      );
    });
  });
  describe('edge cases', () => {
    it('should not add undefined value in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('param', undefined)
        .build();

      expect(url).toBe('https://example.com');
    });

    it('should not add null value in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('param', null)
        .build();

      expect(url).toBe('https://example.com');
    });

    it('should add 0 value properly in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('param', 0)
        .build();

      expect(url).toBe('https://example.com?param=0');
    });

    it('should add empty string as a slash in route parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withRouteParam('')
        .build();

      expect(url).toBe('https://example.com/');
    });

    it('should handle array values by adding each item as a route parameter', () => {
      const items = ['users', '123'];
      let builder = UrlBuilder.create('https://example.com');

      // Add each item in the array as a route parameter
      items.forEach((item) => {
        builder = builder.withRouteParam(item);
      });

      const url = builder.build();
      expect(url).toBe('https://example.com/users/123');
    });

    it('should not add items from an empty array as route parameters', () => {
      const items: string[] = [];
      const builder = UrlBuilder.create('https://example.com');

      // Try to add items from empty array (this loop won't execute)

      const url = builder.build();
      expect(url).toBe('https://example.com');
    });

    it('should not add empty string in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('param', '')
        .build();

      expect(url).toBe('https://example.com');
    });

    it('should handle boolean false value in query parameters', () => {
      const url = UrlBuilder.create('https://example.com')
        .withQueryParam('param', false)
        .build();

      expect(url).toBe('https://example.com?param=false');
    });

    it('should handle multiple edge cases together', () => {
      const url = UrlBuilder.create('https://example.com')
        .withRouteParam('api')
        // No empty route param to avoid the extra slash
        .withQueryParam('undefined', undefined)
        .withQueryParam('null', null)
        .withQueryParam('empty', '')
        .withQueryParam('zero', 0)
        .withQueryParam('false', false)
        .build();

      expect(url).toBe('https://example.com/api?zero=0&false=false');
    });
  });
});
