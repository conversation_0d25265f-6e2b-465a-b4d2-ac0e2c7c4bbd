import { Component, OnInit, inject } from '@angular/core';
import {
  StimulsoftNavigationService,
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
} from '@gc/shared/stimulsoft/models';
import { Observable, combineLatest, switchMap } from 'rxjs';
import { BusinessReviewStimulsoftNavigationService } from './services/business-review-stimulsoft-navigation.service';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { NavigationBackContainerComponent } from '@gc/core/navigation/feature';
import { PushPipe } from '@ngrx/component';
import { BusinessReviewSupplementaryStimulsoftProperties } from './models/business-review-supplementary-stimulsoft-properties.model';

type StimulsoftViewerProperties = CoreStimulsoftProperties &
  CommonStimulsoftProperties &
  BusinessReviewSupplementaryStimulsoftProperties;

@Component({
  selector: 'gc-business-review-viewer',
  standalone: true,
  imports: [
    TranslocoModule,
    NavigationBackContainerComponent,
    StimulsoftViewerContainerComponent,
    PushPipe,
  ],
  templateUrl: './business-review-viewer.component.html',
  styleUrl: './business-review-viewer.component.scss',
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'business-review',
      multi: true,
    },
  ],
})
export class BusinessReviewViewerComponent implements OnInit {
  private readonly _stimulsoftPropertiesService = inject(
    StimulsoftPropertiesService
  );
  private readonly _stimulsoftNavigationService = inject(
    StimulsoftNavigationService
  );
  private readonly _businessReviewStimulsoftNavigationService = inject(
    BusinessReviewStimulsoftNavigationService
  );

  stimulsoftProperties$!: Observable<StimulsoftViewerProperties>;

  ngOnInit(): void {
    this.stimulsoftProperties$ = combineLatest([
      this._stimulsoftNavigationService.getCommonStimulsoftPropertiesFromQueryParams$(),
      this._businessReviewStimulsoftNavigationService.getBusinessReviewStimulsoftPropertiesFromQueryParams$(),
    ]).pipe(
      switchMap(
        ([commonStimulsoftProperties, businessReviewStimulsoftProperties]: [
          CommonStimulsoftProperties,
          BusinessReviewSupplementaryStimulsoftProperties,
        ]) =>
          this._stimulsoftPropertiesService.createStimulsoftProperties$(
            commonStimulsoftProperties.reportId,
            commonStimulsoftProperties.reportFamily,
            businessReviewStimulsoftProperties
          )
      )
    );
  }
}
