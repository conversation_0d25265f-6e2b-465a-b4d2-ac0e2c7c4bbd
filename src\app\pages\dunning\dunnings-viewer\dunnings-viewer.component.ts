import { Component, HostListener, OnInit, inject } from '@angular/core';
import { NavigationBackContainerComponent } from '@gc/core/navigation/feature';
import {
  dunningViewerActions,
  selectSaveResult,
} from '@gc/dunning/data-access';
import { DunningsSaveResult } from '@gc/dunning/models';
import {
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CommonStimulsoftProperties,
  CoreStimulsoftProperties,
  REPORT_ID,
  ReportFamilies,
} from '@gc/shared/stimulsoft/models';
import { TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { filter, map, Observable, switchMap, take, tap } from 'rxjs';

export interface DunningSupplementaryStimulsoftProperties {
  dunningTreatmentId: string;
}

type StimulsoftViewerProperties = CoreStimulsoftProperties &
  CommonStimulsoftProperties &
  DunningSupplementaryStimulsoftProperties;

@Component({
  selector: 'gc-dunnings-viewer',
  templateUrl: './dunnings-viewer.component.html',
  styleUrls: ['./dunnings-viewer.component.scss'],
  standalone: true,
  imports: [
    TranslocoModule,
    NavigationBackContainerComponent,
    StimulsoftViewerContainerComponent,
    PushPipe,
  ],
})
export class DunningsViewerComponent implements OnInit {
  private readonly _stimulsoftPropertiesService = inject(
    StimulsoftPropertiesService
  );
  private readonly _store = inject(Store);

  selectSaveResult$: Observable<DunningsSaveResult | undefined> =
    this._store.select(selectSaveResult);

  stimulsoftProperties$!: Observable<StimulsoftViewerProperties>;

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    event.returnValue = true;
  }

  ngOnInit(): void {
    this.stimulsoftProperties$ =
      this._getDunningSupplementaryStimulsoftProperties$().pipe(
        switchMap(
          (
            dunningSupplementaryStimulsoftProperties: DunningSupplementaryStimulsoftProperties
          ) =>
            this._stimulsoftPropertiesService.createStimulsoftProperties$(
              REPORT_ID.dunning,
              ReportFamilies.DUNNING,
              dunningSupplementaryStimulsoftProperties
            )
        ),
        tap(() =>
          this._store.dispatch(dunningViewerActions.deleteDunningSaveResult())
        )
      );
  }

  private _getDunningSupplementaryStimulsoftProperties$(): Observable<DunningSupplementaryStimulsoftProperties> {
    return this.selectSaveResult$.pipe(
      filter(Boolean),
      map((saveResult: DunningsSaveResult) => {
        return {
          dunningTreatmentId: saveResult.dunningTreatmentId,
        } as DunningSupplementaryStimulsoftProperties;
      }),
      take(1)
    );
  }
}
