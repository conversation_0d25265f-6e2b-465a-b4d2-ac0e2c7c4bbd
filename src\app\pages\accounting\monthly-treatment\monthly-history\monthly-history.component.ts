import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  inject,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import {
  monthlyTreatmentHistoryActions,
  MonthlyTreatmentHistoryStoreModule,
  MonthlyTreatmentService,
  MonthlyTreatmentStoreModule,
  selectCompanyIdAndSelectedEnclosedMonth,
  selectHasEnclosedMonths,
  selectHasNotEditionToConsult,
  selectIsNewestEnclosedMonthSelected,
  selectSelectedEnclosedMonth,
  selectSelectedMonthlyEditions,
  selectUncloseStatus,
} from '@gc/accounting/data-access';
import {
  AvailableEditionsSelectionComponent,
  ClosedMonthSelectionComponent,
} from '@gc/accounting/feature';
import { ProcessStatus } from '@gc/shared/models';
import {
  CapitalizePipe,
  DialogService,
  LoaderComponent,
  MasterDetailPanelsLayoutComponent,
  MIN_DELAY_TO_AVOID_LOADER_FLICKING,
} from '@gc/shared/ui';
import { delayInProgress } from '@gc/shared/utils';
import { TranslocoModule, TranslocoService } from '@jsverse/transloco';
import {
  TranslocoDatePipe,
  TranslocoLocaleService,
} from '@jsverse/transloco-locale';
import { LetDirective, PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { filter, map, Observable, switchMap, take, tap } from 'rxjs';
import { IDocumentViewerManager } from '@isagri-ng/document-viewer';
import {
  MonthlyAccountingReportDocumentsIds,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';

const reportMap: Record<
  MonthlyTreatmentEditionsEnum,
  keyof MonthlyAccountingReportDocumentsIds
> = {
  [MonthlyTreatmentEditionsEnum.SALES]: 'categorySalesId',
  [MonthlyTreatmentEditionsEnum.DETAILS]: 'saleDetailsId',
  [MonthlyTreatmentEditionsEnum.RECEIPTS]: 'payementListingId',
  [MonthlyTreatmentEditionsEnum.DEBT]: 'unpaidListingId',
  [MonthlyTreatmentEditionsEnum.ACCOUNT_ENTRY]: 'accountEntryId',
};

@Component({
  selector: 'gc-monthly-history',
  templateUrl: './monthly-history.component.html',
  standalone: true,
  styleUrls: ['./monthly-history.component.scss'],
  imports: [
    TranslocoModule,
    MasterDetailPanelsLayoutComponent,
    CapitalizePipe,
    TranslocoDatePipe,
    MonthlyTreatmentStoreModule,
    MonthlyTreatmentHistoryStoreModule,
    ClosedMonthSelectionComponent,
    AvailableEditionsSelectionComponent,
    MatDividerModule,
    LoaderComponent,
    LetDirective,
    MatButtonModule,
    PushPipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MonthlyHistoryComponent implements OnInit {
  private readonly _store = inject(Store);
  private readonly _dialogService = inject(DialogService);
  private readonly _documentViewerManager = inject(IDocumentViewerManager);
  private readonly _translocoService = inject(TranslocoService);
  private readonly _translocoLocaleService = inject(TranslocoLocaleService);
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);

  selectedEnclosedMonth$ = this._store.select(selectSelectedEnclosedMonth);
  hasEnclosedMonths$ = this._store.select(selectHasEnclosedMonths);

  isNewestEnclosedMonthSelected$!: Observable<boolean>;
  hasNotEditionToConsult: Observable<boolean> = this._store.select(
    selectHasNotEditionToConsult
  );

  uncloseStatus$!: Observable<ProcessStatus>;

  ngOnInit(): void {
    this.isNewestEnclosedMonthSelected$ = this._store.select(
      selectIsNewestEnclosedMonthSelected
    );

    this.uncloseStatus$ = this._store
      .select(selectUncloseStatus)
      .pipe(delayInProgress(MIN_DELAY_TO_AVOID_LOADER_FLICKING));
  }

  openEditions(): void {
    this._getSelectedReportIds()
      .pipe(take(1))
      .subscribe((selectedReportsIds) =>
        selectedReportsIds.forEach((reportId) =>
          this._documentViewerManager.open(reportId)
        )
      );
  }

  uncloseSelectedMonth(): void {
    this._store
      .select(selectSelectedEnclosedMonth)
      .pipe(
        switchMap((monthToUnclose) =>
          this._getConfirmUncloseMessage$(monthToUnclose!)
        ),
        switchMap((messageConfirm: string) =>
          this._dialogService.confirm(messageConfirm)
        ),
        tap((hasConfirmed) => {
          if (hasConfirmed) {
            this._store.dispatch(
              monthlyTreatmentHistoryActions.uncloseSelectedMonth()
            );
          }
        }),
        take(1)
      )
      .subscribe();
  }

  private _getConfirmUncloseMessage$(monthToUnclose: Date): Observable<string> {
    const monthDateFormatted = this._translocoLocaleService.localizeDate(
      monthToUnclose,
      this._translocoLocaleService.getLocale(),
      { year: 'numeric', month: 'long' }
    );

    const monthDateFormattedAndCapitalized =
      monthDateFormatted.charAt(0).toUpperCase() + monthDateFormatted.slice(1);

    return this._translocoService.selectTranslate(
      'accounting.monthly-edition-history-tab.confirm-unclose',
      { month: monthDateFormattedAndCapitalized }
    );
  }

  private _getSelectedReportIds(): Observable<string[]> {
    return this._store.select(selectCompanyIdAndSelectedEnclosedMonth).pipe(
      take(1),
      switchMap(({ companyId, selectedEnclosedMonth }) =>
        this._monthlyTreatmentService.getExistingReportsIds(
          companyId!,
          selectedEnclosedMonth!
        )
      ),
      filter(Boolean),
      switchMap((ids) =>
        this._store.select(selectSelectedMonthlyEditions).pipe(
          take(1),
          map((selectedMonthlyEditions) =>
            selectedMonthlyEditions
              .map((edition) => ids?.[reportMap[edition]])
              .filter((reportId): reportId is string => !!reportId)
          )
        )
      )
    );
  }
}
