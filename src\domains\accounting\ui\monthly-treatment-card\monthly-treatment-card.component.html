<div class="card-edition" *transloco="let t; read: 'accounting'">
  <div class="header">
    <ng-content select="[header]" />
  </div>

  <div class="body">
    <ng-content />
  </div>

  <div class="actions-container">
    <button
      [disabled]="disabled"
      mat-raised-button
      color="primary"
      type="submit"
      (click)="handleVisualization.emit()">
      {{ t('monthly-edition-history-tab.visualize') }}
      <mat-icon iconPositionEnd>open_in_new</mat-icon>
    </button>
  </div>
</div>
