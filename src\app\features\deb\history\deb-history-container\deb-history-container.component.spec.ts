import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebHistoryContainerComponent } from './deb-history-container.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebHistoryDetailsComponent } from '../deb-history-details/deb-history-details.component';

describe('DebHistoryContainerComponent', () => {
  let spectator: Spectator<DebHistoryContainerComponent>;
  let component: DebHistoryContainerComponent;

  const dialogRefMock = {
    close: jest.fn(),
  };

  const dialogDataMock = {
    companyId: '123456',
  };

  const createComponent = createComponentFactory({
    component: DebHistoryContainerComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(DebHistoryDetailsComponent),
    ],
    providers: [
      { provide: MatDialogRef, useValue: dialogRefMock },
      { provide: MAT_DIALOG_DATA, useValue: dialogDataMock },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('closeDialog', () => {
    it('should call dialogRef.close', () => {
      component.closeDialog();
      expect(dialogRefMock.close).toHaveBeenCalled();
    });
  });

  describe('onCloseButtonClick', () => {
    it('should prevent default, stop propagation and close dialog', () => {
      const mockEvent = {
        preventDefault: jest.fn(),
        stopPropagation: jest.fn(),
      } as unknown as MouseEvent;

      component.onCloseButtonClick(mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockEvent.stopPropagation).toHaveBeenCalled();
      expect(dialogRefMock.close).toHaveBeenCalled();
    });
  });
});
