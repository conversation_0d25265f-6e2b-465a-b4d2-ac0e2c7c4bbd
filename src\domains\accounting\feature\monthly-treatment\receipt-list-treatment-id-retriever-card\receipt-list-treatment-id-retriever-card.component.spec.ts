import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';

import { ReceiptListTreatmentIdRetrieverCardComponent } from './receipt-list-treatment-id-retriever-card.component';

describe('ReceiptListTreatmentIdRetrieverCardComponent', () => {
  let spectator: Spectator<ReceiptListTreatmentIdRetrieverCardComponent>;
  let component: ReceiptListTreatmentIdRetrieverCardComponent;

  const createComponent = createComponentFactory({
    component: ReceiptListTreatmentIdRetrieverCardComponent,
    mocks: [TranslocoService, Store],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
