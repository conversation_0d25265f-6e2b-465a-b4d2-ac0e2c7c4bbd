import { inject, Injectable } from '@angular/core';
import { DebDetails, LoadDetailsParameters } from '@gc/core/deb/domains/models';
import { LoadDetailsPort } from '@gc/core/deb/domains/ports';
import { Observable } from 'rxjs';

@Injectable()
export class LoadDetailsUseCase {
  private readonly loadDetails = inject(LoadDetailsPort);

  for(parameters: LoadDetailsParameters): Observable<DebDetails[]> {
    return this.loadDetails.for(parameters);
  }
}
