import { UpperCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { TRANSLOCO_SCOPE, TranslocoDirective } from '@jsverse/transloco';
import { PayoutTableComponent } from './payout-table/payout-table.component';
import { FiltersComponent } from './filters/filters.component';
import { PayoutSummaryFooterComponent } from './payout-summary-footer/payout-summary-footer.component';
import { CurrencyService } from '@gc/currency/data-access';
import { LetDirective } from '@ngrx/component';

@Component({
  selector: 'gc-commissions',
  standalone: true,
  templateUrl: './commissions.component.html',
  styleUrl: './commissions.component.scss',
  imports: [
    TranslocoDirective,
    UpperCasePipe,
    LetDirective,
    MatTabsModule,
    MatButtonModule,
    MainContainerComponent,
    FiltersComponent,
    PayoutTableComponent,
    PayoutSummaryFooterComponent,
  ],
  providers: [
    { provide: TRANSLOCO_SCOPE, useValue: 'sales', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'company', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/action', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/dialog', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/form', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/snackbar', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/errors', multi: true },
    { provide: TRANSLOCO_SCOPE, useValue: 'shared/loader', multi: true },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommissionsComponent {
  private readonly currencyService = inject(CurrencyService);

  currency$ = this.currencyService.getDefaultCurrency$();

  amountToPay: number | null = null;

  setAmountToPay(amount: number | null) {
    this.amountToPay = amount;
  }
}
