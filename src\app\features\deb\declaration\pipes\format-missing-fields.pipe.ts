import { Pipe, PipeTransform } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';

@Pipe({
  name: 'formatMissingFields',
  standalone: true,
})
export class FormatMissingFieldsPipe implements PipeTransform {
  constructor(private readonly translocoService: TranslocoService) {}

  transform(missingFields: string[] | undefined | null): string | null {
    if (!missingFields || missingFields.length === 0) {
      return null;
    }

    const missingFieldsTranslation = this.translocoService.translate(
      'deb.deb-declaration-table-component.errors.missing-fields'
    );
    const formattedFields = missingFields
      .map((field) => `- ${field}`)
      .join('\n');

    return `${missingFieldsTranslation} :\n${formattedFields}`;
  }
}
