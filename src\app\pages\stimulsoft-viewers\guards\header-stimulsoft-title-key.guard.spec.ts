import { TestBed } from '@angular/core/testing';

import {
  ActivatedRouteSnapshot,
  Router,
  RouterModule,
  RouterStateSnapshot,
} from '@angular/router';
import { HeaderTitleService } from '@gc/core/navigation/feature';
import { TitleKeyHeader, URL_PATHS } from '@gc/core/navigation/models';
import { ReportFamilies } from '@gc/shared/stimulsoft/models';
import { TranslocoModule } from '@jsverse/transloco';
import { MockProvider } from 'ng-mocks';
import { STIMULSOFT_REPORT_FAMILY_TO_HEADER_TRANSLATION_KEY_MAP } from '../constants/stimulsoft-viewer-translation-keys-from-report-family.constant';
import { setStimulsoftHeaderTitleGuard } from './header-stimulsoft-title-key.guard';

describe('setStimulsoftHeaderTitleGuard function', () => {
  let router: Router;
  let navigateSpy: jest.SpyInstance<Promise<boolean>>;
  let headerTitleService: HeaderTitleService;
  let headerTitleServiceSpy: jest.SpyInstance<void, [titleKey: string], any>;
  let getStimulsoftReportFamilyToHeaderTranslationKeyMapSpy: jest.SpyInstance<
    TitleKeyHeader | undefined,
    [key: ReportFamilies],
    any
  >;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterModule, TranslocoModule],
      providers: [MockProvider(HeaderTitleService)],
    });

    router = TestBed.inject(Router);

    navigateSpy = jest.spyOn(router, 'navigate').mockResolvedValue(true);

    headerTitleService = TestBed.inject(HeaderTitleService);
    headerTitleServiceSpy = jest.spyOn(
      headerTitleService,
      'updateCurrentTitle'
    );
    getStimulsoftReportFamilyToHeaderTranslationKeyMapSpy = jest.spyOn(
      STIMULSOFT_REPORT_FAMILY_TO_HEADER_TRANSLATION_KEY_MAP,
      'get'
    );
  });

  describe(`given a state where ActivatedRouteSnapshot has a reportFamily queryParams`, () => {
    describe(`and the queryParams value is a key of the stimulsoftReportFamilyToHeaderTranslationKeyMap`, () => {
      let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
      beforeEach(() => {
        activatedRouteSnapshot = {
          queryParams: {
            reportFamily: 'BRGLT',
          },
        };
        getStimulsoftReportFamilyToHeaderTranslationKeyMapSpy.mockReturnValue(
          TitleKeyHeader.STIMULSOFT_VIEWER_DEPOSIT_SLIP
        );
      });

      it('should return true and NOT call navigate method of Router', async () => {
        const isSetStimulsoftHeaderTitle = TestBed.runInInjectionContext(() => {
          return setStimulsoftHeaderTitleGuard(
            activatedRouteSnapshot as ActivatedRouteSnapshot,
            {} as RouterStateSnapshot
          );
        });

        expect(navigateSpy).not.toHaveBeenCalled();
        expect(isSetStimulsoftHeaderTitle).toBe(true);
      });

      it('should call headerTitleService for update the header title', async () => {
        TestBed.runInInjectionContext(() => {
          return setStimulsoftHeaderTitleGuard(
            activatedRouteSnapshot as ActivatedRouteSnapshot,
            {} as RouterStateSnapshot
          );
        });

        expect(headerTitleServiceSpy).toHaveBeenCalledWith(
          TitleKeyHeader.STIMULSOFT_VIEWER_DEPOSIT_SLIP
        );
      });
    });

    describe(`and the queryParams value is NOT a key of the stimulsoftReportFamilyToHeaderTranslationKeyMap`, () => {
      let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
      beforeEach(() => {
        activatedRouteSnapshot = {
          queryParams: {
            reportFamily: 'UNKNOWN_REPORT_FAMILY',
          },
        };
      });

      it('should navigate to invalid-config url and throw Error', async () => {
        TestBed.runInInjectionContext(() => {
          try {
            setStimulsoftHeaderTitleGuard(
              activatedRouteSnapshot as ActivatedRouteSnapshot,
              {} as RouterStateSnapshot
            );
          } catch (err) {
            expect(err).toStrictEqual(
              new Error(
                'setStimulsoftHeaderTitleGuard: headerTranslationKey is undefined'
              )
            );
            expect(navigateSpy).toHaveBeenCalledWith([URL_PATHS.invalidConfig]);
          }
        });
      });
    });
  });
});
