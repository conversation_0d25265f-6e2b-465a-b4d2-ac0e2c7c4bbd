import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { PayoutSummaryFooterComponent } from './payout-summary-footer.component';
import { MockDirectives, MockPipes } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { AmountWithCurrencyPipe } from '@gc/currency/ui';

describe('PayoutSummaryFooterComponent', () => {
  let spectator: Spectator<PayoutSummaryFooterComponent>;
  const createComponent = createComponentFactory({
    component: PayoutSummaryFooterComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockPipes(AmountWithCurrencyPipe),
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });
});
