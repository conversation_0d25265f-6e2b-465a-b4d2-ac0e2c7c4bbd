import { Currency } from '@gc/shared/models';
import { CurrencyApi } from '@gc/shared/api/data-access';

export class CurrencyAdapter {
  public static fromApi(currencyApi: CurrencyApi): Currency {
    if (!currencyApi.code || !currencyApi.symbol) {
      throw new Error();
    }
    const { code, symbol, usualPrecision } = currencyApi;
    return {
      code,
      symbol,
      usualPrecision,
    };
  }
}
