import { Spectator } from '@ngneat/spectator';
import { createComponentFactory } from '@ngneat/spectator/jest';
import { TranslocoDirective } from '@jsverse/transloco';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { CodeDesignationComponent } from './code-designation.component';
import { DesignationsFormComponent } from '@gc/product/feature';
import { BlockReloadDirective } from '@gc/core/navigation/feature';

const designationsFormComponentWithModification: Partial<DesignationsFormComponent> =
  {
    hasModifications(): boolean {
      return true;
    },
  };

const designationsFormComponentWithoutModification: Partial<DesignationsFormComponent> =
  {
    hasModifications(): boolean {
      return false;
    },
  };
describe('CodeDesignationComponent', () => {
  let spectator: Spectator<CodeDesignationComponent>;
  let component: CodeDesignationComponent;
  const createComponent = createComponentFactory({
    component: CodeDesignationComponent,
    declarations: [
      MockDirectives(TranslocoDirective, BlockReloadDirective),
      MockComponents(DesignationsFormComponent),
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
    ({ component } = spectator);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('canDeactivate', () => {
    describe('given state where hasModifications return true', () => {
      describe('when method is called', () => {
        it('should return false', () => {
          component.designationsFormComponent =
            designationsFormComponentWithModification as DesignationsFormComponent;
          expect(component.canDeactivate()).toBe(false);
        });
      });
    });

    describe('given state where hasModifications return false', () => {
      describe('when method is called', () => {
        it('should return true', () => {
          component.designationsFormComponent =
            designationsFormComponentWithoutModification as DesignationsFormComponent;

          expect(component.canDeactivate()).toBe(true);
        });
      });
    });
  });
});
