<form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
  <div
    class="form-inputs-container"
    *transloco="let t; read: 'deb.deb-closed-month-init-dialog'">
    <div class="input-container">
      <label for="">{{ t('label.select.declaration-number') }}</label>

      <mat-form-field appearance="outline" subscriptSizing="dynamic">
        <mat-label>
          {{ t('label.declaration-number') }}
        </mat-label>
        <input
          matInput
          type="number"
          formControlName="declarationNumber"
          name="declarationNumber" />
        @if (formGroup.controls.declarationNumber.hasError('min')) {
          <mat-error>
            {{
              t('errors.greater-than', {
                min: minimumDeclarationNumberValue - 1,
              })
            }}
          </mat-error>
        }
      </mat-form-field>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="input-container">
      <label for="">{{ t('label.select.declaration-month') }}</label>

      <mat-form-field appearance="outline" subscriptSizing="dynamic">
        <mat-select formControlName="declarationMonth">
          @for (month of lastSixMonths; track month) {
            <mat-option [value]="month">
              {{
                month
                  | translocoDate: { year: 'numeric', month: 'long' }
                  | capitalize
              }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <p class="error" *transloco="let t">
    @if (formGroup.invalid && (formGroup.dirty || formGroup.touched)) {
      <mat-error>{{ t('sharedErrors.form.required') }}</mat-error>
    }
  </p>

  <div *transloco="let t" class="action-button-container">
    <button
      mat-stroked-button
      color="primary"
      type="button"
      cdkFocusInitial
      (click)="cancel()">
      {{ t('sharedAction.cancel') }}
    </button>
    <button
      mat-raised-button
      color="primary"
      type="submit"
      [disabled]="!formGroup.dirty || !formGroup.valid">
      {{ t('sharedAction.validate') }}
    </button>
  </div>
</form>
