import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { RangeFormGroup } from '@gc/accounting/models';
import {
  differentMonthOrYearValidator,
  monthAlreadyClosedValidator,
  startDateAfterEndDateValidator,
} from '@gc/shared/form/utils';
import { MatDatepickerDirective } from '@isagri-ng/ui/angular-material';
import { TranslocoModule } from '@jsverse/transloco';
import { PushPipe } from '@ngrx/component';
import { ToErrorKeyPipe } from './pipe/to-error-key.pipe';

@Component({
  selector: 'gc-monthly-treatment-range[rangeFormGroup]',
  standalone: true,
  imports: [
    MatFormFieldModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatDatepickerDirective,
    MatInputModule,
    TranslocoModule,
    ToErrorKeyPipe,
    PushPipe,
  ],
  templateUrl: './monthly-treatment-range.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MonthlyTreatmentRangeComponent implements OnChanges {
  @Input() rangeFormGroup!: FormGroup<RangeFormGroup>;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['rangeFormGroup'] && this.rangeFormGroup) {
      this.rangeFormGroup.controls.end.addValidators([
        differentMonthOrYearValidator('start', 'end'),
        monthAlreadyClosedValidator('start', 'end'),
        startDateAfterEndDateValidator('start', 'end'),
      ]);
    }
  }
}
