import { inject, Injectable } from '@angular/core';
import { GetDestinationCountryCodesPort } from '@gc/core/deb/domains/ports';
import { map } from 'rxjs';
import { DebApiService } from '../api';

@Injectable()
export class GetDestinationCountryCodesAdapter
  implements GetDestinationCountryCodesPort
{
  private readonly api = inject(DebApiService);

  for(companyId: string, month: Date) {
    return this.api.getDestinationCountries(companyId, month).pipe(
      map((response) => {
        return response.map((country) => country.code);
      })
    );
  }
}
