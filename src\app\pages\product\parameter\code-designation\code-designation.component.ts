import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  ViewChild,
} from '@angular/core';
import { BlockReloadDirective } from '@gc/core/navigation/feature';
import {
  LOCKABLE_COMPONENT_TOKEN,
  LockableComponent,
} from '@gc/core/navigation/models';
import { DesignationsFormComponent } from '@gc/product/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';

@Component({
  selector: 'gc-code-designation',
  templateUrl: './code-designation.component.html',
  styleUrls: [
    'code-designation.component.scss',
    'code-designation-theme.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: LOCKABLE_COMPONENT_TOKEN,
      useExisting: forwardRef(() => CodeDesignationComponent),
    },
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'product-parameter',
      multi: true,
    },
  ],
  standalone: true,
  imports: [TranslocoModule, BlockReloadDirective, DesignationsFormComponent],
})
export class CodeDesignationComponent implements LockableComponent {
  @ViewChild(DesignationsFormComponent)
  designationsFormComponent!: DesignationsFormComponent;

  canDeactivate(): boolean {
    return !this.designationsFormComponent.hasModifications();
  }
}
