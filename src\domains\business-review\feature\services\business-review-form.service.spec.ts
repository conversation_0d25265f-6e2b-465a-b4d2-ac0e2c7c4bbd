import { waitForAsync } from '@angular/core/testing';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { Observable, of, skip } from 'rxjs';
import {
  CalendarYear,
  BusinessReviewParameters,
  PeriodicityKind,
} from '@gc/business-review/models';
import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { LegacyApiService } from '@gc/shared/api/data-access';
import { TokenManagerService } from '@gc/shared/api/authentication/data-access';
import { ReactiveFormsModule } from '@angular/forms';
import { Warehouse, warehousesFixture } from '@gc/warehouse/models';
import { VatService } from '@gc/vat/data-access';
import { GuidHelper } from '@isagri-ng/core';
import { SnackbarService } from '@gc/shared/ui';
import { TranslocoDirective } from '@jsverse/transloco';
import { MockDirective } from 'ng-mocks';
import { BusinessReviewService } from '../../data-access/services/business-review.service';
import { PeriodicityHelper } from '@gc/business-review/utils';
import { BusinessReviewFormService } from './business-review-form.service';

describe('BusinessReviewFormService', () => {
  let spectator: SpectatorService<BusinessReviewFormService>;
  let service: BusinessReviewFormService;
  let businessReviewService: BusinessReviewService;

  let getDataSpy: jest.SpyInstance<
    Observable<{
      vatRates: number[];
      warehouses: Warehouse[];
    }>
  >;

  const createService = createServiceFactory({
    service: BusinessReviewFormService,
    imports: [ReactiveFormsModule],
    declarations: [MockDirective(TranslocoDirective)],
    mocks: [
      BusinessReviewService,
      LegacyApiService,
      VatService,
      TokenManagerService,
      SnackbarService,
    ],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
    businessReviewService = spectator.inject(BusinessReviewService);

    // Calling the init method here to make the tests easier to write
    // When the init method is called, the businessReviewService getData method is called
    getDataSpy = jest.spyOn(businessReviewService, 'getData').mockReturnValue(
      of({
        vatRates: [],
        warehouses: [],
      })
    );
    spectator.service.init();
  });

  it('should init service', () => {
    expect(spectator.service).toBeTruthy();
  });

  describe('calendarYearsOptions property', () => {
    it('should be set with a list of 5 calendar years starting from the current year', () => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const expectedYears: CalendarYear[] = [
        {
          startDate: new Date(currentYear, 0, 1),
          endDate: new Date(currentDate.getFullYear(), 11, 31),
          viewValue: currentDate.getFullYear().toString(),
        },
        {
          startDate: new Date(currentYear - 1, 0, 1),
          endDate: new Date(currentYear - 1, 11, 31),
          viewValue: new Date(currentYear - 1, 0, 1).getFullYear().toString(),
        },
        {
          startDate: new Date(currentYear - 2, 0, 1),
          endDate: new Date(currentYear - 2, 11, 31),
          viewValue: new Date(currentYear - 2, 0, 1).getFullYear().toString(),
        },
        {
          startDate: new Date(currentYear - 3, 0, 1),
          endDate: new Date(currentYear - 3, 11, 31),
          viewValue: new Date(currentYear - 3, 0, 1).getFullYear().toString(),
        },
        {
          startDate: new Date(currentYear - 4, 0, 1),
          endDate: new Date(currentYear - 4, 11, 31),
          viewValue: new Date(currentYear - 4, 0, 1).getFullYear().toString(),
        },
      ];
      expect(spectator.service.calendarYearsOptions.length).toEqual(5);
      expect(spectator.service.calendarYearsOptions).toEqual(expectedYears);
    });
  });

  describe('getBusinessReviewParameters', () => {
    describe('given a state where the form values are set for companies, vatRate and warehouses', () => {
      const selectedCompanyIds = [GuidHelper.newGuid(), GuidHelper.newGuid()];
      const selectedVatRates = 20;
      const selectedWarehouseIds = [
        GuidHelper.newGuid(),
        GuidHelper.newGuid(),
        GuidHelper.newGuid(),
      ];

      beforeEach(() => {
        spectator.service.companiesFC.setValue(selectedCompanyIds);
        spectator.service.vatRateFC.setValue(selectedVatRates);
        spectator.service.warehousesFC.setValue(selectedWarehouseIds);
      });

      describe('and given a state where the periodicity kind is set to calendar year', () => {
        const selectedCalendarYear = {
          startDate: new Date(2020, 1, 1),
          endDate: new Date(2020, 12, 31),
        } as CalendarYear;

        beforeEach(() => {
          spectator.service.periodicityKindFC.setValue(
            PeriodicityKind.CALENDAR_YEAR
          );
          spectator.service.calendarYearFC.setValue(selectedCalendarYear);
        });

        it('should call the periodicity helper', () => {
          const helperSpy = jest.spyOn(
            PeriodicityHelper,
            'getDatesRangesFromCalendarYear'
          );
          spectator.service.getBusinessReviewParameters();
          expect(helperSpy).toHaveBeenCalledWith(selectedCalendarYear, 5);
        });

        it('should return a BusinessReviewParameters object with the selected parameters', () => {
          const expectedResult: BusinessReviewParameters = {
            companyIds: selectedCompanyIds,
            vatRate: selectedVatRates,
            warehouseIds: selectedWarehouseIds,
            dates: [
              '1/1/2020',
              '12/31/2020',
              '1/1/2019',
              '12/31/2019',
              '1/1/2018',
              '12/31/2018',
              '1/1/2017',
              '12/31/2017',
              '1/1/2016',
              '12/31/2016',
            ],
            selectedDates: {
              startDate: selectedCalendarYear.startDate,
              endDate: selectedCalendarYear.endDate,
            },
            isFiscalYear: false,
          };
          expect(spectator.service.getBusinessReviewParameters()).toStrictEqual(
            expectedResult
          );
        });
      });

      describe('and given a state where the periodicity kind is set to fiscal year', () => {
        const fiscalYearsList: FiscalYearDateRange[] = [
          {
            dateFrom: new Date(2020, 7, 1), // August 1st
            dateTo: new Date(2020, 11, 31), // December 31st
          },
          {
            dateFrom: new Date(2020, 0, 1), // January 1st
            dateTo: new Date(2020, 6, 31), // July 31st
          },
          {
            dateFrom: new Date(2019, 7, 1), // August 1st
            dateTo: new Date(2019, 11, 31), // December 31st
          },
          {
            dateFrom: new Date(2019, 0, 1), // January 1st
            dateTo: new Date(2019, 6, 31), // July 31st
          },
          {
            dateFrom: new Date(2018, 7, 1), // August 1st
            dateTo: new Date(2018, 11, 31), // December 31st
          },
          {
            dateFrom: new Date(2018, 0, 1), // January 1st
            dateTo: new Date(2018, 6, 31), // July 31st
          },
          {
            dateFrom: new Date(2000, 0, 1), // January 1st
            dateTo: new Date(2000, 11, 31), // December 31st
          },
        ];

        beforeEach(() => {
          jest
            .spyOn(businessReviewService, 'getFiscalYears')
            .mockReturnValue(of(fiscalYearsList));

          spectator.service.periodicityKindFC.setValue(
            PeriodicityKind.FISCAL_YEAR
          );
        });

        beforeEach(() => {
          spectator.service.periodicityKindFC.setValue(
            PeriodicityKind.FISCAL_YEAR
          );
        });

        describe('and given a state where the fiscal year is set with a value having at least 5 consecutives values', () => {
          // The first fiscal year has 4 consecutive years
          const [selectedFiscalYear] = fiscalYearsList;
          beforeEach(() => {
            spectator.service.fiscalYearFC.setValue(selectedFiscalYear);
          });

          it('should call the periodicity helper', () => {
            const helperSpy = jest.spyOn(
              PeriodicityHelper,
              'getDatesRangesFromFiscalYear'
            );
            spectator.service.getBusinessReviewParameters();
            expect(helperSpy).toHaveBeenCalledWith(
              selectedFiscalYear,
              fiscalYearsList,
              5
            );
          });

          it('should return a BusinessReviewParameters object with the selected parameters', () => {
            const expectedResult: BusinessReviewParameters = {
              companyIds: selectedCompanyIds,
              vatRate: selectedVatRates,
              warehouseIds: selectedWarehouseIds,
              dates: [
                '8/1/2020',
                '12/31/2020',
                '1/1/2020',
                '7/31/2020',
                '8/1/2019',
                '12/31/2019',
                '1/1/2019',
                '7/31/2019',
                '8/1/2018',
                '12/31/2018',
              ],
              selectedDates: {
                startDate: selectedFiscalYear.dateFrom,
                endDate: selectedFiscalYear.dateTo,
              },
              isFiscalYear: true,
            };
            expect(
              spectator.service.getBusinessReviewParameters()
            ).toStrictEqual(expectedResult);
          });
        });

        describe('and given a state where the fiscal year is set with a value having no consecutives values', () => {
          // The last fiscal year has no consecutive year
          const [, , , , , , selectedFiscalYear] = fiscalYearsList;
          beforeEach(() => {
            spectator.service.fiscalYearFC.setValue(selectedFiscalYear);
          });

          it('should call the periodicity helper', () => {
            const helperSpy = jest.spyOn(
              PeriodicityHelper,
              'getDatesRangesFromFiscalYear'
            );
            spectator.service.getBusinessReviewParameters();
            expect(helperSpy).toHaveBeenCalledWith(
              selectedFiscalYear,
              fiscalYearsList,
              5
            );
          });

          it('should return a BusinessReviewParameters object with the selected parameters', () => {
            const expectedResult: BusinessReviewParameters = {
              companyIds: selectedCompanyIds,
              vatRate: selectedVatRates,
              warehouseIds: selectedWarehouseIds,
              dates: ['1/1/2000', '12/31/2000'],
              selectedDates: {
                startDate: selectedFiscalYear.dateFrom,
                endDate: selectedFiscalYear.dateTo,
              },
              isFiscalYear: true,
            };
            expect(
              spectator.service.getBusinessReviewParameters()
            ).toStrictEqual(expectedResult);
          });
        });
      });
    });
  });

  describe('businessReviewFG form Group', () => {
    describe('given an initial state', () => {
      it('should have the default values', () => {
        expect(spectator.service.companiesFC.value).toEqual([]);
        expect(spectator.service.periodicityKindFC.value).toEqual(
          PeriodicityKind.CALENDAR_YEAR
        );
        expect(spectator.service.calendarYearFC.value).toEqual(
          spectator.service.calendarYearsOptions[0]
        );
        expect(spectator.service.fiscalYearFC.value).toEqual(null);
        expect(spectator.service.warehousesFC.value).toEqual([]);
        expect(spectator.service.vatRateFC.value).toEqual(null);
      });

      it('should be invalid', () => {
        expect(spectator.service.businessReviewFG.valid).toBeFalsy();
        expect(spectator.service.companiesFC.valid).toBeFalsy();
        expect(spectator.service.periodicityFG.valid).toBeTruthy();
        expect(spectator.service.vatRateFC.valid).toBeFalsy();
      });

      it('should be pristine', () => {
        expect(spectator.service.businessReviewFG.pristine).toBeTruthy();
        expect(spectator.service.companiesFC.pristine).toBeTruthy();
        expect(spectator.service.periodicityFG.pristine).toBeTruthy();
        expect(spectator.service.vatRateFC.pristine).toBeTruthy();
        expect(spectator.service.warehousesFC.pristine).toBeTruthy();
      });
    });
  });

  describe('_getDataOnCompanyChange method', () => {
    describe('given a state where the controls values have been changed and status are dirty', () => {
      beforeEach(() => {
        spectator.service.calendarYearFC.setValue(
          spectator.service.calendarYearsOptions[2]
        );
        spectator.service.periodicityKindFC.setValue(
          PeriodicityKind.FISCAL_YEAR
        );
        spectator.service.fiscalYearFC.setValue({
          dateFrom: new Date(),
          dateTo: new Date(),
        });
        spectator.service.vatRateFC.setValue(999);
        spectator.service.warehousesFC.setValue([GuidHelper.newGuid()]);

        spectator.service.companiesFC.markAsDirty();
        spectator.service.calendarYearFC.markAsDirty();
        spectator.service.periodicityKindFC.markAsDirty();
        spectator.service.fiscalYearFC.markAsDirty();
        spectator.service.vatRateFC.markAsDirty();
        spectator.service.warehousesFC.markAsDirty();
        spectator.service.businessReviewFG.markAsDirty();
        spectator.service.periodicityFG.markAsDirty();
      });

      it('should have dirty status', () => {
        expect(spectator.service.businessReviewFG.dirty).toBeTruthy();
        expect(spectator.service.companiesFC.dirty).toBeTruthy();
        expect(spectator.service.periodicityFG.dirty).toBeTruthy();
        expect(spectator.service.calendarYearFC.dirty).toBeTruthy();
        expect(spectator.service.fiscalYearFC.dirty).toBeTruthy();
        expect(spectator.service.vatRateFC.dirty).toBeTruthy();
        expect(spectator.service.warehousesFC.dirty).toBeTruthy();
      });

      describe('then when the company selection changes (_getDataOnCompanyChange method)', () => {
        const selectedCompanyIds = [GuidHelper.newGuid()];

        describe('and the business service returns the companies data including a 20% vat Rate', () => {
          const companiesData = {
            vatRates: [8.2, 20, 12.5, 75.99],
            warehouses: warehousesFixture(),
          };

          beforeEach(() => {
            getDataSpy.mockReturnValue(of(companiesData));
            spectator.service.companiesFC.setValue(selectedCompanyIds);
          });

          it('should call the business service getData method', () => {
            expect(getDataSpy).toHaveBeenCalledWith(selectedCompanyIds);
          });

          describe('the periodicity Form Group', () => {
            it('should have his periodicity kind control value set to calendar year', () => {
              expect(spectator.service.periodicityKindFC.value).toEqual(
                PeriodicityKind.CALENDAR_YEAR
              );
            });

            it('should have his periodicity kind control status set to pristine', () => {
              expect(spectator.service.periodicityKindFC.pristine).toBeTruthy();
            });

            it('should have his calendar year control value set with the first option', () => {
              expect(spectator.service.calendarYearFC.value).toEqual(
                spectator.service.calendarYearsOptions[0]
              );
            });

            it('should have his calendar year control status set to pristine', () => {
              expect(spectator.service.calendarYearFC.pristine).toBeTruthy();
            });

            it('should have his fiscal year formControl value set to null', () => {
              expect(spectator.service.fiscalYearFC.value).toBeNull();
            });

            it('should have his fiscal year control status set to pristine', () => {
              expect(spectator.service.fiscalYearFC.pristine).toBeTruthy();
            });

            it('should have the pristine status', () => {
              expect(spectator.service.periodicityFG.pristine).toBeTruthy();
            });
          });

          describe('the vatRate formControl', () => {
            it('should have the default 20% rate value', () => {
              expect(spectator.service.vatRateFC.value).toEqual(20);
            });

            it('should have the status pristine', () => {
              expect(spectator.service.vatRateFC.pristine).toBeTruthy();
            });
          });

          describe('the warehouses formControl', () => {
            it('should have his value set with the warehouseIds values', () => {
              const ids = companiesData.warehouses.map(
                (warehouse) => warehouse.id
              );
              expect(spectator.service.warehousesFC.value).toEqual(ids);
            });

            it('should have his status to pristine', () => {
              expect(spectator.service.warehousesFC.pristine).toBeTruthy();
            });
          });
        });

        describe('and the business service returns the companies data with no 20% vat Rate', () => {
          const highestVatRate = 35.12;
          const companiesData = {
            vatRates: [8.2, highestVatRate, 12.5],
            warehouses: warehousesFixture(),
          };

          beforeEach(() => {
            getDataSpy.mockReturnValue(of(companiesData));
            spectator.service.companiesFC.setValue(selectedCompanyIds);
          });

          describe('the vatRate formControl', () => {
            it('should have the highest vat rate value (as no 20% vat rate in the list)', () => {
              expect(spectator.service.vatRateFC.value).toEqual(highestVatRate);
            });

            it('should have the status pristine', () => {
              expect(spectator.service.vatRateFC.pristine).toBeTruthy();
            });
          });
        });
      });
    });
  });

  describe('_reinitParameters method', () => {
    describe('given a state where one of the FormGroup is dirty', () => {
      beforeEach(() => {
        service.vatRateFC.markAsDirty();
      });

      describe('when method is call with any parameters', () => {
        it('should cal next on formReinitialized$$ subject', waitForAsync(() => {
          expect.assertions(1);
          service.formReinitialized$.subscribe(() => {
            expect(true).toBe(true);
          });

          (service as any)._reinitParameters([], [], []);
        }));
      });
    });
  });

  describe('selectedDateRange$ observable', () => {
    let result: {
      startDate: string | undefined;
      endDate: string | undefined;
    };

    describe('Given an initial state', () => {
      it('should emit the date range of the current calendar year', waitForAsync(() => {
        const currentYear = new Date().getFullYear();
        const currentCalendarYear = {
          startDate: new Date(currentYear, 0, 1),
          endDate: new Date(currentYear, 11, 31),
        };
        spectator.service.selectedDateRange$.subscribe((value) => {
          expect(value).toEqual(currentCalendarYear);
        });
      }));
    });

    describe('Given a state where the periodicity is set with a calendar year', () => {
      const selectedCalendarYear = {
        startDate: new Date(2020, 0, 1),
        endDate: new Date(2022, 11, 31),
        viewValue: '2020',
      };

      beforeEach(() => {
        spectator.service.periodicityKindFC.setValue(
          PeriodicityKind.CALENDAR_YEAR
        );
        spectator.service.calendarYearFC.setValue(selectedCalendarYear);
      });

      it('should emit the date range of the selected calendar year in a locale string format', waitForAsync(() => {
        spectator.service.selectedDateRange$
          .pipe(skip(1))
          .subscribe((value) => {
            expect(value).toEqual({
              startDate: selectedCalendarYear.startDate,
              endDate: selectedCalendarYear.endDate,
            });
          });
      }));
    });

    describe('Given a state where the periodicity kind is set to fiscal year', () => {
      const selectedFiscalYear: FiscalYearDateRange = {
        dateFrom: new Date(1976, 0, 15),
        dateTo: new Date(1977, 9, 31),
      };

      beforeEach(() => {
        spectator.service.periodicityKindFC.setValue(
          PeriodicityKind.FISCAL_YEAR
        );
        spectator.service.fiscalYearFC.setValue(selectedFiscalYear);
      });

      it('should emit the start date of the selected fiscal year in a locale string format', waitForAsync(() => {
        spectator.service.fiscalYearFC.valueChanges
          .pipe(skip(1))
          .subscribe(() => {
            expect(result).toEqual({
              startDate: selectedFiscalYear.dateFrom,
              endDate: selectedFiscalYear.dateTo,
            });
          });
      }));
    });
  });

  describe('isLoading$ observable', () => {
    it('should emit false when all loading status are false', waitForAsync(() => {
      spectator.service.isLoadingAdvancedSettingsData$$.next(false);
      spectator.service.isLoadingFiscalYears$$.next(false);
      spectator.service.isLoading$.subscribe((value) => {
        expect(value).toEqual(false);
      });
    }));

    it('should emit true when the advanced params data loading status is true', waitForAsync(() => {
      spectator.service.isLoadingAdvancedSettingsData$$.next(true);
      spectator.service.isLoadingFiscalYears$$.next(false);
      spectator.service.isLoading$.subscribe((value) => {
        expect(value).toEqual(true);
      });
    }));

    it('should emit true when the fiscal years loading status is true', waitForAsync(() => {
      spectator.service.isLoadingAdvancedSettingsData$$.next(false);
      spectator.service.isLoadingFiscalYears$$.next(true);
      spectator.service.isLoading$.subscribe((value) => {
        expect(value).toEqual(true);
      });
    }));
  });

  describe('periodicityKind getter', () => {
    it('should return the value of the periodicity kind form control', () => {
      spectator.service.periodicityKindFC.setValue(PeriodicityKind.FISCAL_YEAR);
      expect(spectator.service.periodicityKind).toEqual(
        PeriodicityKind.FISCAL_YEAR
      );
    });
  });
});
