.container {
    height: 100%;
    padding: 10px;
    border-radius: 8px;
    overflow-y: auto;

    > .wrapper {
        padding: 12px;

        .header {
            width: 40%;
            margin-bottom: 10px;

            .filters {
                display: flex;
                gap: 40px;
                width: fit-content;
            }
        }

        .body {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: stretch;

            .card {
                width: 45%;
                margin-top: 40px;

                &:first-child {
                    margin-top: 0;
                }

                &.full-width {
                    width: 100%;
                }

                p {
                    margin-top: 10px;
                    opacity: 0.4;
                }
            }
        }

        .actions-container {
            margin-top: 20px;
            border-top: 1px solid rgba(112, 112, 112, 33%);
            padding-top: 20px;
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }

        gc-loader {
            justify-content: center;
            margin-top: 2rem;
        }
    }
}

gc-account-entry-list-treatment-id-retriever-card {
    display: none;
}
