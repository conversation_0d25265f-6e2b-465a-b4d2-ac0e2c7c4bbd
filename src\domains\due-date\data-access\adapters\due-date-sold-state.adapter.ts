import { DueDateStateApi } from '../models/api/due-date-state-api.model';
import { DueDateSoldState } from '@gc/due-date/models';

export class DueDateSoldStateAdapter {
  public static toApi(dueDateSoldState: DueDateSoldState): DueDateStateApi {
    switch (dueDateSoldState) {
      case DueDateSoldState.SOLD: {
        return DueDateStateApi.SOLD;
      }
      case DueDateSoldState.NOT_SOLD: {
        return DueDateStateApi.NOT_SOLD;
      }
      default: {
        return DueDateStateApi.ALL;
      }
    }
  }
}
