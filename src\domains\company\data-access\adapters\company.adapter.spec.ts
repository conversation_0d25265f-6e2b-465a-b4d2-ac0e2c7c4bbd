import { CompanyAdapter } from './company.adapter';
import { companiesApiFixture } from '../fixtures/company-api.fixture';
import { Company } from '@gc/company/models';

describe('CompanyAdapter', () => {
  it('should return a Company', () => {
    const [companyApi] = companiesApiFixture();
    const expected: Company = {
      id: companyApi.id,
      code: companyApi.code,
      designation: companyApi.designation,
    } as Company;

    const result = CompanyAdapter.fromApi(companyApi);

    expect(result).toStrictEqual(expected);
  });
});
