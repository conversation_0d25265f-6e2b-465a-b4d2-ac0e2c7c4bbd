import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import {
  IDocumentContextService,
  provideDocumentViewer,
} from '@isagri-ng/document-viewer';
import { DocumentContextService } from '@gc/core/documents-viewer/data-access';

export function provideDocumentsViewer(): EnvironmentProviders {
  return makeEnvironmentProviders([
    provideDocumentViewer([
      {
        provide: IDocumentContextService,
        useClass: DocumentContextService,
      },
    ]),
  ]);
}
