import { Injectable, inject } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { QueryParamsKeys } from '@gc/shared/models';
import { map, Observable } from 'rxjs';
import { ReportSupplementaryStimulsoftProperties } from '../models/report-supplementary-stimulsoft-properties.model';

@Injectable({
  providedIn: 'root',
})
export class ReportStimulsoftNavigationService {
  private readonly _activatedRoute = inject(ActivatedRoute);

  getReportStimulsoftPropertiesFromQueryParams$(): Observable<ReportSupplementaryStimulsoftProperties> {
    return this._activatedRoute.queryParams.pipe(
      map((queryParams: Params) => {
        return {
          currentCompanyId: queryParams[
            QueryParamsKeys.DEFAULT_COMPANY_ID
          ] as string,
          companiesIds: queryParams[QueryParamsKeys.COMPANIES_IDS] as
            | string[]
            | undefined,
          clientCode: queryParams[QueryParamsKeys.CLIENT_CODE] as string,
        };
      })
    );
  }
}
