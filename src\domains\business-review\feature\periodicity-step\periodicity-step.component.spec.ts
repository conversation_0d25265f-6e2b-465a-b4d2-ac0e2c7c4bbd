import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { MockDirective } from 'ng-mocks';
import { TranslocoDirective } from '@jsverse/transloco';
import { PeriodicityStepComponent } from './periodicity-step.component';
import { BusinessReviewFormService } from '../services/business-review-form.service';

describe('PeriodicityStepComponent', () => {
  let spectator: Spectator<PeriodicityStepComponent>;
  const createComponent = createComponentFactory({
    component: PeriodicityStepComponent,
    declarations: [MockDirective(TranslocoDirective)],
    mocks: [BusinessReviewFormService],
  });

  it('should create', () => {
    spectator = createComponent();

    expect(spectator.component).toBeTruthy();
  });
});
