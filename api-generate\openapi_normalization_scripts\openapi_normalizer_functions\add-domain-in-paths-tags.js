/**
 *  This method take as input an openapi object corresponding to a openapi file from stoplight ; for instance : IS-GC_DEB_V1.json
 *  This method adds the domain (= deb) in paths tags because it is being used by the generator to generate the service name 👉 debApi.service.ts
 *
 * @param {*} openapiObj deserialized object from .json openapi file
 * @param {*} domain api domain (ie: deb)
 * @return {*} openapiObj with tags updated
 */
function addDomainInPathsTags(openapiObj, domain) {
    if (openapiObj.paths) {
        for (const pathKey in openapiObj.paths) {
            const pathItem = openapiObj.paths[pathKey];
            for (const method in pathItem) {
                pathItem[method].tags = [domain.toLowerCase()];
            }
        }
    }
    return openapiObj;
}

exports.addDomainInPathsTags = addDomainInPathsTags;
