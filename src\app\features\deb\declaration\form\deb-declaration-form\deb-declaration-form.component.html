<div
  class="form-container"
  *transloco="let t; read: 'deb.declaration-edit-dialog.declaration-edit-form'">
  <form [formGroup]="formGroup" (ngSubmit)="onSubmit()">
    <div class="form-inputs-container">
      <mat-form-field appearance="outline">
        <mat-label>{{ t('nomenclature') }}</mat-label>
        <input
          matInput
          type="text"
          [matAutocomplete]="auto"
          formControlName="euNomenclature" />
        <mat-autocomplete #auto="matAutocomplete">
          @for (
            filteredEuNomenclature of filteredEuNomenclatures();
            track filteredEuNomenclature
          ) {
            <mat-option [value]="filteredEuNomenclature">
              {{ filteredEuNomenclature }}
            </mat-option>
          }
        </mat-autocomplete>
        <mat-hint align="end">
          {{ euNomenclatureFC.value?.length || 0 }} /
          {{ nomenclatureMaxLength }}
        </mat-hint>
        @if (euNomenclatureFC.hasError('maxlength')) {
          <mat-error *transloco="let t; read: 'deb.declaration-edit-dialog'">
            {{
              t('errors.max-length', {
                maxlength: nomenclatureMaxLength,
              })
            }}
          </mat-error>
        }
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>{{ t('destinationCountryCode') }}</mat-label>
        <mat-select formControlName="destinationCountryCode">
          @for (
            destinationCountryCode of destinationCountryCodes().data;
            track destinationCountryCode
          ) {
            <mat-option [value]="destinationCountryCode">
              {{ destinationCountryCode }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>
      <gc-currency-input
        [label]="t('invoicedAmount')"
        [currency]="(currency$ | ngrxPush) ?? {}"
        formControlName="invoicedAmount"></gc-currency-input>
      <mat-form-field appearance="outline">
        <mat-label>{{ t('statisticalProcedureCode') }}</mat-label>
        <mat-select formControlName="statisticalProcedureCode">
          @for (
            statisticalProcedureCode of statisticalProcedures().data;
            track statisticalProcedureCode
          ) {
            <mat-option [value]="statisticalProcedureCode">
              {{ statisticalProcedureCode }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>
          {{ t('vatNumber') }}
        </mat-label>
        <input
          matInput
          formControlName="vatNumber"
          name="vatNumber"
          [maxlength]="VAT_NUMBER_MAX_LENGTH" />
      </mat-form-field>
    </div>
    @if (formGroup.invalid && (formGroup.dirty || formGroup.touched)) {
      <p class="error" *transloco="let t">
        <mat-error>{{ t('sharedErrors.form.required') }}</mat-error>
      </p>
    }

    <div *transloco="let t" class="action-button-container">
      <button
        mat-stroked-button
        color="primary"
        type="button"
        cdkFocusInitial
        (click)="cancel()">
        {{ t('sharedAction.cancel') }}
      </button>
      <button
        mat-raised-button
        color="primary"
        type="submit"
        [disabled]="!formGroup.valid">
        {{ t('sharedAction.validate') }}
      </button>
    </div>
  </form>
</div>
