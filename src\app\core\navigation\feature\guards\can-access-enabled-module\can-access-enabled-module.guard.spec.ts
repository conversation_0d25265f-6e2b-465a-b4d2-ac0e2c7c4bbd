import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { of, Observable } from 'rxjs';
import { CodeModuleCommercial, URL_PATHS } from '@gc/core/navigation/models';
import { UserModulesService } from '../../services/user-modules.service';
import { canAccessModule } from './can-access-enabled-module.guard';

// Mock the inject function
jest.mock('@angular/core', () => ({
  ...jest.requireActual('@angular/core'),
  inject: jest.fn(),
}));

describe('canAccessModule', () => {
  let router: jest.Mocked<Router>;
  let userModulesService: jest.Mocked<UserModulesService>;
  let mockRoute: ActivatedRouteSnapshot;
  let mockState: RouterStateSnapshot;

  beforeEach(() => {
    jest.clearAllMocks();

    router = {
      createUrlTree: jest.fn(),
      navigate: jest.fn(),
    } as unknown as jest.Mocked<Router>;

    userModulesService = {
      canAccessModule: jest.fn(),
      getUserModules: jest.fn(),
    } as unknown as jest.Mocked<UserModulesService>;

    mockRoute = {} as ActivatedRouteSnapshot;
    mockState = {} as RouterStateSnapshot;

    (inject as jest.Mock).mockImplementation((token: any) => {
      if (token === Router) return router;
      if (token === UserModulesService) return userModulesService;
      return null;
    });
  });

  it('should return true when user has access to the module', () => {
    const moduleName = CodeModuleCommercial.Deb;
    userModulesService.canAccessModule.mockReturnValue(of(true));

    const guardFn = canAccessModule(moduleName);
    const result = guardFn(mockRoute, mockState) as Observable<
      boolean | UrlTree
    >;

    result.subscribe((value) => {
      expect(value).toBe(true);
    });
    expect(userModulesService.canAccessModule).toHaveBeenCalledWith(moduleName);
  });

  it('should return a UrlTree to unavailable module page when user does not have access', () => {
    const moduleName = CodeModuleCommercial.Deb;
    const mockUrlTree = new UrlTree();

    userModulesService.canAccessModule.mockReturnValue(of(false));
    router.createUrlTree.mockReturnValue(mockUrlTree);

    const guardFn = canAccessModule(moduleName);
    const result = guardFn(mockRoute, mockState) as Observable<
      boolean | UrlTree
    >;

    result.subscribe((value) => {
      expect(value).toBe(mockUrlTree);
    });
    expect(userModulesService.canAccessModule).toHaveBeenCalledWith(moduleName);
    expect(router.createUrlTree).toHaveBeenCalledWith([
      URL_PATHS.unavailableModule,
    ]);
  });

  it('should pass the correct module name to canAccessModule', () => {
    const moduleName = CodeModuleCommercial.DeliveryNoteAndCreditNote;
    userModulesService.canAccessModule.mockReturnValue(of(true));

    const guardFn = canAccessModule(moduleName);
    guardFn(mockRoute, mockState);

    expect(userModulesService.canAccessModule).toHaveBeenCalledWith(moduleName);
  });
});
