import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'gc-sub-header',
  templateUrl: './sub-header.component.html',
  styleUrls: ['./sub-header.component.scss'],
  standalone: true,
  imports: [MatButtonModule, RouterLink, MatIconModule],
})
export class SubHeaderComponent {
  @Input() title!: string;
  @Input() routeBack? = '../';
}
