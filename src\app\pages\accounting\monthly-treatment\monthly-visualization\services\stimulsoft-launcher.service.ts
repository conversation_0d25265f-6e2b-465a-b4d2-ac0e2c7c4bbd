import { Injectable, inject } from '@angular/core';
import { Params } from '@angular/router';
import { MonthlyTreatmentService } from '@gc/accounting/data-access';
import {
  SalesByProductFilters,
  SalesDetailsFilters,
} from '@gc/accounting/models';
import { NavigationService } from '@gc/core/navigation/feature';
import { URL_PATHS } from '@gc/core/navigation/models';
import { LoadingStatus, StimulsoftQueryParamsKeys } from '@gc/shared/models';
import {
  REPORT_ID,
  ReportFamilies,
  ReportId,
} from '@gc/shared/stimulsoft/models';
import { BehaviorSubject, catchError, EMPTY, map, Observable, tap } from 'rxjs';

@Injectable()
export class StimulsoftLauncherService {
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);
  private readonly _navigationService = inject(NavigationService);

  get salesByProductTreatmentIdLoadingStatus$(): Observable<LoadingStatus> {
    return this._salesByProductTreatmentIdLoadingStatus$$.asObservable();
  }
  get salesDetailsTreatmentIdLoadingStatus$(): Observable<LoadingStatus> {
    return this._salesDetailsTreatmentIdLoadingStatus$$.asObservable();
  }

  private _salesByProductTreatmentIdLoadingStatus$$ =
    new BehaviorSubject<LoadingStatus>('NOT_LOADED');
  private _salesDetailsTreatmentIdLoadingStatus$$ =
    new BehaviorSubject<LoadingStatus>('NOT_LOADED');

  retrieveSalesByProductCategoryTreatmentIdAndNavigateToStimulsoft(
    filters: SalesByProductFilters
  ): Observable<void> {
    this._salesByProductTreatmentIdLoadingStatus$$.next('IN_PROGRESS');
    return this._monthlyTreatmentService
      .getSalesByProductTreatmentId(filters)
      .pipe(
        tap((treatmentId) => {
          const stimulsoftQueryParams: Params = {
            [StimulsoftQueryParamsKeys.TREATMENT_ID]: treatmentId,
            [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.sales,
            [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.SALES,
          };
          this._navigateToStimulsoft(stimulsoftQueryParams);

          this._salesByProductTreatmentIdLoadingStatus$$.next('LOADED');
        }),
        catchError(() => {
          this._salesByProductTreatmentIdLoadingStatus$$.next('ERROR');
          return EMPTY;
        }),
        map(() => void 0)
      );
  }

  retrieveSalesDetailsTreatmentIdAndNavigateToStimulsoft(
    filters: SalesDetailsFilters
  ): Observable<void> {
    this._salesDetailsTreatmentIdLoadingStatus$$.next('IN_PROGRESS');
    return this._monthlyTreatmentService
      .getSalesDetailsTreatmentId(filters)
      .pipe(
        tap((treatmentId) => {
          const stimulsoftQueryParams: Params = {
            [StimulsoftQueryParamsKeys.TREATMENT_ID]: treatmentId,
            [StimulsoftQueryParamsKeys.REPORT_ID]: REPORT_ID.details,
            [StimulsoftQueryParamsKeys.REPORT_FAMILY]: ReportFamilies.DETAILS,
          };
          this._navigateToStimulsoft(stimulsoftQueryParams);

          this._salesDetailsTreatmentIdLoadingStatus$$.next('LOADED');
        }),
        catchError(() => {
          this._salesDetailsTreatmentIdLoadingStatus$$.next('ERROR');
          return EMPTY;
        }),
        map(() => void 0)
      );
  }

  prepareParamsAndNavigateToStimulsoft(
    reportId: ReportId,
    reportFamily: ReportFamilies,
    companyId: string,
    endDate: Date
  ): void {
    const stimulsoftQueryParams: Params = {
      [StimulsoftQueryParamsKeys.COMPANY_ID]: companyId,
      [StimulsoftQueryParamsKeys.END_DATE]: endDate.toISOString(),
      [StimulsoftQueryParamsKeys.REPORT_ID]: reportId,
      [StimulsoftQueryParamsKeys.REPORT_FAMILY]: reportFamily,
    };

    this._navigateToStimulsoft(stimulsoftQueryParams);
  }

  private _navigateToStimulsoft(stimulsoftQueryParams: Params): void {
    this._navigationService.openInNewTab(
      URL_PATHS.stimulsoftViewer,
      stimulsoftQueryParams
    );
  }
}
