import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  inject,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatStepperModule } from '@angular/material/stepper';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { CompaniesStepComponent } from '../companies-step/companies-step.component';
import { PeriodicityStepComponent } from '../periodicity-step/periodicity-step.component';
import { SummaryStepComponent } from '../summary-step/summary-step.component';
import { LetDirective } from '@ngrx/component';
import { LoaderComponent } from '@gc/shared/ui';
import { BusinessReviewFormService } from '../services/business-review-form.service';

@Component({
  selector: 'gc-main-settings-stepper',
  standalone: true,
  imports: [
    TranslocoModule,
    MatStepperModule,
    MatButtonModule,
    CompaniesStepComponent,
    PeriodicityStepComponent,
    SummaryStepComponent,
    LetDirective,
    LoaderComponent,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'business-review',
      multi: true,
    },
  ],
  templateUrl: './main-settings-stepper.component.html',
  styleUrl: './main-settings-stepper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MainSettingsStepperComponent {
  private readonly _service = inject(BusinessReviewFormService);

  @Input() isLastStep = false;
  @Output() isLastStepChange = new EventEmitter<boolean>();

  companiesFC = this._service.companiesFC;
  periodicityFG = this._service.periodicityFG;

  isLoading$ = this._service.isLoading$;
}
