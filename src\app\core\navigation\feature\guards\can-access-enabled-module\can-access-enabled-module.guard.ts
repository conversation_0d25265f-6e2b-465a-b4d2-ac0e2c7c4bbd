import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { CodeModuleCommercial, URL_PATHS } from '@gc/core/navigation/models';
import { UserModulesService } from '../../services/user-modules.service';
import { map } from 'rxjs';

export const canAccessModule =
  (moduleName: CodeModuleCommercial): CanActivateFn =>
  () => {
    const router = inject(Router);
    const userModulesService = inject(UserModulesService);

    return userModulesService
      .canAccessModule(moduleName)
      .pipe(
        map((hasAccess) =>
          hasAccess ? true : router.createUrlTree([URL_PATHS.unavailableModule])
        )
      );
  };
