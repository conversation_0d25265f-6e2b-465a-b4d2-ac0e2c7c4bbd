import { BusinessResultWarningMessageApiAdapter } from './business-result-warning-message-api.adapter';
import { BusinessResultErrorMessageApiAdapter } from './business-result-error-message-api.adapter';
import { DunningLetterResponseBusinessResult } from '@gc/dunning/models';
import { DunningLetterResponseApiAdapter } from './dunning-letter-reponse-api.adapter';
import { DunningLetterResponseBusinessResultAPIApi } from '@gc/shared/api/data-access';

export class DunningLetterResponseBusinessResultAPIApiAdapter {
  static fromApi(
    response: DunningLetterResponseBusinessResultAPIApi
  ): DunningLetterResponseBusinessResult {
    if (!response) {
      throw new Error(
        '[DunningLetterResponseBusinessResultAPIApiAdapter] reponse should not be null'
      );
    }
    if (!response.warnings) {
      throw new Error(
        '[DunningLetterResponseBusinessResultAPIApiAdapter] warnings should not be null'
      );
    }
    if (!response.errors) {
      throw new Error(
        '[DunningLetterResponseBusinessResultAPIApiAdapter] errors should not be null'
      );
    }
    return {
      result: DunningLetterResponseApiAdapter.fromApi(response.result),
      warnings: BusinessResultWarningMessageApiAdapter.fromApi(
        response.warnings
      ),
      errors: BusinessResultErrorMessageApiAdapter.fromApi(response.errors),
    };
  }
}
