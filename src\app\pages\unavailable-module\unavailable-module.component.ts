import { Component } from '@angular/core';
import { NoNavigationContainerComponent } from '@gc/core/navigation/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';

@Component({
  selector: 'gc-unavailable-module',
  standalone: true,
  templateUrl: './unavailable-module.component.html',
  styleUrl: './unavailable-module.component.scss',
  imports: [NoNavigationContainerComponent, TranslocoModule],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'unavailable-module',
      multi: true,
    },
  ],
})
export class UnavailableModuleComponent {}
