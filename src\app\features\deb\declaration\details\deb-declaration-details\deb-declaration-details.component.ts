import { TranslocoDirective } from '@jsverse/transloco';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { LoaderComponent } from '@gc/shared/ui';
import {
  DebDetails,
  DebLineDetailsDocumentEnum,
  DraftDeclaration,
} from '@gc/core/deb/domains/models';

@Component({
  selector: 'gc-deb-declaration-details',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    MatTableModule,
    MatButtonModule,
    TranslocoDirective,
    MatCardModule,
    LoaderComponent,
  ],
  templateUrl: './deb-declaration-details.component.html',
  styleUrl: './deb-declaration-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationDetailsComponent {
  lineDetails = input<DebDetails[]>([]);
  isDetailsLoading = input<boolean>();
  debSummaryInfo = input<DraftDeclaration>();
  displayedColumns: string[] = [
    'documentType',
    'productCode',
    'productLabel',
    'documentNumber',
    'customerCode',
    'deliveryDate',
    'lineAmount',
  ];

  readonly documentTypeMappings: {
    [key in DebLineDetailsDocumentEnum]: string;
  } = {
    Invoice: 'invoice', // Maps 'Invoice' to 'invoice'
    CreditNote: 'credit-note', // Maps 'CreditNote' to 'creditNote'
    DeliveryNote: 'delivery-note', // Maps 'DeliveryNote' to 'deliveryNote'
    ReturnNote: 'return-note', // Maps 'ReturnNote' to 'returnNote'
  };

  getDocumentTypeTranslation(key: DebLineDetailsDocumentEnum) {
    return this.documentTypeMappings[key];
  }
}
