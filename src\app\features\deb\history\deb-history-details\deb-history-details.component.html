<ng-container *transloco="let t; read: 'deb.history'">
  @if (
    !closedMonths().isLoading &&
    closedMonths().data?.length &&
    !uncloseMonthState().isLoading
  ) {
    <mat-card>
      <mat-card-content>
        <table
          mat-table
          [dataSource]="closedMonths().data!"
          class="gc-table-cell-center table-container">
          <ng-container matColumnDef="declarationNumber">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ t('table.declaration-number') }}
            </th>
            <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
              {{ lineDetails | showDeclarationNumber: closedMonths().data! }}
            </td>
          </ng-container>
          <ng-container matColumnDef="closeMonth">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ t('table.close-month') }}
            </th>
            <td mat-cell *matCellDef="let lineDetails; let rowIndex = index">
              {{
                lineDetails['declarationMonth']
                  | translocoDate: { year: 'numeric', month: 'long' }
                  | capitalize
              }}
            </td>
          </ng-container>
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td
              mat-cell
              *matCellDef="
                let lineDetails;
                let rowIndex = index;
                let first = first
              ">
              @if (first) {
                @if (!displayUncloseActionButtons) {
                  <button
                    mat-raised-button
                    color="primary"
                    (click)="displayUncloseActionButtons = true">
                    {{ t('table.unclose') }}
                  </button>
                } @else {
                  <gc-confirm-action
                    (action)="handleUncloseActions($event, lineDetails)" />
                }
              }
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  } @else if (closedMonths().isLoading || uncloseMonthState().isLoading) {
    <gc-loader />
  }
</ng-container>
