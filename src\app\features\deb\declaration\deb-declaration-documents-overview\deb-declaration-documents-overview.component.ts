import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  effect,
  inject,
  input,
  output,
  Signal,
  signal,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTable, MatTableModule } from '@angular/material/table';
import { AmountWithCurrencyPipe } from '@gc/currency/ui';
import { Currency } from '@gc/shared/models';

import {
  ActionContainerComponent,
  AUTO_SIZES_DIALOG_CONFIG,
  DEFAULT_DIALOG_CONFIG,
  DialogService,
  LoaderComponent,
} from '@gc/shared/ui';
import { GuidHelper } from '@isagri-ng/core';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';

import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DebFacade } from '@gc/core/deb/application/facades';
import {
  DebDetails,
  DraftDeclaration,
  Filters,
  LoadDetailsParameters,
} from '@gc/core/deb/domains/models';
import { ResourceState } from '@gc/core/shared/store';
import { DebDeclarationDetailsContainerComponent } from '@gc/features/deb/declaration/details';
import { DebDeclarationFormContainerComponent } from '@gc/features/deb/declaration/form/deb-declaration-form-container';
import { FormConfig } from '@gc/features/deb/declaration/form/models';
import { MatButtonModule } from '@angular/material/button';
import { take } from 'rxjs';
import { FormatMissingFieldsPipe } from '@gc/features/deb/declaration/pipes/format-missing-fields.pipe';

type guid = string;

@Component({
  selector: 'gc-deb-declaration-documents-overview',
  standalone: true,
  imports: [
    TranslocoDirective,
    CommonModule,
    AmountWithCurrencyPipe,
    LoaderComponent,
    ActionContainerComponent,
    MatCardModule,
    MatTableModule,
    MatSlideToggleModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    FormatMissingFieldsPipe,
  ],
  styleUrls: ['./deb-declaration-documents-overview.component.scss'],
  templateUrl: './deb-declaration-documents-overview.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebDeclarationDocumentsOverviewComponent {
  private readonly dialogService: DialogService = inject(DialogService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly debFacade = inject(DebFacade);
  private readonly translocoService = inject(TranslocoService);

  companyId = input.required<string>();
  filters = input.required<Filters | null>();
  areFiltersApplied = input.required<boolean>();

  declarations = output<DraftDeclaration[]>();

  labelErrors = signal<string | null>(null);

  readonly details: Signal<ResourceState<DebDetails[]>>;

  inputDraftDeclarationsState =
    input.required<ResourceState<DraftDeclaration[]>>();
  draftDeclarations = signal<DraftDeclaration[]>([]);
  currency = input.required<Currency>();

  @ViewChild(MatTable) table!: MatTable<DraftDeclaration>;
  editedDebs: Map<guid, boolean> = new Map();

  displayedColumns: string[] = [
    'index',
    'nomenclature',
    'destinationCountryCode',
    'invoicedAmount',
    'statisticalProcedureCode',
    'vatNumber',
    'actions',
  ];
  constructor() {
    this.details = this.debFacade.getDetails();

    effect(() => {
      this.declarations.emit(this.draftDeclarations());
    });

    effect(
      () => {
        const inputDraftDeclarationsState = this.inputDraftDeclarationsState();
        if (!inputDraftDeclarationsState?.data) {
          this.draftDeclarations.set([]);
          return;
        }

        const enabledDeclarations = inputDraftDeclarationsState.data?.map(
          (declaration) => {
            const missingFields = this.checkMissingFields(declaration);

            return <DraftDeclaration>{
              ...declaration,
              status: 'enabled',
              missingFields,
            };
          }
        );
        this.draftDeclarations.set(enabledDeclarations ?? []);
      },
      { allowSignalWrites: true }
    );
  }

  toggleDeclarationState(id: guid): void {
    // Update the draftDeclarations signal with the new status
    this.draftDeclarations.update((declarations) => {
      // Find the declaration and its index
      const index = declarations.findIndex((d) => d.id === id);
      if (index === -1) {
        return declarations;
      }

      const declaration = declarations[index];
      const newStatus =
        declaration.status === 'enabled' ? 'disabled' : 'enabled';

      return [
        ...declarations.slice(0, index),
        { ...declaration, status: newStatus },
        ...declarations.slice(index + 1),
      ];
    });
  }

  onAddDebLineButtonClick(): void {
    const dialogRef = this.dialogService.open<
      DebDeclarationFormContainerComponent,
      FormConfig,
      DraftDeclaration
    >(
      DebDeclarationFormContainerComponent,

      {
        companyId: this.companyId(),
        month: this.filters()!.declarationMonth!,
        currency: this.currency(),
      },
      AUTO_SIZES_DIALOG_CONFIG
    );

    dialogRef
      .pipe(take(1))
      .subscribe((addedDraftDeclaration?: DraftDeclaration) => {
        if (addedDraftDeclaration) {
          this.add(addedDraftDeclaration);
        }
      });
  }

  openDetailsDialog(draftDeclaration: DraftDeclaration): void {
    if (draftDeclaration.state === 'initial') {
      this.openLineDetailsDialog(draftDeclaration);
    } else {
      this.openAddedOrUpdatedLineDetailsDialog(draftDeclaration);
    }
  }

  openAddedOrUpdatedLineDetailsDialog(
    draftDeclaration: DraftDeclaration
  ): void {
    this.dialogService.open(
      DebDeclarationDetailsContainerComponent,
      draftDeclaration,
      { ...DEFAULT_DIALOG_CONFIG }
    );
  }

  openLineDetailsDialog(draftDeclaration: DraftDeclaration): void {
    this.dialogService.open(
      DebDeclarationDetailsContainerComponent,
      {},
      { ...AUTO_SIZES_DIALOG_CONFIG }
    );

    if (!this.filters()) {
      return;
    }

    const detailsParameters: LoadDetailsParameters = {
      companyId: this.companyId(),
      deliveryNotesIncluded: this.filters()!.includeDeliveryNotes!,
      destinationCountryCode: draftDeclaration.destinationCountryCode!,
      invoicesIncluded: this.filters()!.includeInvoices!,
      euNomenclature: draftDeclaration.euNomenclature!,
      month: this.filters()!.declarationMonth!,
      statisticalProcedureCode: draftDeclaration.statisticalProcedureCode,
      vatNumber: draftDeclaration.vatNumber!,
    };

    this.debFacade.loadDetailsFor(detailsParameters);
  }

  openUpdateDialogAndUpdateToList(draftDeclaration: DraftDeclaration): void {
    const dialogRef = this.dialogService.open<
      DebDeclarationFormContainerComponent,
      FormConfig,
      DraftDeclaration
    >(
      DebDeclarationFormContainerComponent,
      {
        draftDeclaration,
        companyId: this.companyId(),
        month: this.filters()!.declarationMonth!,
        currency: this.currency(),
      },
      AUTO_SIZES_DIALOG_CONFIG
    );

    dialogRef
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((updatedDeclaration?: DraftDeclaration) => {
        if (updatedDeclaration) {
          this.update(updatedDeclaration);
        }
      });
  }

  private update(draftDeclaration: DraftDeclaration) {
    const existingDeclaration = this.draftDeclarations().find(
      (summary) => summary.id === draftDeclaration.id
    );

    const missingFields = this.checkMissingFields(draftDeclaration);

    const updatedDeclaration: DraftDeclaration = {
      ...draftDeclaration,
      state: 'updated',
      status: existingDeclaration?.status ?? 'enabled',
      missingFields,
    };

    this.draftDeclarations.update((declarations) => {
      const index = declarations.findIndex(
        (d) => d.id === updatedDeclaration.id
      );

      if (index === -1) return declarations;

      return [
        ...declarations.slice(0, index),
        updatedDeclaration,
        ...declarations.slice(index + 1),
      ];
    });

    this.table.renderRows();
    this.editedDebs.set(draftDeclaration.id, true);
  }

  private add(draftDeclaration: DraftDeclaration) {
    const newDraftDeclaration: DraftDeclaration = {
      ...draftDeclaration,
      id: GuidHelper.newGuid(),
      state: 'added',
      status: 'enabled',
    };

    this.draftDeclarations.update((declarations) => {
      return [newDraftDeclaration, ...declarations];
    });

    this.table.renderRows();
    this.editedDebs.set(newDraftDeclaration.id, true);
  }

  private checkMissingFields(declaration: DraftDeclaration): string[] {
    const fieldMappings: {
      property: keyof DraftDeclaration;
      translationKey: string;
    }[] = [
      { property: 'euNomenclature', translationKey: 'table.nomenclature' },
      {
        property: 'destinationCountryCode',
        translationKey: 'table.destination-country-code',
      },
      { property: 'invoicedAmount', translationKey: 'table.invoiced-amount' },
      {
        property: 'statisticalProcedureCode',
        translationKey: 'table.statistical-procedure-code',
      },
      { property: 'vatNumber', translationKey: 'table.vat-number' },
    ];

    return fieldMappings
      .filter((mapping) => {
        const value = declaration[mapping.property];
        return (
          value === undefined ||
          value === null ||
          (typeof value === 'string' && value.trim() === '')
        );
      })
      .map((mapping) =>
        this.translocoService.translate(
          `deb.deb-declaration-table-component.${mapping.translationKey}`
        )
      );
  }
}
