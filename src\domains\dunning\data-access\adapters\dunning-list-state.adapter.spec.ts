import {
  anyDunningInvoice,
  fakeDunning1,
  fakeDunning2,
  <PERSON>ingLevel,
  DunningNextLevelProposal,
  Dunning,
  DunningsToSaveAll,
  DunningsToSave,
} from '@gc/dunning/models';
import { DunningListState } from '../stores/dunning-list/models/dunning-list-state.model';
import { DunningListStateAdapter } from './dunning-list-state.adapter';

describe('DunningListStateAdapter', () => {
  const firstPage = 1;

  const dunningPages = new Map<number, Dunning[]>();
  dunningPages.set(firstPage, [fakeDunning1, fakeDunning2]);

  const fakeInvoice1 = {
    ...anyDunningInvoice,
    id: 'abcd-efgh-ijkl-mnop1',
    dueDatesIds: ['aaa-bbb-ccc-ddd1-1', 'aaa-bbb-ccc-ddd1-2'],
  };
  const fakeInvoice2 = {
    ...anyDunningInvoice,
    id: 'abcd-efgh-ijkl-mnop2',
    dueDatesIds: ['aaa-bbb-ccc-ddd2-1', 'aaa-bbb-ccc-ddd2-2'],
  };

  const nextLevelProposal: DunningNextLevelProposal[] = [
    {
      level: DunningLevel.NOT_EXISTING,
      nextLevel: DunningLevel.LEVEL_1,
    },
    {
      level: DunningLevel.LEVEL_1,
      nextLevel: DunningLevel.LEVEL_2,
    },
    {
      level: DunningLevel.LEVEL_2,
      nextLevel: DunningLevel.LEVEL_3,
    },
    {
      level: DunningLevel.LEVEL_3,
      nextLevel: DunningLevel.LEVEL_4,
    },
    {
      level: DunningLevel.LEVEL_4,
      nextLevel: DunningLevel.LEVEL_4,
    },
  ];

  const state: DunningListState = {
    defaultDueDateFrom: new Date('1995-12-17'),
    currentPage: firstPage,
    filters: undefined,
    elementsPerPage: 2,
    totalElements: 7,
    dunningPages: dunningPages,
    saveResult: {
      dueDateSuccess: undefined,
      dueDateFails: undefined,
      dunningTreatmentId: undefined,
    },
    dueDates: [],
    invoices: [fakeInvoice1, fakeInvoice2],
    selectedInvoicesIds: [fakeInvoice1.id],
    globalSelectionActivated: false,
    totalDunningAmount: 0,
    loadDunningStatus: 'NOT_LOADED',
  };

  describe('toDunningToSave method', () => {
    describe('Given a state with 2 valid invoices', () => {
      beforeEach(() => {
        state.invoices = [
          fakeInvoice1,
          { ...fakeInvoice2, selectedLevel: DunningLevel.LEVEL_1 },
        ];
      });
      describe('When one invoice is selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [fakeInvoice1.id];
          state.dueDates = [
            {
              id: fakeInvoice1.dueDatesIds[0],
              date: new Date(),
              balance: 10,
              amount: 0,
              isDue: false,
            },
            {
              id: fakeInvoice1.dueDatesIds[1],
              date: new Date(),
              balance: 10,
              amount: 10,
              isDue: true,
            },
          ];
        });
        it('The due dueDates of the selected invoice are returned', () => {
          const expectedDunningsToSave: DunningsToSave = {
            dueDates: [
              {
                id: fakeInvoice1.dueDatesIds[1],
                invoicesDueUpTo: state.defaultDueDateFrom,
                level: DunningLevel.LEVEL_2,
              },
            ],
          };
          const returnedDunningsToSave =
            DunningListStateAdapter.toDunningToSave(state, nextLevelProposal);

          expect(returnedDunningsToSave).toEqual(expectedDunningsToSave);
        });
      });

      describe('When two invoices are selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [fakeInvoice1.id, fakeInvoice2.id];
          state.dueDates = [
            {
              id: fakeInvoice1.dueDatesIds[0],
              date: new Date(),
              balance: 10,
              amount: 10,
              isDue: true,
            },
            {
              id: fakeInvoice1.dueDatesIds[1],
              date: new Date(),
              balance: 11,
              amount: 11,
              isDue: true,
            },
            {
              id: fakeInvoice2.dueDatesIds[0],
              date: new Date(),
              balance: 12,
              amount: 12,
              isDue: true,
            },
            {
              id: fakeInvoice2.dueDatesIds[1],
              date: new Date(),
              balance: 13,
              amount: 13,
              isDue: true,
            },
          ];
        });
        it('The due dates of the selected invoices are returned', () => {
          const expectedDunningsToSave: DunningsToSave = {
            dueDates: [
              {
                id: fakeInvoice1.dueDatesIds[0],
                invoicesDueUpTo: state.defaultDueDateFrom,
                level: DunningLevel.LEVEL_2,
              },
              {
                id: fakeInvoice1.dueDatesIds[1],
                invoicesDueUpTo: state.defaultDueDateFrom,
                level: DunningLevel.LEVEL_2,
              },
              {
                id: fakeInvoice2.dueDatesIds[0],
                invoicesDueUpTo: state.defaultDueDateFrom,
                level: DunningLevel.LEVEL_1,
              },
              {
                id: fakeInvoice2.dueDatesIds[1],
                invoicesDueUpTo: state.defaultDueDateFrom,
                level: DunningLevel.LEVEL_1,
              },
            ],
          };
          const returnedDunningsToSave =
            DunningListStateAdapter.toDunningToSave(state, nextLevelProposal);
          expect(returnedDunningsToSave).toEqual(expectedDunningsToSave);
        });
      });

      describe('When no invoice is selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [];
        });
        it('No due date is returned', () => {
          const expectedDunningsToSave: DunningsToSave = {
            dueDates: [],
          };
          const returnedDunningsToSave =
            DunningListStateAdapter.toDunningToSave(state, nextLevelProposal);
          expect(returnedDunningsToSave).toEqual(expectedDunningsToSave);
        });
      });
    });
  });

  describe('toDunningToSaveAll method', () => {
    const fakeCompanyId = '53379c22-2383-4ad7-99d7-24c2e94ce5c4';
    describe('Given a state with 2 valid invoices and 1 filter enterprise', () => {
      beforeEach(() => {
        state.invoices = [fakeInvoice1, fakeInvoice2];
        state.filters = {
          dueDateFrom: undefined,
          companiesId: [fakeCompanyId],
        };
      });
      describe('When one invoice is selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [fakeInvoice1.id];
        });
        it('The due dates of the unselected invoice are returned', () => {
          const expectedDunningsToSaveAll: DunningsToSaveAll = {
            dueDateIdsExcluded: [...fakeInvoice2.dueDatesIds],
            dueDates: [],
            invoicesDueUpTo: state.defaultDueDateFrom,
            enterpriseIds: [fakeCompanyId],
          };
          const returnedDunningsToSaveAll =
            DunningListStateAdapter.toDunningToSaveAll(
              state,
              nextLevelProposal
            );

          expect(returnedDunningsToSaveAll).toEqual(expectedDunningsToSaveAll);
        });
      });

      describe('When two invoices are selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [fakeInvoice1.id, fakeInvoice2.id];
        });
        it('No due dates returned', () => {
          const expectedDunningsToSaveAll: DunningsToSaveAll = {
            dueDateIdsExcluded: [],
            dueDates: [],
            invoicesDueUpTo: state.defaultDueDateFrom,
            enterpriseIds: [fakeCompanyId],
          };
          const returnedDunningsToSaveAll =
            DunningListStateAdapter.toDunningToSaveAll(
              state,
              nextLevelProposal
            );

          expect(returnedDunningsToSaveAll).toEqual(expectedDunningsToSaveAll);
        });
      });

      describe('When no invoice is selected', () => {
        beforeEach(() => {
          state.selectedInvoicesIds = [];
        });
        it('No the due date of all the invoices are returned', () => {
          const expectedDunningsToSaveAll: DunningsToSaveAll = {
            dueDateIdsExcluded: [
              ...fakeInvoice1.dueDatesIds,
              ...fakeInvoice2.dueDatesIds,
            ],
            dueDates: [],
            invoicesDueUpTo: state.defaultDueDateFrom,
            enterpriseIds: [fakeCompanyId],
          };
          const returnedDunningsToSaveAll =
            DunningListStateAdapter.toDunningToSaveAll(
              state,
              nextLevelProposal
            );

          expect(returnedDunningsToSaveAll).toEqual(expectedDunningsToSaveAll);
        });
      });
    });
  });
});
