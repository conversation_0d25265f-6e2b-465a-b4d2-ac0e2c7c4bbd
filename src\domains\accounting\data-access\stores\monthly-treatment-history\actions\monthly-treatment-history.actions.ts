import {
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsEnum,
} from '@gc/accounting/models';
import { createActionGroup, emptyProps, props } from '@ngrx/store';

export const monthlyTreatmentHistoryActions = createActionGroup({
  source: 'Monthly Treatment history',
  events: {
    'Load selected company enclosed years': emptyProps(),
    'Load selected company enclosed years success': props<{
      years: number[];
    }>(),
    'Load selected year enclosed months': emptyProps(),
    'Load selected year enclosed months success': props<{
      months: Date[];
    }>(),
    'Change selected enclosed year': props<{
      year: number | null;
    }>(),
    'Change selected enclosed month': props<{
      month: Date | null;
    }>(),
    'Load selected month available editions': emptyProps(),
    'Load selected month available editions success': props<{
      availableMonthlyEditions: MonthlyTreatmentAvailableEdition[];
    }>(),
    'Toggle monthly treatment edition selection ': props<{
      edition: MonthlyTreatmentEditionsEnum;
    }>(),
    'Unclose selected month': emptyProps(),
    'Unclose selected month success': emptyProps(),
    'Unclose selected month error': emptyProps(),
  },
});
