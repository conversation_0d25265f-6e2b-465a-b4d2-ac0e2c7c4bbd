import { hasMissingParameters } from './missing-parameters.rule';
import { DebParameters } from '@gc/core/deb/domains/models';

describe('hasMissingParameters', () => {
  const completeParameters: DebParameters = {
    authorizationNumber: 'AUTH123',
    declarantName: 'Test Declarant',
    declarationNumber: 123,
    declarationType: 'light',
  };

  it('should return true when parameters is undefined', () => {
    expect(hasMissingParameters(undefined)).toBe(true);
  });

  it('should return true when parameters is an empty object', () => {
    expect(hasMissingParameters({} as DebParameters)).toBe(true);
  });

  it('should return true when authorizationNumber is missing', () => {
    const params: DebParameters = {
      ...completeParameters,
      authorizationNumber: undefined,
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return true when authorizationNumber is empty string', () => {
    const params: DebParameters = {
      ...completeParameters,
      authorizationNumber: '',
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return true when declarantName is missing', () => {
    const params: DebParameters = {
      ...completeParameters,
      declarantName: undefined,
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return true when declarantName is empty string', () => {
    const params: DebParameters = {
      ...completeParameters,
      declarantName: '',
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return true when declarationNumber is missing', () => {
    const params: DebParameters = {
      ...completeParameters,
      declarationNumber: undefined,
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return true when declarationType is missing', () => {
    const params: DebParameters = {
      ...completeParameters,
      declarationType: undefined,
    };
    expect(hasMissingParameters(params)).toBe(true);
  });

  it('should return false when all required parameters are present', () => {
    expect(hasMissingParameters(completeParameters)).toBe(false);
  });

  it('should return false when all required parameters are present with declarationType as "full"', () => {
    const params: DebParameters = {
      ...completeParameters,
      declarationType: 'full',
    };
    expect(hasMissingParameters(params)).toBe(false);
  });
});
