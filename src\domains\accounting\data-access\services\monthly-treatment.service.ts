import { Injectable, inject } from '@angular/core';
import {
  EnclosingMonthInformations,
  MonthlyAccountingCloseUntilMonth,
  MonthlyAccountingEncloseParameters,
  MonthlyAccountingReportDocumentsIds,
  MonthlyAccountingUncloseParameters,
  MonthlyTreatmentAvailableEdition,
  MonthlyTreatmentEditionsFiltersModel,
  SalesByProductFilters,
  SalesDetailsFilters,
} from '@gc/accounting/models';

import {
  BusinessResultAPIApi,
  BooleanBusinessResultAPIApi,
  GuidBusinessResultAPIApi,
  MonthlyAccountingReportsAvailabilityBusinessResultAPIApi,
  MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi,
  MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi,
  MonthlyAccountingReportsDocumentsBusinessResultAPIApi,
  MonthlyAccountingReportsMonthsBusinessResultAPIApi,
  MonthlyAccountingReportsStartMonthsBusinessResultAPIApi,
  MonthlyAccountingReportsYearsBusinessResultAPIApi,
  LegacyApiService,
} from '@gc/shared/api/data-access';
import { DateAdapter } from '@gc/shared/utils';
import { BehaviorSubject, map, Observable, take, tap } from 'rxjs';
import { MonthlyAccountingCloseUntilMonthAdapter } from '../adapters/monthly-accounting-close-until-month.adapter';
import { MonthlyAccountingEncloseParametersAdapter } from '../adapters/monthly-accounting-enclose-parameters.adapter';
import { MonthlyTreatmentAvailableEditionsAdapter } from '../adapters/monthly-treatment-available-editions.adapter';
import { MonthlyTreatmentEditionsFiltersAdapter } from '../adapters/monthly-treatment-editions-filters.adapter';
import { SalesByProductFiltersAdapter } from '../adapters/sales-by-product-filters.adapter';
import { SalesDetailsFiltersAdapter } from '../adapters/sales-details-filters.adapter';
import { MonthlyAccountingReportDocumentsIdsAdapter } from '../adapters/monthly-acounting-report-documents-ids.adapter';
import { LoadingStatus } from '@gc/shared/models';

@Injectable({
  providedIn: 'root',
})
export class MonthlyTreatmentService {
  private readonly _legacyApiService = inject(LegacyApiService);

  private readonly _loadingStatusStartMonths$$ =
    new BehaviorSubject<LoadingStatus>('NOT_LOADED');

  get loadingStatusStartMonths$(): Observable<LoadingStatus> {
    return this._loadingStatusStartMonths$$.asObservable();
  }

  getCompanyEnclosedYears(companyId: string): Observable<number[]> {
    return this._legacyApiService
      .monthlyAccountingReportsGetClosedMonthlyTreatmentYears(companyId)
      .pipe(
        map(
          (br: MonthlyAccountingReportsYearsBusinessResultAPIApi) =>
            br.result!.years
        )
      );
  }

  getCompanyYearEnclosedMonths(
    companyId: string,
    year: number
  ): Observable<Date[]> {
    return this._legacyApiService
      .monthlyAccountingReportsGetClosedMonthlyTreatmentMonths(companyId, year)
      .pipe(
        map((br: MonthlyAccountingReportsMonthsBusinessResultAPIApi) =>
          br.result!.months.map((month: number) => {
            const date = new Date();
            date.setFullYear(year, month - 1, 1);
            return date;
          })
        )
      );
  }

  getAvailableEditions(
    companyId: string,
    month: Date
  ): Observable<MonthlyTreatmentAvailableEdition[]> {
    return this._legacyApiService
      .monthlyAccountingReportsGetMonthlyTreatmentDocumentsExistance(
        companyId,
        DateAdapter.dateToStringAPIWithoutDay(month)!
      )
      .pipe(
        map((br: MonthlyAccountingReportsAvailabilityBusinessResultAPIApi) =>
          MonthlyTreatmentAvailableEditionsAdapter.fromApi(br.result!)
        )
      );
  }

  getDefaultFilters(): Observable<MonthlyTreatmentEditionsFiltersModel> {
    return this._legacyApiService
      .monthlyAccountingReportsLoadDefaultParameters()
      .pipe(
        map(
          (br: MonthlyAccountingReportsDefaultParameterBusinessResultAPIApi) =>
            MonthlyTreatmentEditionsFiltersAdapter.fromApi(br.result)
        )
      );
  }

  getSalesByProductTreatmentId(
    filters: SalesByProductFilters
  ): Observable<string> {
    return this._legacyApiService
      .stimulsoftTreatmentsGenerateTreatmentSalesByProductCategory(
        SalesByProductFiltersAdapter.toApi(filters)
      )
      .pipe(map((br: GuidBusinessResultAPIApi) => br.result!));
  }

  getSalesDetailsTreatmentId(filters: SalesDetailsFilters): Observable<string> {
    return this._legacyApiService
      .stimulsoftTreatmentsGenerateTreatmentSalesDetails(
        SalesDetailsFiltersAdapter.toApi(filters)
      )
      .pipe(map((br: GuidBusinessResultAPIApi) => br.result!));
  }

  getAccountingDefaultMonth(
    companyId: string
  ): Observable<EnclosingMonthInformations | null> {
    return this._legacyApiService
      .monthlyAccountingReportsLoadDefaultMonth(companyId)
      .pipe(
        map((br: MonthlyAccountingReportsDefaultMonthBusinessResultAPIApi) =>
          br.result?.month
            ? {
                enclosureMonth: DateAdapter.dateFromStringAPI(br.result.month),
                hasAlreadyEnclosedMonths: !br.result?.noClosedMonths,
              }
            : null
        )
      );
  }

  getAvailableStartMonths(companyId: string): Observable<Date[]> {
    this._loadingStatusStartMonths$$.next('IN_PROGRESS');
    return this._legacyApiService
      .monthlyAccountingReportsGetAvailableStartMonths(companyId)
      .pipe(
        map((br: MonthlyAccountingReportsStartMonthsBusinessResultAPIApi) =>
          br.result!.months.map(
            (dateMonth) => DateAdapter.dateFromStringAPI(dateMonth)!
          )
        ),
        tap(() => this._loadingStatusStartMonths$$.next('LOADED'))
      );
  }

  saveFilters(
    monthlyFilters: MonthlyTreatmentEditionsFiltersModel
  ): Observable<BusinessResultAPIApi> {
    return this._legacyApiService
      .monthlyAccountingReportsSaveDefaultParameters(
        MonthlyTreatmentEditionsFiltersAdapter.toApi(monthlyFilters)
      )
      .pipe(take(1));
  }

  checkEmptyData(
    companyId: string,
    dateFrom: Date,
    dateTo: Date
  ): Observable<boolean | undefined> {
    return this._legacyApiService
      .monthlyAccountingReportsEmptyData(
        companyId,

        DateAdapter.dateToStringAPI(dateFrom) as string,
        DateAdapter.dateToStringAPI(dateTo) as string
      )
      .pipe(
        take(1),
        map((br: BooleanBusinessResultAPIApi) => br.result)
      );
  }

  encloseMonth(
    monthlyAccountingEncloseParameters: MonthlyAccountingEncloseParameters
  ): Observable<void> {
    return this._legacyApiService
      .monthlyAccountingReportsCloseMonth(
        MonthlyAccountingEncloseParametersAdapter.toApi(
          monthlyAccountingEncloseParameters
        )
      )
      .pipe(
        take(1),
        map(() => void 0)
      );
  }

  getExistingReportsIds(
    companyId: string,
    month: Date
  ): Observable<MonthlyAccountingReportDocumentsIds | undefined> {
    return this._legacyApiService
      .monthlyAccountingReportsGetMonthlyTreatmentDocuments(
        companyId,
        DateAdapter.dateToStringAPIWithoutDay(month)!
      )
      .pipe(
        map((br: MonthlyAccountingReportsDocumentsBusinessResultAPIApi) =>
          br.result
            ? MonthlyAccountingReportDocumentsIdsAdapter.fromApi(br.result)
            : undefined
        )
      );
  }

  uncloseMonth(
    monthlyAccountingUncloseParameters: MonthlyAccountingUncloseParameters
  ): Observable<void> {
    const monthApi = DateAdapter.dateToStringAPIWithoutDay(
      monthlyAccountingUncloseParameters.month
    )!;
    return this._legacyApiService
      .monthlyAccountingReportsUndoCloseMonth(
        monthlyAccountingUncloseParameters.companyId,
        monthApi
      )
      .pipe(
        take(1),
        map(() => void 0)
      );
  }

  saveDefaultEncloseMonth(
    monthlyAccountingCloseUntilMonth: MonthlyAccountingCloseUntilMonth
  ): Observable<void> {
    return this._legacyApiService
      .monthlyAccountingReportsCloseUntilMonth(
        MonthlyAccountingCloseUntilMonthAdapter.toApi(
          monthlyAccountingCloseUntilMonth
        )
      )
      .pipe(map(() => void 0));
  }
}
