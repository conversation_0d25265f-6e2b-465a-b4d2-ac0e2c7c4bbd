import { waitForAsync } from '@angular/core/testing';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import {
  Mat<PERSON><PERSON>er,
  MatDrawerContainer,
  MatDrawerContent,
} from '@angular/material/sidenav';
import { Router } from '@angular/router';
import { MainContainerComponent } from '@gc/core/navigation/feature';
import { URL_PATHS } from '@gc/core/navigation/models';
import {
  dunningActions,
  dunningListActions,
  DunningListState,
  DunningListStoreModule,
  DunningStoreModule,
  initialState,
  selectTotalSelectedDunnings,
} from '@gc/dunning/data-access';

import {
  anyDunningSaveDueDateFail,
  anyDunningSaveDueDateSuccess,
  DunningSaveDueDateFail,
  DunningSaveDueDateSuccess,
  DunningsSaveResult,
} from '@gc/dunning/models';
import {
  DunningFilterResultsComponent,
  DunningFiltersComponent,
  DunningListComponent,
} from '@gc/dunning/feature';
import { DunningMoreActionsButtonComponent } from '@gc/dunning/ui';
import { DialogService } from '@gc/shared/ui';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { Store } from '@ngrx/store';
import { provideMockStore } from '@ngrx/store/testing';
import { MockComponents, MockDirectives, MockModule } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import { DunningsComponent } from './dunnings.component';

describe('DunningsComponent', () => {
  let spectator: Spectator<DunningsComponent>;

  let store: Store;
  let router: Router;
  let dialogService: DialogService;
  let translocoService: TranslocoService;

  const dunningTreatmentId = '111222333444555666777888999';

  const fakeDunningSaveDueDateSuccess: DunningSaveDueDateSuccess = {
    ...anyDunningSaveDueDateSuccess,
    dueDateId: '12836ef1-16fb-55b7-8001-dc69de6403f4',
  };

  const dueDateSuccessList: Array<DunningSaveDueDateSuccess> = [
    anyDunningSaveDueDateSuccess,
    fakeDunningSaveDueDateSuccess,
  ];

  const fakeDunningSaveDueDateFail: DunningSaveDueDateFail = {
    ...anyDunningSaveDueDateFail,
    dueDateId: '12836ef1-16fb-55b7-8001-dc69de6403f4',
  };

  const dueDateFailsList: Array<DunningSaveDueDateFail> = [
    anyDunningSaveDueDateFail,
    fakeDunningSaveDueDateFail,
  ];

  const saveResultInitial: DunningsSaveResult = {
    dueDateSuccess: dueDateSuccessList,
    dueDateFails: dueDateFailsList,
    dunningTreatmentId: dunningTreatmentId,
  };

  const createComponent = createComponentFactory({
    component: DunningsComponent,
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(
        DunningListComponent,
        MainContainerComponent,
        MatButton,
        DunningMoreActionsButtonComponent,
        MatIcon,
        MatDrawerContainer,
        MatDrawer,
        MatDrawerContent,
        DunningFilterResultsComponent,
        DunningFiltersComponent
      ),
      MockModule(DunningListStoreModule),
      MockModule(DunningStoreModule),
    ],
    mocks: [Store, Router, DialogService, TranslocoService],
    // ...
    providers: [
      provideMockStore<DunningListState>({
        initialState,
      }),
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    store = spectator.inject(Store);
    router = spectator.inject(Router);
    dialogService = spectator.inject(DialogService);
    translocoService = spectator.inject(TranslocoService);
  });

  describe('ngOnInit', () => {
    let selectTotalSelectedDunningsSpy: jest.SpyInstance<Observable<number>>;
    let dispatchSpy: jest.SpyInstance<void>;
    let listenDunningSaveAndOpenLetterSpy: jest.SpyInstance<void>;
    const anyTotalSelectedDunnings = 0;

    beforeEach(() => {
      selectTotalSelectedDunningsSpy = jest.spyOn(
        store,
        'select'
      ) as jest.SpyInstance<Observable<number>>;

      listenDunningSaveAndOpenLetterSpy = jest.spyOn(
        spectator.component,
        'listenDunningSaveAndOpenLetter'
      ) as jest.SpyInstance<void>;

      dispatchSpy = jest.spyOn(store, 'dispatch');

      selectTotalSelectedDunningsSpy.mockReturnValue(
        of(anyTotalSelectedDunnings)
      );

      spectator.detectChanges();
    });

    describe('given any state', () => {
      it('should dispatch initDunningPagination action', () => {
        expect(dispatchSpy).toHaveBeenCalledWith(
          dunningListActions.initDunningPagination()
        );
      });

      it('should dispatch fetchDunningLevelConfig action', () => {
        expect(dispatchSpy).toHaveBeenCalledWith(
          dunningActions.fetchDunningLevelConfig()
        );
      });

      it('should call select method of Store with selectTotalSelectedDunnings selector', () => {
        expect(selectTotalSelectedDunningsSpy).toHaveBeenCalledWith(
          selectTotalSelectedDunnings
        );
      });
      it('should call listenDunningSaveAndOpenLetter method', () => {
        expect(listenDunningSaveAndOpenLetterSpy).toHaveBeenCalled();
      });
    });
  });

  describe('navigateTo', () => {
    let navigateSpy: jest.SpyInstance<Promise<boolean>>;
    beforeEach(() => {
      navigateSpy = jest.spyOn(router, 'navigate');
    });

    it('should call navigate of router', () => {
      const path = 'path';
      spectator.component.navigateTo(path);
      expect(navigateSpy).toHaveBeenCalledWith([path]);
    });
  });

  describe('openSaveDunningDialog (with openConfirmSaveDunningDialog and openInfoSaveDunningDialog)', () => {
    let selectTranslateConfirmSpy: jest.SpyInstance<Observable<string>>;
    let dialogServiceConfirmSpy: jest.SpyInstance<Observable<boolean>>;

    let selectTranslateInfoSpy: jest.SpyInstance<Observable<string>>;
    let dialogServiceInfoSpy: jest.SpyInstance<Observable<void>>;

    beforeEach(() => {
      selectTranslateConfirmSpy = jest.spyOn(
        translocoService,
        'selectTranslate'
      ) as jest.SpyInstance<Observable<string>>;

      dialogServiceConfirmSpy = jest.spyOn(dialogService, 'confirm');
      dialogServiceInfoSpy = jest.spyOn(dialogService, 'info');

      selectTranslateInfoSpy = jest.spyOn(
        translocoService,
        'selectTranslate'
      ) as jest.SpyInstance<Observable<string>>;
    });

    describe('isDialogOpened state', () => {
      describe('given a state where totalSelectedDunnings emit any value', () => {
        const anyTotalSelectedDunnings = 10;

        const confirmReturnValue = false;
        const messageConfirm =
          'Vous allez relancer 10 clients, confirmez-vous ?';

        beforeEach(() => {
          spectator.component.totalSelectedDunnings$ = of(
            anyTotalSelectedDunnings
          );

          selectTranslateConfirmSpy.mockReturnValue(of(messageConfirm));

          dialogServiceConfirmSpy.mockReturnValue(of(confirmReturnValue));
        });

        describe('and isDialogOpened is true', () => {
          beforeEach(() => {
            spectator.component.isDialogOpened = true;
          });

          it("shouldn't call selectTranslate methods of TranslocoService", waitForAsync(() => {
            spectator.component.openSaveDunningDialog();
            expect(selectTranslateInfoSpy).not.toHaveBeenCalled();
            expect(selectTranslateConfirmSpy).not.toHaveBeenCalled();
          }));
          it("shouldn't call confirm and info methods of DialogService", waitForAsync(() => {
            spectator.component.openSaveDunningDialog();
            expect(dialogServiceInfoSpy).not.toHaveBeenCalled();
            expect(dialogServiceConfirmSpy).not.toHaveBeenCalled();
          }));
        });
        describe('and isDialogOpened is false', () => {
          beforeEach(() => {
            spectator.component.isDialogOpened = false;
          });

          it('should call confirm method of DialogService with messageInfo params', waitForAsync(() => {
            spectator.component.openSaveDunningDialog();
            expect(dialogServiceConfirmSpy).toHaveBeenCalledWith(
              messageConfirm
            );
          }));

          it('should set isDialogOpened back to false', waitForAsync(() => {
            spectator.component.openSaveDunningDialog();
            expect(spectator.component.isDialogOpened).toBe(false);
          }));
        });
      });
    });

    describe('open the correct dialog', () => {
      describe('given a state where totalSelectedDunnings Observable emit', () => {
        describe('with a value strictly greater than  0', () => {
          const anyTotalSelectedDunnings = 10;

          const messageConfirm =
            'Vous allez relancer 10 clients, confirmez-vous ?';

          beforeEach(() => {
            spectator.component.totalSelectedDunnings$ = of(
              anyTotalSelectedDunnings
            );

            selectTranslateConfirmSpy.mockReturnValue(of(messageConfirm));
          });

          describe('and isDialogOpened is false', () => {
            const confirmReturnValue = false;
            beforeEach(() => {
              spectator.component.isDialogOpened = false;
              dialogServiceConfirmSpy.mockReturnValue(of(confirmReturnValue));
            });

            it('should call selectTranslate method of TranslocoService with params to get the confirm message', waitForAsync(() => {
              spectator.component.openSaveDunningDialog();

              expect(selectTranslateConfirmSpy).toHaveBeenCalledWith(
                'dunning-page.save.confirm.message',
                { nbClients: anyTotalSelectedDunnings },
                'dunning'
              );
            }));

            it('should call confirm method of DialogService with messageConfirm params', waitForAsync(() => {
              spectator.component.openSaveDunningDialog();
              expect(dialogServiceConfirmSpy).toHaveBeenCalledWith(
                messageConfirm
              );
            }));
          });
        });

        describe('with a value equal to 0', () => {
          const anyTotalSelectedDunnings = 0;

          const messageInfo =
            'Veuillez sélectionner au moins un client ou une facture à relancer';
          beforeEach(() => {
            spectator.component.totalSelectedDunnings$ = of(
              anyTotalSelectedDunnings
            );

            selectTranslateInfoSpy.mockReturnValue(of(messageInfo));
          });

          describe('and isDialogOpened is false', () => {
            beforeEach(() => {
              spectator.component.isDialogOpened = false;
              dialogServiceInfoSpy.mockReturnValue(of(void 0));
            });

            it('should call selectTranslate method of TranslocoService with params to get the info message', waitForAsync(() => {
              spectator.component.openSaveDunningDialog();

              expect(selectTranslateInfoSpy).toHaveBeenCalledWith(
                'dunning-page.save.info.message',
                {},
                'dunning'
              );
            }));

            it('should call info method of DialogService with messageInfo params', waitForAsync(() => {
              spectator.component.openSaveDunningDialog();
              expect(dialogServiceInfoSpy).toHaveBeenCalledWith(messageInfo);
            }));
          });
        });
      });
    });
  });

  describe('listenDunningSaveAndOpenLetter', () => {
    let openDueDateFailDialogSpy: jest.SpyInstance<
      Observable<DunningsSaveResult | undefined>
    >;

    beforeEach(() => {
      openDueDateFailDialogSpy = jest.spyOn(
        spectator.component,
        'openDueDateFailDialog$'
      );
    });

    describe('given a state with dueDateFails of selectResult', () => {
      let selectSaveResultSpy: jest.SpyInstance<
        Observable<DunningsSaveResult | undefined>
      >;
      let navigateSpy: jest.SpyInstance<Promise<boolean>>;
      describe('where dueDateFails length is greater than 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: dueDateSuccessList,
            dueDateFails: dueDateFailsList,
            dunningTreatmentId: dunningTreatmentId,
          };

          selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<DunningsSaveResult | undefined>
          >;
          selectSaveResultSpy.mockReturnValue(of(saveResult));
          openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

          navigateSpy = jest.spyOn(router, 'navigate');
          navigateSpy.mockResolvedValue(true);
        });

        it('should call error method of DialogService', waitForAsync(() => {
          spectator.component.listenDunningSaveAndOpenLetter();
          expect(openDueDateFailDialogSpy).toHaveBeenCalledWith(saveResult);
        }));
      });

      describe('and dueDateFails length is equal to 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: dueDateSuccessList,
            dueDateFails: [],
            dunningTreatmentId: dunningTreatmentId,
          };

          selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<DunningsSaveResult | undefined>
          >;
          selectSaveResultSpy.mockReturnValue(of(saveResult));
          openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

          navigateSpy = jest.spyOn(router, 'navigate');
          navigateSpy.mockResolvedValue(true);
        });

        it("should'nt call error method of DialogService", waitForAsync(() => {
          spectator.component.listenDunningSaveAndOpenLetter();
          expect(openDueDateFailDialogSpy).not.toHaveBeenCalled();
        }));
      });
    });

    describe('given a state without dueDateFails of selectResult', () => {
      let selectSaveResultSpy: jest.SpyInstance<
        Observable<DunningsSaveResult | undefined>
      >;
      let navigateSpy: jest.SpyInstance<Promise<boolean>>;
      let saveResult: DunningsSaveResult;
      beforeEach(() => {
        saveResult = {
          ...saveResultInitial,
          dueDateSuccess: dueDateSuccessList,
          dueDateFails: undefined,
          dunningTreatmentId: dunningTreatmentId,
        };

        selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
          Observable<DunningsSaveResult | undefined>
        >;
        selectSaveResultSpy.mockReturnValue(of(saveResult));

        openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

        navigateSpy = jest.spyOn(router, 'navigate');
        navigateSpy.mockResolvedValue(true);
      });

      it("shouldn't call error method of DialogService", waitForAsync(() => {
        spectator.component.listenDunningSaveAndOpenLetter();
        expect(openDueDateFailDialogSpy).not.toHaveBeenCalled();
      }));
    });

    describe('given state with dueDateSuccess of selectResult', () => {
      let selectSaveResultSpy: jest.SpyInstance<
        Observable<DunningsSaveResult | undefined>
      >;
      let navigateSpy: jest.SpyInstance<Promise<boolean>>;
      describe('where dueDateSuccess length is greater than 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: dueDateSuccessList,
            dueDateFails: undefined,
            dunningTreatmentId: dunningTreatmentId,
          };

          selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<DunningsSaveResult | undefined>
          >;
          selectSaveResultSpy.mockReturnValue(of(saveResult));
          openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

          navigateSpy = jest.spyOn(router, 'navigate');
          navigateSpy.mockResolvedValue(true);
        });
        it('should call navigate method of Router', waitForAsync(() => {
          spectator.component.listenDunningSaveAndOpenLetter();
          expect(navigateSpy).toHaveBeenCalledWith([URL_PATHS.dunningsViewer]);
        }));
      });

      describe('and dueDateSuccess length is equal to 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: [],
            dueDateFails: undefined,
            dunningTreatmentId: dunningTreatmentId,
          };

          selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
            Observable<DunningsSaveResult | undefined>
          >;
          selectSaveResultSpy.mockReturnValue(of(saveResult));
          openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

          navigateSpy = jest.spyOn(router, 'navigate');
          navigateSpy.mockResolvedValue(true);
        });

        it("shouldn't call navigate method of Router", waitForAsync(() => {
          spectator.component.listenDunningSaveAndOpenLetter();
          expect(navigateSpy).not.toHaveBeenCalled();
        }));
      });
    });

    describe('given state without dueDateSuccess of selectResult', () => {
      let selectSaveResultSpy: jest.SpyInstance<
        Observable<DunningsSaveResult | undefined>
      >;
      let navigateSpy: jest.SpyInstance<Promise<boolean>>;
      let saveResult: DunningsSaveResult;
      beforeEach(() => {
        saveResult = {
          ...saveResultInitial,
          dueDateSuccess: undefined,
          dueDateFails: undefined,
          dunningTreatmentId: dunningTreatmentId,
        };

        selectSaveResultSpy = jest.spyOn(store, 'select') as jest.SpyInstance<
          Observable<DunningsSaveResult | undefined>
        >;
        selectSaveResultSpy.mockReturnValue(of(saveResult));

        openDueDateFailDialogSpy.mockReturnValue(of(saveResult));

        navigateSpy = jest.spyOn(router, 'navigate');
        navigateSpy.mockResolvedValue(true);
      });

      it("shouldn't call navigate method of Router", waitForAsync(() => {
        spectator.component.listenDunningSaveAndOpenLetter();
        expect(navigateSpy).not.toHaveBeenCalled();
      }));
    });
  });

  describe('openDueDateFailDialog', () => {
    let dialogServiceErrorSpy: jest.SpyInstance<Observable<void>>;

    beforeEach(() => {
      dialogServiceErrorSpy = jest.spyOn(
        dialogService,
        'error'
      ) as jest.SpyInstance<Observable<void>>;
      dialogServiceErrorSpy.mockReturnValue(of(void 0));
    });

    describe('given a state with dueDateFails of selectResult', () => {
      describe('where dueDateFails length is greater than 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: undefined,
            dueDateFails: dueDateFailsList,
            dunningTreatmentId: dunningTreatmentId,
          };
        });

        it('should call error method of DialogService', waitForAsync(() => {
          const expectedErrorMessages = [
            'Écheance déjà soldée',
            'Écheance déjà soldée',
          ];
          spectator.component.openDueDateFailDialog$(saveResult);
          expect(dialogServiceErrorSpy).toHaveBeenCalledWith(
            expectedErrorMessages
          );
        }));
      });

      describe('where dueDateFails length is equal than 0', () => {
        let saveResult: DunningsSaveResult;
        beforeEach(() => {
          saveResult = {
            ...saveResultInitial,
            dueDateSuccess: undefined,
            dueDateFails: [],
            dunningTreatmentId: dunningTreatmentId,
          };
        });

        it('should throw Error', () => {
          expect(() => {
            spectator.component.openDueDateFailDialog$(saveResult);
          }).toThrow(new Error('dueDateFails from saveResult is undefined'));
        });
      });
    });
    describe('given a state without dueDateFails of selectResult', () => {
      let saveResult: DunningsSaveResult;
      beforeEach(() => {
        saveResult = {
          ...saveResultInitial,
          dueDateSuccess: undefined,
          dueDateFails: undefined,
          dunningTreatmentId: dunningTreatmentId,
        };
      });

      it('should throw Error', () => {
        expect(() => {
          spectator.component.openDueDateFailDialog$(saveResult);
        }).toThrow(new Error('dueDateFails from saveResult is undefined'));
      });
    });
  });
});
