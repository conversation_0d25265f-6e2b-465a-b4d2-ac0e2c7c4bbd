import { waitForAsync } from '@angular/core/testing';
import {
  dunningListInitialState,
  dunningViewerActions,
  selectSaveResult,
} from '@gc/dunning/data-access';
import { DunningsSaveResult } from '@gc/dunning/models';
import {
  StimulsoftPropertiesService,
  StimulsoftViewerContainerComponent,
} from '@gc/shared/stimulsoft/feature';
import {
  CoreStimulsoftProperties,
  REPORT_ID,
  ReportFamilies,
} from '@gc/shared/stimulsoft/models';
import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { PushPipe } from '@ngrx/component';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { MockComponents, MockPipes } from 'ng-mocks';
import { Observable, of } from 'rxjs';
import {
  DunningSupplementaryStimulsoftProperties,
  DunningsViewerComponent,
} from './dunnings-viewer.component';

describe('DunningsViewerComponent', () => {
  let spectator: Spectator<DunningsViewerComponent>;
  let store: MockStore;
  const initialState = dunningListInitialState;

  let stimulsoftPropertiesService: StimulsoftPropertiesService;

  const createComponent = createComponentFactory({
    component: DunningsViewerComponent,
    declarations: [
      MockComponents(StimulsoftViewerContainerComponent),
      MockPipes(PushPipe),
    ],
    mocks: [StimulsoftPropertiesService],
    providers: [provideMockStore({ initialState })],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();

    stimulsoftPropertiesService = spectator.inject(StimulsoftPropertiesService);
    store = spectator.inject(MockStore);
  });

  it('should create', () => {
    expect(spectator).toBeTruthy();
    expect(spectator.component).toBeTruthy();
  });

  describe('stimulsoftProperties$', () => {
    describe('given a state where the saveResult is defined in the store', () => {
      const saveResult: DunningsSaveResult = {
        dueDateSuccess: undefined,
        dueDateFails: undefined,
        dunningTreatmentId: '1ae204b4-329e-4ed5-acc0-6e41d162b7f6',
      };

      beforeEach(() => {
        store.overrideSelector(selectSaveResult, saveResult);
        store.refreshState();
      });

      describe('and the stimulsoftPropertiesService returns the full properties', () => {
        const coreStimulsoftProperties: CoreStimulsoftProperties = {
          authToken: 'Bearer 123245654987',
          culture: 'fr-FR',
        };

        const fullStimulsoftProperties = {
          authToken: coreStimulsoftProperties.authToken,
          culture: coreStimulsoftProperties.culture,
          reportId: REPORT_ID.dunning,
          reportFamily: ReportFamilies.DUNNING,
          dunningTreatmentId: saveResult.dunningTreatmentId,
        };

        let createStimulsoftPropertiesSpy: jest.SpyInstance<
          Observable<unknown>
        >;

        beforeEach(() => {
          createStimulsoftPropertiesSpy = jest
            .spyOn(stimulsoftPropertiesService, 'createStimulsoftProperties$')
            .mockReturnValue(of(fullStimulsoftProperties));
        });

        it('should return a StimulsoftViewerProperties and dispatch deleteDunningTreatmentId', waitForAsync(() => {
          const expectedDunningSupplementaryStimulsoftProperties: DunningSupplementaryStimulsoftProperties =
            {
              dunningTreatmentId: saveResult.dunningTreatmentId!,
            };

          const dispatchSpy = jest.spyOn(store, 'dispatch');
          const expectedAction = dunningViewerActions.deleteDunningSaveResult();

          spectator.detectChanges();

          expect.assertions(3);

          spectator.component.stimulsoftProperties$.subscribe((result) => {
            expect(createStimulsoftPropertiesSpy).toHaveBeenCalledWith(
              REPORT_ID.dunning,
              ReportFamilies.DUNNING,
              expectedDunningSupplementaryStimulsoftProperties
            );
            expect(result).toEqual({
              authToken: coreStimulsoftProperties.authToken,
              culture: coreStimulsoftProperties.culture,
              reportId: REPORT_ID.dunning,
              reportFamily: ReportFamilies.DUNNING,
              dunningTreatmentId: saveResult.dunningTreatmentId,
            });
            expect(dispatchSpy).toHaveBeenCalledWith(expectedAction);
          });
        }));
      });
    });
  });
});
