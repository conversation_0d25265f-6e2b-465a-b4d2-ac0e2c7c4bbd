import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { PeriodicityKind } from '@gc/business-review/models';

/**
 * Validator that checks if the formControls for calendarYear or fiscalYear has a value depending on the selected Periodicity kind.
 */
export function periodicityValidator(): ValidatorFn {
  return (formGroup: AbstractControl): ValidationErrors | null => {
    const periodicityKind = formGroup.get('periodicityKind');
    const calendarYearFC = formGroup.get('calendarYearFC');
    const fiscalYearFC = formGroup.get('fiscalYearFC');
    const hasValue =
      periodicityKind?.value === PeriodicityKind.CALENDAR_YEAR
        ? !calendarYearFC?.value
        : !fiscalYearFC?.value;
    if (periodicityKind && calendarYearFC && fiscalYearFC && hasValue) {
      return { emptyDateRange: true };
    }
    return null;
  };
}
