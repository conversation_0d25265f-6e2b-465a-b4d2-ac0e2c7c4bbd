import { FiscalYearDateRange } from '@gc/fiscal-year/models';
import { CalendarYear } from '../models';

const DATE_FORMAT_CULTURE_FOR_STIMULSOFT = 'en-US';

const JANUARY = 0;
const FIRST = 1;
const DECEMBER = 11;
const THIRTY_ONE = 31;

export class PeriodicityHelper {
  static getDatesRangesFromCalendarYear(
    fromCalendarYear: CalendarYear,
    dateRangesCount: number
  ): string[] {
    return this.getCalendarYearsFromDate(
      dateRangesCount,
      fromCalendarYear.startDate
    ).flatMap((calendarYear) => [
      calendarYear.startDate.toLocaleDateString(
        DATE_FORMAT_CULTURE_FOR_STIMULSOFT
      ),
      calendarYear.endDate.toLocaleDateString(
        DATE_FORMAT_CULTURE_FOR_STIMULSOFT
      ),
    ]);
  }

  static getCalendarYearsFromDate(
    nLastYears: number,
    fromDate: Date
  ): CalendarYear[] {
    return [...Array(nLastYears).keys()].map((pastYears) => {
      const d = new Date(fromDate);
      const y = d.getFullYear();
      d.setFullYear(y - pastYears);
      return {
        startDate: new Date(d.getFullYear(), JANUARY, FIRST),
        endDate: new Date(d.getFullYear(), DECEMBER, THIRTY_ONE),
        viewValue: d.getFullYear().toString(),
      };
    });
  }

  static getDatesRangesFromFiscalYear(
    fromFiscalYear: FiscalYearDateRange | null,
    fiscalYearsList: FiscalYearDateRange[],
    dateRangesCount: number
  ): string[] {
    if (!fromFiscalYear || !fiscalYearsList) return [];
    const fiscalYearsRanges: FiscalYearDateRange[] = [fromFiscalYear];
    let previousFiscalYear = this._getPreviousFiscalYear(
      fromFiscalYear,
      fiscalYearsList
    );
    while (previousFiscalYear && fiscalYearsRanges.length < dateRangesCount) {
      fiscalYearsRanges.push(previousFiscalYear);
      previousFiscalYear = this._getPreviousFiscalYear(
        previousFiscalYear,
        fiscalYearsList
      );
    }
    return fiscalYearsRanges.flatMap((fiscalYear) => [
      fiscalYear.dateFrom.toLocaleDateString(
        DATE_FORMAT_CULTURE_FOR_STIMULSOFT
      ),
      fiscalYear.dateTo.toLocaleDateString(DATE_FORMAT_CULTURE_FOR_STIMULSOFT),
    ]);
  }

  private static _getPreviousFiscalYear(
    currentYear: FiscalYearDateRange,
    fiscalYears: FiscalYearDateRange[]
  ): FiscalYearDateRange | undefined {
    if (currentYear.dateFrom) {
      const expectedEndDateForPreviousYear = new Date(currentYear.dateFrom);
      expectedEndDateForPreviousYear.setDate(
        currentYear.dateFrom.getDate() - 1
      );
      return fiscalYears.find(
        (fiscalYear) =>
          fiscalYear.dateTo?.getTime() ===
          expectedEndDateForPreviousYear.getTime()
      );
    }
    return undefined;
  }
}
