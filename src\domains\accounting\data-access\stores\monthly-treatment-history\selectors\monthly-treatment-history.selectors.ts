import { createFeatureSelector, createSelector } from '@ngrx/store';
import { FEATURE_KEY_MONTHLY_TREATMENT_HISTORY } from '../models/monthly-treatment-history-key.constant';
import { MonthlyTreatmentHistoryState } from '../models/monthly-treatment-history-state.model';
import { selectMonthlyTreatmentCompanyId } from '../../monthly-treatment/selectors/monthly-treatment.selectors';

export const selectMonthlyTreatmentHistoryState =
  createFeatureSelector<MonthlyTreatmentHistoryState>(
    FEATURE_KEY_MONTHLY_TREATMENT_HISTORY
  );

export const selectSelectedEnclosedMonth = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.selectedEnclosedMonth
);

export const selectSelectedEnclosedYear = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.selectedEnclosedYear
);

export const selectEnclosedYears = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.enclosedYears
);

export const selectEnclosedMonthsOfSelectedYear = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.enclosedMonthsOfSelectedYear
);

export const selectSelectedMonthlyEditions = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.selectedMonthlyEditions
);

export const selectAvailableMonthlyEditions = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.availableMonthlyEditions
);

export const selectAvailableMonthlyEditionsLoadingStatus = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) =>
    state.availableMonthlyEditionsLoadingStatus
);

export const selectIsNewestEnclosedMonthSelected = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => {
    const isNewestYearSelected =
      state.enclosedYears !== undefined &&
      state.selectedEnclosedYear !== undefined &&
      state.selectedEnclosedYear === Math.max(...state.enclosedYears);
    const selectedMonth = state.selectedEnclosedMonth?.getMonth();
    const months = state.enclosedMonthsOfSelectedYear?.map((d) => d.getMonth());
    const isNewestMonthSelected =
      selectedMonth !== undefined &&
      months !== undefined &&
      selectedMonth === Math.max(...months);

    return isNewestMonthSelected && isNewestYearSelected;
  }
);

export const selectUncloseStatus = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => state.uncloseSelectedMonthStatus
);

export const selectCompanyIdAndSelectedEnclosedMonth = createSelector(
  selectMonthlyTreatmentCompanyId,
  selectSelectedEnclosedMonth,
  (companyId, selectedEnclosedMonth) => ({
    companyId,
    selectedEnclosedMonth,
  })
);

export const selectIsOldestEnclosedMonthSelected = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => {
    const {
      enclosedYears,
      selectedEnclosedYear,
      enclosedMonthsOfSelectedYear,
      selectedEnclosedMonth,
    } = state;

    const isOldestYearSelected =
      enclosedYears !== undefined &&
      selectedEnclosedYear !== undefined &&
      selectedEnclosedYear === Math.min(...enclosedYears);

    const selectedMonth = selectedEnclosedMonth?.getMonth();
    const months = enclosedMonthsOfSelectedYear?.map((d) => d.getMonth());

    const isOldestMonthSelected =
      selectedMonth !== undefined &&
      months !== undefined &&
      selectedMonth === Math.min(...months);

    return isOldestMonthSelected && isOldestYearSelected;
  }
);

export const selectHasEnclosedMonths = createSelector(
  selectMonthlyTreatmentHistoryState,
  (state: MonthlyTreatmentHistoryState) => {
    return state.enclosedYears ? state.enclosedYears.length > 0 : null;
  }
);

export const selectHasNotEditionToConsult = createSelector(
  selectAvailableMonthlyEditions,
  selectSelectedMonthlyEditions,
  (availableMonthlyEditions, selectedMonthlyEditions) =>
    !availableMonthlyEditions ||
    !availableMonthlyEditions.some((edition) => edition.isAvailable) ||
    !selectedMonthlyEditions.length
);
