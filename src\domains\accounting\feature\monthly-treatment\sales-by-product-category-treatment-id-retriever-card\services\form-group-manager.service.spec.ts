import { Form<PERSON>rray, Form<PERSON>uilder, Validators } from '@angular/forms';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';

import { FormGroupManagerService } from './form-group-manager.service';

describe('FormGroupManagerService', () => {
  let spectator: SpectatorService<FormGroupManagerService>;

  let service: FormGroupManagerService;

  const createService = createServiceFactory({
    service: FormGroupManagerService,
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('init', () => {
    describe('given any state', () => {
      describe('when method is called with default characteristics', () => {
        const defaultCharacteristicsEnum: ProductCharacteristicEnum[] = [
          ProductCharacteristicEnum.PRODUCT_FAMILY,
          ProductCharacteristicEnum.PACKAGING,
        ];

        it('should init select characteristic form group with values', () => {
          const switchRequiredValidationStateSpy = jest
            .spyOn(service, 'switchRequiredValidationState')
            .mockImplementation();

          const fg = service.init(defaultCharacteristicsEnum);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(characteristics.length).toEqual(3);
          expect(characteristics.controls[0].value).toEqual(
            ProductCharacteristicEnum.PRODUCT_FAMILY
          );
          expect(characteristics.controls[1].value).toEqual(
            ProductCharacteristicEnum.PACKAGING
          );
          expect(characteristics.controls[2].value).toBeNull();
          expect(switchRequiredValidationStateSpy).toHaveBeenCalled();
        });
      });

      describe('when method is called with empty characteristics', () => {
        const defaultCharacteristicsEnum: ProductCharacteristicEnum[] = [];

        it('should init select characteristic from group with null values', () => {
          const switchRequiredValidationStateSpy = jest
            .spyOn(service, 'switchRequiredValidationState')
            .mockImplementation();

          const fg = service.init(defaultCharacteristicsEnum);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(characteristics.length).toEqual(3);
          expect(characteristics.controls[0].value).toBeNull();
          expect(characteristics.controls[1].value).toBeNull();
          expect(characteristics.controls[2].value).toBeNull();

          expect(switchRequiredValidationStateSpy).toHaveBeenCalled();
        });
      });

      describe('when method is called with undefined', () => {
        const defaultCharacteristicsEnum = undefined;

        it('should init select characteristic from group with null values', () => {
          const switchRequiredValidationStateSpy = jest
            .spyOn(service, 'switchRequiredValidationState')
            .mockImplementation();

          const fg = service.init(defaultCharacteristicsEnum);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(characteristics.length).toEqual(3);
          expect(characteristics.controls[0].value).toBeNull();
          expect(characteristics.controls[1].value).toBeNull();
          expect(characteristics.controls[2].value).toBeNull();

          expect(switchRequiredValidationStateSpy).toHaveBeenCalled();
        });
      });
    });
  });

  describe('switchRequiredValidationState', () => {
    describe('given any state', () => {
      describe('when method is called with default form group with null values', () => {
        const fb = new FormBuilder();

        const fg = fb.group({
          characteristics: fb.array([
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
          ]),
        });

        it('should add required error', () => {
          service.switchRequiredValidationState(fg);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(
            characteristics.controls[0].hasValidator(Validators.required)
          ).toBeTruthy();

          expect(
            characteristics.controls[1].hasValidator(Validators.required)
          ).toBeTruthy();

          expect(
            characteristics.controls[2].hasValidator(Validators.required)
          ).toBeTruthy();
        });
      });

      describe('when method is called with default form group with at least one value', () => {
        const fb = new FormBuilder();

        const fg = fb.group({
          characteristics: fb.array([
            fb.control<ProductCharacteristicEnum | null>(16, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
          ]),
        });

        it('should not add required error', () => {
          service.switchRequiredValidationState(fg);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(
            characteristics.controls[0].hasValidator(Validators.required)
          ).toBeFalsy();

          expect(
            characteristics.controls[1].hasValidator(Validators.required)
          ).toBeFalsy();

          expect(
            characteristics.controls[2].hasValidator(Validators.required)
          ).toBeFalsy();
        });
      });

      describe('when method is called with FormGroup with the first FormControl value being null and the second being defined', () => {
        const fb = new FormBuilder();

        const fg = fb.group({
          characteristics: fb.array([
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(15, {
              validators: [Validators.required],
            }),
            fb.control<ProductCharacteristicEnum | null>(null, {
              validators: [Validators.required],
            }),
          ]),
        });

        it('should add required validator on the first FormControl', () => {
          service.switchRequiredValidationState(fg);

          const characteristics = fg.get('characteristics') as FormArray;

          expect(
            characteristics.controls[0].hasValidator(Validators.required)
          ).toBeTruthy();

          expect(
            characteristics.controls[1].hasValidator(Validators.required)
          ).toBeFalsy();

          expect(
            characteristics.controls[2].hasValidator(Validators.required)
          ).toBeFalsy();
        });
      });
    });
  });
});
