import { Component } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { CompanyMultiSelectDefaultSelectionMode } from '@gc/company/feature';
import { Company, companiesFixture } from '@gc/company/models';
import { SpectatorHost, createHostFactory } from '@ngneat/spectator/jest';
import { PushPipe } from '@ngrx/component';
import { CompanyMultiSelectComponent } from './company-multi-select.component';
import { Observable, of } from 'rxjs';
import { BaseControlValueAccessorTestHelper } from '@gc/shared/form/utils';
import { CompanyService } from '@gc/company/data-access';

@Component({})
class CustomMultipleHostComponent {
  hostId = 'host-id';
  hostLabel = 'label du Host';
  hostFC: FormControl = new FormControl([], [Validators.required]);
  hostFormGroup = new FormGroup({
    hostFC: this.hostFC,
  });
}

const mockCompanies: Company[] = companiesFixture();

describe('CompanyMultiSelectComponent', () => {
  let spectatorHost: SpectatorHost<
    CompanyMultiSelectComponent,
    CustomMultipleHostComponent
  >;
  let component: CompanyMultiSelectComponent;

  const createHost = createHostFactory({
    component: CompanyMultiSelectComponent,
    host: CustomMultipleHostComponent,
    imports: [
      MatSelectModule,
      MatFormFieldModule,
      MatCheckboxModule,
      ReactiveFormsModule,
      PushPipe,
    ],
    mocks: [CompanyService],
    detectChanges: false,
  });

  beforeEach(() => {
    spectatorHost = createHost(
      `
          <form [formGroup]="hostFormGroup">
              <gc-company-multi-select
                  formControlName="hostFC"
                  [inputId]="hostId"
                  [label]="hostLabel"
                  [invalid]="false"
                  errorText="Ceci est une erreur"
              ></gc-company-multi-select>
          </form>
        `
    );

    ({ component } = spectatorHost);
  });

  describe('ngOnInit', () => {
    let companiesGetterSpy: jest.SpyInstance<Observable<Company[]>>;
    let handleDefaultSelectionSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      BaseControlValueAccessorTestHelper.mockBaseControlValueAccessorNgOnInit<
        string[]
      >(component, []);

      const companyService = spectatorHost.inject(CompanyService);
      companiesGetterSpy = jest.spyOn(companyService, 'companies$', 'get');

      handleDefaultSelectionSpy = jest
        .spyOn(component, 'handleDefaultSelection')
        .mockReturnValue(void 0);
    });

    [
      {
        companies: mockCompanies,
        amountOfCompanyLabel: 'a list with more than one Company',
        shouldSetCompanyOptions: true,
        shouldCallHandleDefaultSelection: true,
      },
      {
        companies: [],
        amountOfCompanyLabel: 'an emtpy list',
        shouldSetCompanyOptions: false,
        shouldCallHandleDefaultSelection: false,
      },
    ].forEach(
      ({
        companies,
        amountOfCompanyLabel,
        shouldSetCompanyOptions,
        shouldCallHandleDefaultSelection,
      }) => {
        describe(`Given a state where companies getter of CompanyService return ${amountOfCompanyLabel}`, () => {
          beforeEach(() => {
            companiesGetterSpy.mockReturnValueOnce(of(companies));
          });

          it(`Should ${
            shouldSetCompanyOptions ? '' : 'not'
          } set companiesOptions property`, () => {
            component.ngOnInit();
            expect(component.companiesOptions).toStrictEqual(
              shouldSetCompanyOptions ? companies : undefined
            );
          });

          it(`Should ${
            shouldCallHandleDefaultSelection ? '' : 'not'
          } call handleDefaultSelection method`, () => {
            component.ngOnInit();
            shouldCallHandleDefaultSelection
              ? expect(handleDefaultSelectionSpy).toHaveBeenCalledWith(
                  companies
                )
              : expect(handleDefaultSelectionSpy).not.toHaveBeenCalled();
          });
        });
      }
    );
  });

  describe('handleDefaultSelection', () => {
    let resetParentSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      resetParentSpy = jest
        .spyOn(component, 'resetParent')
        .mockReturnValue(void 0);
    });
    describe('given any state', () => {
      describe('when method is called with a list of 1 Company', () => {
        const [company] = mockCompanies;

        it("should call resetParent method with a list containing only the Company's id", () => {
          component.handleDefaultSelection([company]);
          expect(resetParentSpy).toHaveBeenCalledWith([company.id]);
        });
      });
    });

    describe('given a state where defaultSelectionMode is ALL', () => {
      beforeEach(() => {
        component.defaultSelectionMode =
          CompanyMultiSelectDefaultSelectionMode.ALL;
      });
      describe('when method is called with a list of Company', () => {
        const companies = mockCompanies;
        it("should call resetParent method with a list containing the Company's ids", () => {
          const expectedIds = companies.map(({ id }) => id);
          component.handleDefaultSelection(companies);
          expect(resetParentSpy).toHaveBeenCalledWith(expectedIds);
        });
      });
    });
  });
});
