import {
  ANY_HTTP_ERROR_RESPONSE,
  ANY_HTTP_REQUEST,
  nextHttpHandlerThrowError,
} from '@gc/shared/tests';
import { ITraceService } from '@isagri-ng/core/diagnostics';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { ITRACE_SERVICES_STUB } from '../test/itrace-service.stub';
import { TraceLogHttpErrorsInterceptor } from './trace-log-http-errors.interceptor';

describe('TraceLogHttpErrorsInterceptor', () => {
  let spectator: SpectatorService<TraceLogHttpErrorsInterceptor>;
  let interceptor: TraceLogHttpErrorsInterceptor;

  const createService = createServiceFactory({
    service: TraceLogHttpErrorsInterceptor,
    providers: [{ provide: ITraceService, useValue: ITRACE_SERVICES_STUB }],
  });

  beforeEach(() => {
    spectator = createService();
    interceptor = spectator.service;
  });

  describe('intercept', () => {
    let logErrorExceptionSpy: jest.SpyInstance<void>;

    beforeEach(() => {
      const traceService = spectator.inject(ITraceService);
      logErrorExceptionSpy = jest.spyOn(traceService, 'logErrorException');
    });
    describe('given a state where handle method of next HttpHandler throw an HttpErrorResponse', () => {
      it('should throw error and call logErrorException method of ITraceService with the error', () => {
        interceptor
          .intercept(
            ANY_HTTP_REQUEST,
            nextHttpHandlerThrowError(ANY_HTTP_ERROR_RESPONSE)
          )
          .subscribe({
            next: () => {
              throw new Error('fail');
            },
            error: (error: unknown) => {
              expect(error).toStrictEqual(ANY_HTTP_ERROR_RESPONSE);
              expect(logErrorExceptionSpy).toHaveBeenCalledWith(
                ANY_HTTP_ERROR_RESPONSE
              );
            },
          });
      });
    });

    describe('given a state where handle method of next HttpHandler throw an error not being an HttpErrorResponse', () => {
      it('should throw error and NOT call logErrorException method of ITraceService', () => {
        interceptor
          .intercept(
            ANY_HTTP_REQUEST,
            nextHttpHandlerThrowError(ANY_HTTP_ERROR_RESPONSE)
          )
          .subscribe({
            next: () => {
              throw new Error('fail');
            },
            error: (error: unknown) => {
              expect(error).toStrictEqual(ANY_HTTP_ERROR_RESPONSE);
              expect(logErrorExceptionSpy).not.toHaveBeenCalled();
            },
          });
      });
    });
  });
});
