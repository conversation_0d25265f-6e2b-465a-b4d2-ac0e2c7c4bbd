import { Title<PERSON><PERSON><PERSON>eader, TitleKeyTab } from '@gc/core/navigation/models';
import { ReportFamilies } from '@gc/shared/stimulsoft/models';

export const STIMULSOFT_REPORT_FAMILY_TO_HEADER_TRANSLATION_KEY_MAP = new Map<
  ReportFamilies,
  TitleKeyHeader
>([
  [ReportFamilies.DEPOSIT_SLIP, TitleKeyHeader.STIMULSOFT_VIEWER_DEPOSIT_SLIP],
  [ReportFamilies.PAYMENT, TitleKeyHeader.STIMULSOFT_VIEWER_PAYMENT],
  [ReportFamilies.SALES, TitleKeyHeader.STIMULSOFT_VIEWER_ACCOUNTING_SALES],
  [ReportFamilies.DETAILS, TitleKeyHeader.STIMULSOFT_VIEWER_ACCOUNTING_DETAILS],
  [
    ReportFamilies.RECEIPTS,
    TitleKeyHeader.STIMULSOFT_VIEWER_ACCOUNTING_RECEIPTS,
  ],
  [ReportFamilies.DEBT, TitleK<PERSON>Header.STIMULSOFT_VIEWER_ACCOUNTING_DEBT],
  [
    ReportFamilies.ACCOUNT_ENTRY,
    TitleKeyHeader.STIMULSOFT_VIEWER_ACCOUNTING_ENTRY,
  ],
]);

export const STIMULSOFT_REPORT_FAMILY_TO_TAB_TRANSLATION_KEY_MAP = new Map<
  ReportFamilies,
  TitleKeyTab
>([
  [ReportFamilies.DEPOSIT_SLIP, TitleKeyTab.STIMULSOFT_VIEWER_DEPOSIT_SLIP],
  [ReportFamilies.PAYMENT, TitleKeyTab.STIMULSOFT_VIEWER_PAYMENT],
  [ReportFamilies.SALES, TitleKeyTab.STIMULSOFT_VIEWER_ACCOUNTING_SALES],
  [ReportFamilies.DETAILS, TitleKeyTab.STIMULSOFT_VIEWER_ACCOUNTING_DETAILS],
  [ReportFamilies.RECEIPTS, TitleKeyTab.STIMULSOFT_VIEWER_ACCOUNTING_RECEIPTS],
  [ReportFamilies.DEBT, TitleKeyTab.STIMULSOFT_VIEWER_ACCOUNTING_DEBT],
  [
    ReportFamilies.ACCOUNT_ENTRY,
    TitleKeyTab.STIMULSOFT_VIEWER_ACCOUNTING_ENTRY,
  ],
]);
