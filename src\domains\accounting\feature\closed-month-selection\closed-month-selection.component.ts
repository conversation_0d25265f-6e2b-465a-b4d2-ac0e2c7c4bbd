import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import {
  monthlyTreatmentActions,
  monthlyTreatmentHistoryActions,
  MonthlyTreatmentHistoryStoreModule,
  MonthlyTreatmentStoreModule,
  selectEnclosedMonthsOfSelectedYear,
  selectEnclosedYears,
  selectMonthlyTreatmentCompanyId,
  selectSelectedEnclosedMonth,
  selectSelectedEnclosedYear,
} from '@gc/accounting/data-access';
import { CompanySingleSelectComponent } from '@gc/company/feature';
import { TRANSLOCO_SCOPE, TranslocoModule } from '@jsverse/transloco';
import { LetDirective, PushPipe } from '@ngrx/component';
import { Store } from '@ngrx/store';
import { EMPTY, filter, map, Observable, switchMap, tap } from 'rxjs';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import {
  CapitalizePipe,
  SortDatesDscPipe,
  SortNumbersDscPipe,
} from '@gc/shared/ui';
import { CompanyService } from '@gc/company/data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'gc-closed-month-selection',
  standalone: true,
  templateUrl: './closed-month-selection.component.html',
  styleUrls: [
    './closed-month-selection.component.scss',
    './closed-month-selection-theme.component.scss',
  ],
  imports: [
    TranslocoModule,
    CompanySingleSelectComponent,
    ReactiveFormsModule,
    MonthlyTreatmentStoreModule,
    PushPipe,
    MonthlyTreatmentHistoryStoreModule,
    MatSelectModule,
    MatFormFieldModule,
    MatRadioModule,
    TranslocoDatePipe,
    CapitalizePipe,
    LetDirective,
    SortNumbersDscPipe,
    SortDatesDscPipe,
  ],
  providers: [
    {
      provide: TRANSLOCO_SCOPE,
      useValue: 'shared/form',
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClosedMonthSelectionComponent implements OnInit {
  private readonly _companyService = inject(CompanyService);
  private readonly _store = inject(Store);
  private readonly _destroyRef = inject(DestroyRef);

  selectCompanyFC?: FormControl<string>;

  enclosedYears$ = this._store.select(selectEnclosedYears);
  enclosedMonths$ = this._store.select(selectEnclosedMonthsOfSelectedYear);

  selectedEnclosedYear$ = this._store.select(selectSelectedEnclosedYear);
  selectedEnclosedMonth$ = this._store.select(selectSelectedEnclosedMonth);

  ngOnInit(): void {
    this.handleSelectedCompanyId();
    this.handleYearsRetrieval();
    this.handleMonthsRetrieval();
  }

  handleSelectedCompanyId(): void {
    this._companyService.companyIdIfSingleCompany$
      .pipe(
        switchMap((companyId?: string) =>
          companyId ? EMPTY : this._initSelectCompanyFormControl()
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  handleYearsRetrieval(): void {
    this._store
      .select(selectMonthlyTreatmentCompanyId)
      .pipe(
        filter(Boolean),
        tap(() =>
          this._store.dispatch(
            monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears()
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  handleMonthsRetrieval(): void {
    this._store
      .select(selectSelectedEnclosedYear)
      .pipe(
        filter(Boolean),
        tap(() =>
          this._store.dispatch(
            monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonths()
          )
        ),
        takeUntilDestroyed(this._destroyRef)
      )
      .subscribe();
  }

  selectedYearChange(year: number): void {
    this._store.dispatch(
      monthlyTreatmentHistoryActions.changeSelectedEnclosedYear({ year })
    );
  }

  selectedMonthChange(month: Date): void {
    this._store.dispatch(
      monthlyTreatmentHistoryActions.changeSelectedEnclosedMonth({
        month,
      })
    );
  }

  private _initSelectCompanyFormControl(): Observable<void> {
    return this._store.select(selectMonthlyTreatmentCompanyId).pipe(
      tap((companyId?: string) => {
        this.selectCompanyFC = new FormControl<string>(companyId ?? '', {
          nonNullable: true,
          validators: [Validators.required],
        });
      }),

      switchMap(() => {
        return this.selectCompanyFC!.valueChanges.pipe(
          tap((companyId) =>
            this._store.dispatch(
              monthlyTreatmentActions.changeCompanyId({
                companyId,
              })
            )
          )
        );
      }),
      map(() => void 0)
    );
  }
}
