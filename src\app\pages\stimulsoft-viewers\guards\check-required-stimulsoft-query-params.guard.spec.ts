import { TestBed } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterModule,
  RouterStateSnapshot,
} from '@angular/router';
import { checkRequiredStimulsoftQueryParams } from './check-required-stimulsoft-query-params.guard';
import { URL_PATHS } from '@gc/core/navigation/models';

describe('checkRequiredStimulsoftQueryParams function', () => {
  let router: Router;
  let navigateSpy: jest.SpyInstance<Promise<boolean>>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RouterModule],
    });

    router = TestBed.inject(Router);
    navigateSpy = jest.spyOn(router, 'navigate').mockResolvedValue(true);
  });

  [
    {
      reportFamily: undefined,
      reportId: undefined,
    },
    {
      reportFamily: 'aReportFamily',
      reportId: 'aReportId',
    },
    {
      reportFamily: 'aReportFamily',
      reportId: undefined,
    },
    {
      reportFamily: undefined,
      reportId: 'aReportId',
    },
  ].forEach(({ reportFamily, reportId }) => {
    describe(`given a state where url has the following queryParams : reportFamily = ${reportFamily} and reportId = ${reportId}`, () => {
      let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
      beforeEach(() => {
        activatedRouteSnapshot = {
          queryParams: {
            reportId,
            reportFamily,
          },
        };
      });

      it('should navigate to invalid-config url and throw error', () => {
        TestBed.runInInjectionContext(() => {
          try {
            checkRequiredStimulsoftQueryParams(
              activatedRouteSnapshot as ActivatedRouteSnapshot,
              {} as RouterStateSnapshot
            );
          } catch (err) {
            expect(err).toStrictEqual(
              new Error(
                'checkRequiredStimulsoftQueryParams: reportFamily or reportId is undefined'
              )
            );
            expect(navigateSpy).toHaveBeenCalledWith([URL_PATHS.invalidConfig]);
          }
        });
      });
    });
  });

  describe(`given a state where all the following queryParams are defined in ActivatedRouteSnapshot : reportFamily and reportId`, () => {
    let activatedRouteSnapshot: Partial<ActivatedRouteSnapshot>;
    beforeEach(() => {
      activatedRouteSnapshot = {
        queryParams: {
          reportId: 'aReportId',
          reportFamily: 'reportFamily',
        },
      };
    });

    it('should return true and not call navigate method of Router', () => {
      const canActivate = TestBed.runInInjectionContext(() => {
        return checkRequiredStimulsoftQueryParams(
          activatedRouteSnapshot as ActivatedRouteSnapshot,
          {} as RouterStateSnapshot
        );
      });

      expect(navigateSpy).not.toHaveBeenCalled();
      expect(canActivate).toBe(true);
    });
  });
});
