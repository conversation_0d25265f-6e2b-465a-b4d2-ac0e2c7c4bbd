import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, filter, map, switchMap } from 'rxjs/operators';
import { MonthlyTreatmentService } from '../../../services/monthly-treatment.service';
import { monthlyTreatmentHistoryActions } from '../actions/monthly-treatment-history.actions';
import { selectMonthlyTreatmentCompanyId } from '../../monthly-treatment/selectors/monthly-treatment.selectors';
import {
  selectSelectedEnclosedMonth,
  selectSelectedEnclosedYear,
} from '../selectors/monthly-treatment-history.selectors';
import { of } from 'rxjs';
import { MonthlyTreatmentAvailableEdition } from '@gc/accounting/models';

@Injectable()
export class MonthlyTreatmentHistoryEffects {
  private readonly _actions$ = inject(Actions);
  private readonly _monthlyTreatmentService = inject(MonthlyTreatmentService);
  private readonly _store = inject(Store);

  loadSelectedCompanyEnclosedYears$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears),
      concatLatestFrom(() =>
        this._store.select(selectMonthlyTreatmentCompanyId)
      ),
      filter(([_, companyId]) => !!companyId),
      switchMap(([_, companyId]) => {
        return this._monthlyTreatmentService.getCompanyEnclosedYears(
          companyId!
        );
      }),
      map((years: number[]) => {
        return monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYearsSuccess(
          { years }
        );
      })
    );
  });

  loadSelectedYearEnclosedMonths$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonths),
      concatLatestFrom(() => [
        this._store.select(selectMonthlyTreatmentCompanyId),
        this._store.select(selectSelectedEnclosedYear),
      ]),
      filter(
        ([_, companyId, selectedEnclosedYear]) =>
          !!companyId && !!selectedEnclosedYear
      ),
      switchMap(([_, companyId, selectedEnclosedYear]) => {
        return this._monthlyTreatmentService.getCompanyYearEnclosedMonths(
          companyId!,
          selectedEnclosedYear!
        );
      }),
      map((months: Date[]) => {
        return monthlyTreatmentHistoryActions.loadSelectedYearEnclosedMonthsSuccess(
          { months }
        );
      })
    );
  });

  loadSelectedMonthAvailableEditions$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditions),
      concatLatestFrom(() => [
        this._store.select(selectMonthlyTreatmentCompanyId),
        this._store.select(selectSelectedEnclosedMonth),
      ]),
      filter(
        ([_, companyId, selectedEnclosedMonth]) =>
          !!companyId && !!selectedEnclosedMonth
      ),
      switchMap(([_, companyId, selectedEnclosedMonth]) => {
        return this._monthlyTreatmentService.getAvailableEditions(
          companyId!,
          selectedEnclosedMonth!
        );
      }),
      map((availableMonthlyEditions: MonthlyTreatmentAvailableEdition[]) => {
        return monthlyTreatmentHistoryActions.loadSelectedMonthAvailableEditionsSuccess(
          { availableMonthlyEditions }
        );
      })
    );
  });

  uncloseSelectedMonth$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentHistoryActions.uncloseSelectedMonth),
      concatLatestFrom(() => [
        this._store.select(selectSelectedEnclosedMonth),
        this._store.select(selectMonthlyTreatmentCompanyId),
      ]),
      switchMap(([_, selectedEnclosedMonth, companyId]) => {
        return this._monthlyTreatmentService.uncloseMonth({
          month: selectedEnclosedMonth!,
          companyId: companyId!,
        });
      }),
      map(() => monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess()),
      catchError(() =>
        of(monthlyTreatmentHistoryActions.uncloseSelectedMonthError())
      )
    );
  });

  uncloseSelectedMonthSuccess$ = createEffect(() => {
    return this._actions$.pipe(
      ofType(monthlyTreatmentHistoryActions.uncloseSelectedMonthSuccess),
      map(() =>
        monthlyTreatmentHistoryActions.loadSelectedCompanyEnclosedYears()
      )
    );
  });
}
