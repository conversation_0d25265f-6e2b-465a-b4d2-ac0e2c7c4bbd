import { of, throwError } from 'rxjs';
import {
  handleStoreLoading,
  HandleStoreLoadingOptions,
} from './handle-store-loading.operator';
import { BaseStore } from '../base.store';
import { ResourceState } from '@gc/core/shared/store';
import { HttpErrorResponse } from '@angular/common/http';

enum TestEnum {
  Data = 'Data',
}

interface TestState {
  [TestEnum.Data]: ResourceState<string>;
}

describe('handleStoreLoading', () => {
  let store: BaseStore<typeof TestEnum, TestState>;

  beforeEach(() => {
    store = new (class extends BaseStore<typeof TestEnum, TestState> {
      constructor() {
        super(TestEnum);
      }
    })();
    jest.spyOn(store, 'update');
  });

  it('should update store with success state when observable emits', (done) => {
    const testData = 'test data';
    const source$ = of(testData);

    source$.pipe(handleStoreLoading(store, TestEnum.Data)).subscribe({
      next: (result) => {
        expect(result).toBe(testData);
        expect(store.update).toHaveBeenCalledWith(TestEnum.Data, {
          data: testData,
          isLoading: false,
          status: 'Success',
        });
        done();
      },
    });
  });

  it('should return the error message when observable throws', (done) => {
    const error = new HttpErrorResponse({ error: new Error('test error') });
    const source$ = throwError(() => error);

    source$.pipe(handleStoreLoading(store, TestEnum.Data)).subscribe({
      error: (e) => {
        expect(e).toBeInstanceOf(HttpErrorResponse);
        expect(e.error.message).toBe('test error');
        done();
      },
    });
  });

  it('should update store with error state when observable throws', (done) => {
    const httpError = new HttpErrorResponse({
      error: { errors: [{ code: '123', message: 'test error' }] },
    });
    const source$ = throwError(() => httpError);

    source$.pipe(handleStoreLoading(store, TestEnum.Data)).subscribe({
      error: (e) => {
        expect(e).toBeInstanceOf(HttpErrorResponse);
        expect(store.update).toHaveBeenCalledWith(TestEnum.Data, {
          data: undefined,
          isLoading: false,
          status: 'Error',
          errors: [
            {
              code: '123',
              message: 'test error',
            },
          ],
        });
        done();
      },
    });
  });

  it('should pass through the emitted value', (done) => {
    const testData = 'test data';
    const source$ = of(testData);

    source$.pipe(handleStoreLoading(store, TestEnum.Data)).subscribe({
      next: (result) => {
        expect(result).toBe(testData);
        done();
      },
    });
  });

  it('should not complete after first emission when completeOnFirstEmission is false', (done) => {
    const testData1 = 'test data 1';
    const testData2 = 'test data 2';
    // Create an observable that emits multiple values
    const source$ = of(testData1, testData2);
    const options: HandleStoreLoadingOptions = {
      completeOnFirstEmission: false,
    };

    const values: string[] = [];

    source$.pipe(handleStoreLoading(store, TestEnum.Data, options)).subscribe({
      next: (result) => {
        values.push(result);
        if (values.length === 2) {
          expect(values).toEqual([testData1, testData2]);
          expect(store.update).toHaveBeenCalledTimes(2);
          done();
        }
      },
      complete: () => {
        // This should be called after both values are emitted
        expect(values.length).toBe(2);
      },
    });
  });
});
