import { inject, Injectable } from '@angular/core';
import {
  GetParametersPort,
  SaveParametersPort,
} from '@gc/core/deb/domains/ports';
import { DebParameters } from '@gc/core/deb/domains/models';

@Injectable()
export class ParametersUseCase {
  private readonly getParameters = inject(GetParametersPort);
  private readonly saveParameters = inject(SaveParametersPort);

  getFor(companyId: string) {
    return this.getParameters.for(companyId);
  }

  saveFor(parameters: DebParameters, companyId: string) {
    return this.saveParameters.for(parameters, companyId);
  }
}
