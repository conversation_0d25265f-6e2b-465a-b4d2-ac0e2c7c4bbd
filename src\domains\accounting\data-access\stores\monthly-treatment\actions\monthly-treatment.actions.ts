import {
  EnclosingMonthInformations,
  MonthlyTreatmentEditionsFiltersModel,
} from '@gc/accounting/models';
import { ProductCharacteristicEnum } from '@gc/product/models';
import { createActionGroup, emptyProps, props } from '@ngrx/store';

export const monthlyTreatmentActions = createActionGroup({
  source: 'Monthly Treatment',
  events: {
    'Change company id': props<{ companyId: string }>(),
    'Change date range': props<{ startDate: Date; endDate: Date }>(),
    'Load default filters': emptyProps(),
    'Load default filters success': props<{
      filters: MonthlyTreatmentEditionsFiltersModel | undefined;
    }>(),
    'Load default filters error': emptyProps(),
    'Change sales by product current filter': props<{
      filter: (ProductCharacteristicEnum | null)[] | undefined;
    }>(),
    'Change sales details current filter': props<{
      filter: boolean;
    }>(),
    'Enclose default month': props<{
      defaultMonth: Date;
    }>(),
    'Enclose default month success': props<{
      defaultMonth: Date;
    }>(),
    'Enclose default month error': emptyProps(),
    'Load enclosure month': props<{ companyId: string }>(),
    'Load enclosure month success': props<{
      enclosingMonthInformations: EnclosingMonthInformations | null;
    }>(),
    'Load enclosure month error': emptyProps(),
    'Enclose month': emptyProps(),
    'Enclose month success': emptyProps(),
    'Enclose month error': emptyProps(),
    'Reset enclosing status': emptyProps(),
    'Load has empty edition': emptyProps(),
    'Load has empty editions success': props<{
      isEmpty: boolean | undefined;
    }>(),
    'Load has empty editions error': emptyProps(),
    'Reset empty editions': emptyProps(),
    'Set range month back': emptyProps(),
  },
});
