import { DunningLetterApiAdapter } from '../adapters-api/dunning-letter-api.adapter';
import { BusinessResultWarningMessageApiAdapter } from '../adapters-api/business-result-warning-message-api.adapter';
import { BusinessResultErrorMessageApiAdapter } from '../adapters-api/business-result-error-message-api.adapter';
import { DunningLetterBusinessResult } from '@gc/dunning/models';
import { DunningLetterBusinessResultAPIApi } from '@gc/shared/api/data-access';

export class DunningLetterBusinessResultAdapter {
  static fromApi(
    response: DunningLetterBusinessResultAPIApi
  ): DunningLetterBusinessResult {
    if (!response) {
      throw new Error(
        '[DunningLetterBusinessResultAdapter] reponse should not be null'
      );
    }

    if (!response.warnings) {
      throw new Error(
        '[DunningLetterBusinessResultAdapter] warnings should not be null'
      );
    }

    if (!response.errors) {
      throw new Error(
        '[DunningLetterBusinessResultAdapter] errors should not be null'
      );
    }

    return {
      result: response?.result
        ? DunningLetterApiAdapter.fromApi(response.result)
        : undefined,
      warnings: BusinessResultWarningMessageApiAdapter.fromApi(
        response.warnings
      ),
      errors: BusinessResultErrorMessageApiAdapter.fromApi(response.errors),
    };
  }
}
