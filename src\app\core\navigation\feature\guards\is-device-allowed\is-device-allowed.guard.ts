import { inject } from '@angular/core';
import { CanMatchFn, Router } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import { DeviceAccessService } from '../../services/device-access.service';

export const isDeviceAllowed = (): CanMatchFn => (route, _) => {
  const router: Router = inject(Router);
  const deviceFlagService: DeviceAccessService = inject(DeviceAccessService);
  const deviceTypes: [] = route.data?.['allowedDevices'];

  return deviceFlagService.allowedFor(deviceTypes)
    ? true
    : router.parseUrl(`/${URL_PATHS.unavailableDevice}`);
};
