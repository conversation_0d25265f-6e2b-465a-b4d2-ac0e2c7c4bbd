import { LoadDetailsPort } from '@gc/core/deb/domains/ports';
import {
  DebDetails,
  DebLineDetailsDocumentEnum,
  LoadDetailsParameters,
} from '../../domains/models';
import { inject, Injectable } from '@angular/core';
import { DebApiService } from '@gc/core/deb/infrastructure/api';
import { GetDebLineDetailsParamsRequest } from '@gc/core/deb/infrastructure/api/request';
import { DebLineDetailsResponse } from '@gc/core/deb/infrastructure/api/response';
import { Observable, map } from 'rxjs';

const documentTypeMap: { [key: string]: DebLineDetailsDocumentEnum } = {
  Invoice: DebLineDetailsDocumentEnum.INVOICE,
  CreditNote: DebLineDetailsDocumentEnum.CREDIT_NOTE,
  DeliveryNote: DebLineDetailsDocumentEnum.DELIVERY_NOTE,
  ReturnNote: DebLineDetailsDocumentEnum.RETURN_NOTE,
};

@Injectable()
export class LoadDetailsAdapter implements LoadDetailsPort {
  private readonly api = inject(DebApiService);

  for(parameters: LoadDetailsParameters): Observable<DebDetails[]> {
    const request: GetDebLineDetailsParamsRequest = {
      companyId: parameters.companyId,
      month: parameters.month,
      invoicesIncluded: parameters.invoicesIncluded,
      deliveryNotesIncluded: parameters.deliveryNotesIncluded,
      destinationCountryCode: parameters.destinationCountryCode,
      statisticalProcedureCode: parameters.statisticalProcedureCode,
      euNomenclature: parameters.euNomenclature || '',
      vatNumber: parameters.vatNumber,
    };

    return this.api
      .getLineDetails(request)
      .pipe(
        map((debDetailsResponse) =>
          debDetailsResponse.map((item) => this.mapToDomain(item))
        )
      );
  }

  private mapToDomain(response: DebLineDetailsResponse): DebDetails {
    return {
      documentType: this.mapDocumentType(response.documentType),
      documentNumber: response.documentNumber,
      customerCode: response.customerCode,
      deliveryDate: response.deliveryDate,
      productCode: response.productCode,
      productLabel: response.productLabel,
      lineAmount: response.lineAmount,
    };
  }

  private mapDocumentType(documentType: string): DebLineDetailsDocumentEnum {
    return documentTypeMap[documentType];
  }
}
