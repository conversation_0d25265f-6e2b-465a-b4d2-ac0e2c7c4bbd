<mat-card class="h-full" *transloco="let t">
  <form
    class="form-container h-full"
    [formGroup]="debFiltersFG"
    (ngSubmit)="applyFiltersSubmit()">
    <mat-card-header>
      <mat-card-title>
        {{ t('deb.statement-page.filters.title') }}
      </mat-card-title>
    </mat-card-header>
    <mat-card-content class="deb-filters-container">
      <div>
        <p>
          <strong>{{ t('deb.statement-page.filters.documentsType') }}</strong>
        </p>
        <div class="checkbox-item">
          <mat-checkbox formControlName="deliveryNoteRMAFC" color="primary">
            {{ t('deb.statement-page.filters.deliveryNoteRMA') }}
          </mat-checkbox>
        </div>
        <div class="checkbox-item">
          <mat-checkbox formControlName="invoicesCreditNotesFC" color="primary">
            {{ t('deb.statement-page.filters.invoicesCreditNotes') }}
          </mat-checkbox>
        </div>
      </div>
      <div>
        <p>
          <strong>{{
            t('deb.statement-page.filters.declarationMonth')
          }}</strong>
        </p>
        <div class="month-item">
          <p>
            {{
              declarationMonth()
                | translocoDate: { year: 'numeric', month: 'long' }
                | capitalize
            }}
          </p>
        </div>
      </div>
      <mat-card-actions class="actions-aligned-end">
        <button mat-raised-button color="primary">
          {{ t('deb.statement-page.filters.actions.applyFilters') }}
        </button>
      </mat-card-actions>
    </mat-card-content>
  </form>
</mat-card>
