import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { RightsService } from './rights.service';
import {
  LicenseDTOApi,
  LicensesApiService,
  LicenseTypeApi,
} from '@gc/shared/api/data-access';
import {
  BehaviorSubject,
  Observable,
  of,
  skip,
  take,
  tap,
  throwError,
} from 'rxjs';
import { LoadingStatus } from '@gc/shared/models';

describe('RightsService', () => {
  let spectator: SpectatorService<RightsService>;
  let service: RightsService;

  const createService = createServiceFactory({
    service: RightsService,
    mocks: [LicensesApiService],
  });

  beforeEach(() => {
    spectator = createService();
    ({ service } = spectator);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('loadIsViti', () => {
    let licensesApiServiceSpy: jest.SpyInstance<Observable<LicenseDTOApi>>;

    beforeEach(() => {
      const licensesApiService = spectator.inject(LicensesApiService);
      licensesApiServiceSpy = jest.spyOn(
        licensesApiService,
        'getLicense'
      ) as unknown as jest.SpyInstance<Observable<LicenseDTOApi>>;
    });

    describe('given a state where LicensesApiService getLicense method return LicenseTypeApi set to Vigne', () => {
      beforeEach(() => {
        licensesApiServiceSpy.mockReturnValueOnce(
          of({ licenseType: LicenseTypeApi.Vigne })
        );
      });
      it('should push true to the _isViti$$ subject', async () => {
        await new Promise<void>((done) => {
          const firstValueOfBehaviorSubject = 1;
          ((service as any)._isViti$$ as BehaviorSubject<boolean>)
            .pipe(skip(firstValueOfBehaviorSubject))
            .subscribe((isViti: boolean) => {
              expect(licensesApiServiceSpy).toHaveBeenCalled();
              expect(isViti).toBe(true);

              done();
            });
          (service as any)._loadIsViti();
        }).then();
      });

      it('should change status of _isVitiLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to LOADED', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._isVitiLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'LOADED',
                ]);
                done();
              },
            });

          (service as any)._loadIsViti();
        }).then();
      });
    });

    describe('given a state where LicensesApiService getLicense method return LicenseTypeApi set to Fact', () => {
      beforeEach(() => {
        licensesApiServiceSpy.mockReturnValueOnce(
          of({ licenseType: LicenseTypeApi.Fact })
        );
      });
      it('should push false to the _isViti$$ subject', async () => {
        await new Promise<void>((done) => {
          const firstValueOfBehaviorSubject = 1;
          ((service as any)._isViti$$ as BehaviorSubject<boolean>)
            .pipe(skip(firstValueOfBehaviorSubject))
            .subscribe((isViti: boolean) => {
              expect(licensesApiServiceSpy).toHaveBeenCalled();
              expect(isViti).toBe(false);

              done();
            });
          (service as any)._loadIsViti();
        }).then();
      });

      it('should change status of _isVitiLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to LOADED', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._isVitiLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'LOADED',
                ]);
                done();
              },
            });

          (service as any)._loadIsViti();
        }).then();
      });
    });

    describe('given a state where LicensesApiService getLicense method throw an error', () => {
      beforeEach(() => {
        licensesApiServiceSpy.mockImplementation(() =>
          throwError(() => new Error('Error when retrieving commercial module'))
        );
      });

      it('should not push a value in _isViti$$ subject and change status of _isVitiLoadingStatus$$ from NOT_LOADED, to IN_PROGRESS to ERROR', async () => {
        await new Promise<void>((done) => {
          const emittedStatus: LoadingStatus[] = [];

          (
            (service as any)
              ._isVitiLoadingStatus$$ as BehaviorSubject<LoadingStatus>
          )
            .pipe(
              take(3),
              tap((status) => emittedStatus.push(status))
            )
            .subscribe({
              complete: () => {
                expect(emittedStatus).toEqual([
                  'NOT_LOADED',
                  'IN_PROGRESS',
                  'ERROR',
                ]);

                const isVitiSnapshot = (
                  (service as any)._isViti$$ as BehaviorSubject<boolean | null>
                ).value;

                expect(isVitiSnapshot).toBeNull();
                done();
              },
            });

          (service as any)._loadIsViti();
        }).then();
      });
    });
  });
});
