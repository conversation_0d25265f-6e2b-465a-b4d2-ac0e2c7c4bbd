import { Injectable } from '@angular/core';
import { BaseStore, ResourceState } from '@gc/core/shared/store';

export enum SharedUserStoreEnum {
  SELECTED_COMPANY = 'SELECTED_COMPANY',
}

export interface SharedUserState {
  [SharedUserStoreEnum.SELECTED_COMPANY]: ResourceState<string>;
}

@Injectable({
  providedIn: 'root',
})
export class SharedUserStore extends BaseStore<
  typeof SharedUserStoreEnum,
  SharedUserState
> {
  constructor() {
    super(SharedUserStoreEnum);
  }
}
