import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MockComponents, MockDirectives } from 'ng-mocks';
import { DebHistoryDetailsComponent } from './deb-history-details.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { DebFacade } from '@gc/core/deb/application/facades';
import { MatTable } from '@angular/material/table';
import { ConfirmActionComponent, SnackbarService } from '@gc/shared/ui';
import { signal } from '@angular/core';
import { ResourceState } from '@gc/core/shared/store';
import { ClosedMonth } from '@gc/core/deb/domains/models';
import { HttpClient } from '@angular/common/http';
import { ShowDeclarationNumberPipe } from './pipes/show-declaration-number.pipe';
import { TranslocoDatePipe } from '@jsverse/transloco-locale';
import { CapitalizePipe, LoaderComponent } from '@gc/shared/ui';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

describe('DebHistoryDetailsComponent', () => {
  let spectator: Spectator<DebHistoryDetailsComponent>;
  let component: DebHistoryDetailsComponent;
  let debFacade: DebFacade;

  const mockClosedMonths: ClosedMonth[] = [
    {
      declarationMonth: new Date('2023-01-01'),
      declarationNumber: 1,
    },
    {
      declarationMonth: new Date('2023-02-01'),
      declarationNumber: 2,
    },
  ];

  const mockClosedMonthsSignal = signal<ResourceState<ClosedMonth[]>>({
    isLoading: false,
    data: mockClosedMonths,
    status: 'Success',
    errors: undefined,
  });

  const mockUncloseMonthSignal = signal<ResourceState<void>>({
    isLoading: false,
    status: 'Success',
    errors: undefined,
  });

  const mockDebFacade = {
    getClosedMonthsForCurrentCompany: jest
      .fn()
      .mockReturnValue(mockClosedMonthsSignal),
    getUncloseMonth: jest.fn().mockReturnValue(mockUncloseMonthSignal),
    uncloseMonthFor: jest.fn(),
    acknowledgeUncloseMonth: jest.fn(),
  };

  const createComponent = createComponentFactory({
    component: DebHistoryDetailsComponent,
    imports: [MatButtonModule, MatCardModule, MatIconModule, MatTooltipModule],
    declarations: [
      MockDirectives(TranslocoDirective),
      MockComponents(MatTable, ConfirmActionComponent, LoaderComponent),
      ShowDeclarationNumberPipe,
      TranslocoDatePipe,
      CapitalizePipe,
    ],
    mocks: [HttpClient, SnackbarService],
    providers: [
      {
        provide: DebFacade,
        useValue: mockDebFacade,
      },
    ],
    detectChanges: false,
  });

  beforeEach(() => {
    spectator = createComponent();
    component = spectator.component;
    debFacade = spectator.inject(DebFacade);

    Object.defineProperty(component, 'companyId', {
      value: () => '123456',
    });

    component.table = {
      renderRows: jest.fn(),
    } as unknown as MatTable<ClosedMonth>;
  });

  it('should create', () => {
    spectator.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should create a component with a table', () => {
      expect(component.table).toBeDefined();
    });
  });

  describe('handleUncloseActions', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should hide action buttons after timeout', () => {
      component.displayUncloseActionButtons = true;

      component.handleUncloseActions(false, mockClosedMonths[0]);
      jest.runAllTimers();

      expect(component.displayUncloseActionButtons).toBe(false);
    });

    it('should not call uncloseMonthFor when confirm is false', () => {
      component.handleUncloseActions(false, mockClosedMonths[0]);

      expect(debFacade.uncloseMonthFor).not.toHaveBeenCalled();
    });
  });

  describe('uncloseMonthState', () => {
    it('should call acknowledgeUncloseMonth when uncloseMonthState has Success status', () => {
      jest.clearAllMocks();
      const successState: ResourceState<void> = {
        isLoading: false,
        status: 'Success',
        errors: undefined,
      };

      if (successState.status === 'Success') {
        mockDebFacade.acknowledgeUncloseMonth();
      }

      expect(mockDebFacade.acknowledgeUncloseMonth).toHaveBeenCalled();
    });

    it('should not call acknowledgeUncloseMonth when uncloseMonthState not have error status', () => {
      jest.clearAllMocks();

      const errorState: ResourceState<void> = {
        isLoading: false,
        status: 'Error',
        errors: [{ code: 'ERR-001', message: 'Some error' }],
      };

      if (errorState.status === 'Success') {
        mockDebFacade.acknowledgeUncloseMonth();
      }

      expect(mockDebFacade.acknowledgeUncloseMonth).not.toHaveBeenCalled();
    });
  });
});
