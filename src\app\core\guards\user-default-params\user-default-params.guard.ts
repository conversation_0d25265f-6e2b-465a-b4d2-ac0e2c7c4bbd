import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, Params, Router } from '@angular/router';
import { URL_PATHS } from '@gc/core/navigation/models';
import {
  UserLocalStorageService,
  UserNavigationService,
  UserStoreService,
} from '@gc/user/data-access';
import { User } from '@gc/user/models';

@Injectable({
  providedIn: 'root',
})
export class UserDefaultParamsGuard {
  private readonly _userNavigationService = inject(UserNavigationService);
  private readonly _userLocalStorageService = inject(UserLocalStorageService);
  private readonly _userStoreService = inject(UserStoreService);
  private readonly _router = inject(Router);

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const user = this._getUser(route.queryParams);

    if (user) this._saveUser(user);
    else this._router.navigate([URL_PATHS.invalidConfig]);

    return !!user;
  }

  private _getUser(queryParams: Params): User | undefined {
    return (
      this._userNavigationService.getUserDefaultDataFromQueryParams(
        queryParams
      ) ?? this._userLocalStorageService.get()
    );
  }

  private _saveUser(user: User): void {
    this._userLocalStorageService.set(user);
    this._userStoreService.updateUser(user);
  }
}
