import {
  DueDateToSaveApi,
  DunningsDataToSaveApi,
} from '@gc/shared/api/data-access';
import { DunningsToSave, DunningDueDateToSave } from '@gc/dunning/models';
import { DunningLevelAdapter } from '../adapters/dunning-level.adapter';
import { DateAdapter } from '@gc/shared/utils';

export class DunningToSaveAdapter {
  public static toApi(dunningsToSave: DunningsToSave): DunningsDataToSaveApi {
    return {
      dueDates: dunningsToSave.dueDates.map(
        (dunningToSave: DunningDueDateToSave) =>
          ({
            dueDateId: dunningToSave.id,
            invoicesDueUpTo: DateAdapter.dateToStringAPI(
              dunningToSave.invoicesDueUpTo
            ),
            dunningLevel: DunningLevelAdapter.toApi(dunningToSave.level),
          }) as DueDateToSaveApi
      ),
    } as unknown as DunningsDataToSaveApi;
  }
}
