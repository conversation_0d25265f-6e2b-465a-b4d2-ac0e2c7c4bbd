<ng-container *transloco="let t">
  <gc-main-container>
    <div class="main-container">
      <form [formGroup]="formGroup" *gcIsSingleCompany>
        <div class="companies-select-container" [hidden]="true">
          <gc-company-multi-select
            formControlName="companiesFC"
            [defaultSelectionMode]="companySelectDefaultSelectionMode"
            [inputId]="'companies-select'"
            [label]="t('company.companies-select.label')"
            [selectAllLabel]="t('company.companies-select.selectAll')"
            [invalid]="
              companiesFC.invalid && (companiesFC.dirty || companiesFC.touched)
            "
            [errorText]="t('sharedForm.error.required')" />
        </div>
      </form>

      @if (filteredReports$ | ngrxPush; as reports) {
        <div class="category-and-search-container">
          <gc-report-category-chip-autocomplete
            [formControl]="categoryFilterCtrl" />
          <gc-search-bar
            [hidden]="customReportsTab.isActive"
            [list]="filteredReportsByCategory$ | async"
            (search)="onSearch($event)" />
        </div>
        <div class="tab-group-container">
          <mat-tab-group
            class="tab-group large-margin-under-tabs-header"
            mat-stretch-tabs="false"
            mat-align-tabs="center">
            <mat-tab
              label="{{ t('reports.page.navigation-tab.all') | uppercase }} ">
              <gc-report-cards-viewer
                [reports]="reports"
                (selectedReport)="openStimulsoftReport($event)" />
            </mat-tab>
            <mat-tab
              #customReportsTab
              label="{{
                t('reports.page.navigation-tab.personals', {
                  count: customReportsCount$ | ngrxPush,
                }) | uppercase
              }} ">
              <gc-custom-report-cards
                (selectedReport)="openStimulsoftReport($event)" />
            </mat-tab>
          </mat-tab-group>
          <div class="toggle-btn-container">
            <gc-toggle-button />
          </div>
        </div>
      }
    </div>
  </gc-main-container>
</ng-container>
