:host {
  margin-top: 16px;
}

.custom-icon-color {
  color: #69dfc4;
}

.description-container {
  margin: 0 25px;

  .description-info {
    display: flex;
    gap: 5px;
    font-size: 15px;
    margin-bottom: 20px;
  }

  .description-data {
    display: flex;
    flex-direction: column;
    font-size: 15px;

    p {
      margin-bottom: 4px;
    }

    .value {
      font-weight: bold;
    }
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 0 1rem;
  margin-top: 5px;
  justify-content: center;
}

.action-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
  gap: 10px;
}

.error {
  display: flex;
  justify-content: center;
  width: 100%;
}

.form-inputs-container {
  display: flex;
  gap: 10px;
}
